const fs = require('fs');

try {
  const code = fs.readFileSync('src/widgets/Content/SVGComponents/LmhSvg.jsx', 'utf8');
  const lines = code.split('\n');
  
  let openBraces = 0;
  let openParens = 0;
  let openBrackets = 0;
  
  for(let i = 0; i < lines.length; i++) {
    const line = lines[i];
    for(let char of line) {
      if(char === '{') openBraces++;
      else if(char === '}') openBraces--;
      else if(char === '(') openParens++;
      else if(char === ')') openParens--;
      else if(char === '[') openBrackets++;
      else if(char === ']') openBrackets--;
    }
    
    if(openBraces < 0 || openParens < 0 || openBrackets < 0) {
      console.log('Syntax error at line', i+1, ':', lines[i].trim());
      break;
    }
  }
  
  console.log('Final counts - Braces:', openBraces, 'Parens:', openParens, 'Brackets:', openBrackets);
  
  if(openBraces !== 0 || openParens !== 0 || openBrackets !== 0) {
    console.log('Unmatched brackets detected!');
  } else {
    console.log('All brackets are properly matched.');
  }
  
} catch(e) {
  console.log('Error:', e.message);
}