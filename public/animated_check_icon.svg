<svg width="300" height="300" viewBox="0 0 300 300" xmlns="http://www.w3.org/2000/svg" id="checkIcon">
  <!-- 透明背景 -->
  
  <!-- 四个角的点 - 同时移动，加快速度 -->
  <!-- 左上角点 -->
  <circle r="4" fill="#4ade80" transform="translate(150,150)" id="dot1">
    <animateTransform
      attributeName="transform"
      type="translate"
      values="150,150; 30,30"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="dot1Open"/>
    <animateTransform
      attributeName="transform"
      type="translate"
      values="30,30; 150,150"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="dot1Close"/>
  </circle>
  
  <!-- 右上角点 -->
  <circle r="4" fill="#4ade80" transform="translate(150,150)" id="dot2">
    <animateTransform
      attributeName="transform"
      type="translate"
      values="150,150; 270,30"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="dot2Open"/>
    <animateTransform
      attributeName="transform"
      type="translate"
      values="270,30; 150,150"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="dot2Close"/>
  </circle>
  
  <!-- 左下角点 -->
  <circle r="4" fill="#4ade80" transform="translate(150,150)" id="dot3">
    <animateTransform
      attributeName="transform"
      type="translate"
      values="150,150; 30,270"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="dot3Open"/>
    <animateTransform
      attributeName="transform"
      type="translate"
      values="30,270; 150,150"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="dot3Close"/>
  </circle>
  
  <!-- 右下角点 -->
  <circle r="4" fill="#4ade80" transform="translate(150,150)" id="dot4">
    <animateTransform
      attributeName="transform"
      type="translate"
      values="150,150; 270,270"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="dot4Open"/>
    <animateTransform
      attributeName="transform"
      type="translate"
      values="270,270; 150,150"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="dot4Close"/>
  </circle>

  <!-- 外层圆形 - 从中心扩散，带弹簧效果 -->
  <circle cx="150" cy="150" r="80" fill="#22c55e" opacity="0" id="outerCircle" transform-origin="150 150">
    <animate
      attributeName="opacity"
      values="0; 0.6"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="outerCircleOpacityOpen"/>
    <animateTransform
      attributeName="transform"
      type="scale"
      values="0; 1.2; 0.9; 1.1; 1"
      dur="0.6s"
      begin="indefinite"
      fill="freeze"
      id="outerCircleScaleOpen"/>
    <animate
      attributeName="opacity"
      values="0.6; 0"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="outerCircleOpacityClose"/>
    <animateTransform
      attributeName="transform"
      type="scale"
      values="1; 0"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="outerCircleScaleClose"/>
  </circle>
  
  <!-- 内层圆形 - 从中心扩散，带弹簧效果 -->
  <circle cx="150" cy="150" r="60" fill="#4ade80" opacity="0" id="innerCircle" transform-origin="150 150">
    <animate
      attributeName="opacity"
      values="0; 1"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="innerCircleOpacityOpen"/>
    <animateTransform
      attributeName="transform"
      type="scale"
      values="0; 1.3; 0.85; 1.15; 1"
      dur="0.7s"
      begin="indefinite"
      fill="freeze"
      id="innerCircleScaleOpen"/>
    <animate
      attributeName="opacity"
      values="1; 0"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="innerCircleOpacityClose"/>
    <animateTransform
      attributeName="transform"
      type="scale"
      values="1; 0"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="innerCircleScaleClose"/>
  </circle>

  <!-- 对勾 - 更快速度 -->
  <path d="M 120 150 L 140 170 L 180 130" 
        stroke="#1a1a1a" 
        stroke-width="8" 
        stroke-linecap="round" 
        stroke-linejoin="round" 
        fill="none"
        opacity="0"
        stroke-dasharray="100"
        stroke-dashoffset="100"
        id="checkmark">
    <animate
      attributeName="opacity"
      values="0; 1"
      dur="0.1s"
      begin="indefinite"
      fill="freeze"
      id="checkOpacityOpen"/>
    <animate
      attributeName="stroke-dashoffset"
      values="100; 0"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="checkDrawOpen"/>
    <animate
      attributeName="opacity"
      values="1; 0"
      dur="0.1s"
      begin="indefinite"
      fill="freeze"
      id="checkOpacityClose"/>
    <animate
      attributeName="stroke-dashoffset"
      values="0; 100"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="checkDrawClose"/>
  </path>

  <!-- 发光效果滤镜 -->
  <defs>
    <filter id="glow">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 为外层圆添加发光效果 - 带弹簧效果 -->
  <circle cx="150" cy="150" r="80" fill="none" stroke="#22c55e" stroke-width="2" opacity="0" filter="url(#glow)" id="glowCircle" transform-origin="150 150">
    <animate
      attributeName="opacity"
      values="0; 0.8; 0.4"
      dur="0.8s"
      begin="indefinite"
      fill="freeze"
      id="glowOpacityOpen"/>
    <animateTransform
      attributeName="transform"
      type="scale"
      values="0; 1.4; 0.8; 1.2; 1"
      dur="0.8s"
      begin="indefinite"
      fill="freeze"
      id="glowScaleOpen"/>
    <animate
      attributeName="opacity"
      values="0.4; 0"
      dur="0.5s"
      begin="indefinite"
      fill="freeze"
      id="glowOpacityClose"/>
    <animateTransform
      attributeName="transform"
      type="scale"
      values="1; 0"
      dur="0.5s"
      begin="indefinite"
      fill="freeze"
      id="glowScaleClose"/>
  </circle>

  <script>
    <![CDATA[
    // 获取所有动画元素
    const animations = {
      open: {
        dots: ['dot1Open', 'dot2Open', 'dot3Open', 'dot4Open'],
        outerCircle: ['outerCircleOpacityOpen', 'outerCircleScaleOpen'],
        innerCircle: ['innerCircleOpacityOpen', 'innerCircleScaleOpen'],
        checkmark: ['checkOpacityOpen', 'checkDrawOpen'],
        glow: ['glowOpacityOpen', 'glowScaleOpen']
      },
      close: {
        dots: ['dot1Close', 'dot2Close', 'dot3Close', 'dot4Close'],
        outerCircle: ['outerCircleOpacityClose', 'outerCircleScaleClose'],
        innerCircle: ['innerCircleOpacityClose', 'innerCircleScaleClose'],
        checkmark: ['checkOpacityClose', 'checkDrawClose'],
        glow: ['glowOpacityClose', 'glowScaleClose']
      }
    };

    // 播放开启动画
    function playOpenAnimation() {
      // 停止所有关闭动画
      stopAllAnimations('close');
      
      // 重置初始状态
      resetToInitialState();
      
      // 播放开启动画序列 - 更快速度
      setTimeout(() => {
        // 四个点同时移动 (0ms, 300ms duration)
        animations.open.dots.forEach(id => {
          document.getElementById(id).beginElement();
        });
      }, 0);
      
      setTimeout(() => {
        // 外层圆形和发光效果 (300ms)
        animations.open.outerCircle.forEach(id => {
          document.getElementById(id).beginElement();
        });
        animations.open.glow.forEach(id => {
          document.getElementById(id).beginElement();
        });
      }, 300);
      
      setTimeout(() => {
        // 内层圆形 (450ms)
        animations.open.innerCircle.forEach(id => {
          document.getElementById(id).beginElement();
        });
      }, 450);
      
      setTimeout(() => {
        // 对勾 (750ms)
        animations.open.checkmark.forEach(id => {
          document.getElementById(id).beginElement();
        });
      }, 750);
    }

    // 播放关闭动画
    function playCloseAnimation() {
      // 停止所有开启动画
      stopAllAnimations('open');
      
      // 播放关闭动画序列（倒序）- 更快速度
      setTimeout(() => {
        // 对勾消失 (0ms)
        animations.close.checkmark.forEach(id => {
          document.getElementById(id).beginElement();
        });
      }, 0);
      
      setTimeout(() => {
        // 内层圆形消失 (300ms)
        animations.close.innerCircle.forEach(id => {
          document.getElementById(id).beginElement();
        });
      }, 300);
      
      setTimeout(() => {
        // 外层圆形和发光效果消失 (450ms)
        animations.close.outerCircle.forEach(id => {
          document.getElementById(id).beginElement();
        });
        animations.close.glow.forEach(id => {
          document.getElementById(id).beginElement();
        });
      }, 450);
      
      setTimeout(() => {
        // 四个点回到中心 (950ms)
        animations.close.dots.forEach(id => {
          document.getElementById(id).beginElement();
        });
      }, 950);
    }

    // 停止指定类型的所有动画
    function stopAllAnimations(type) {
      Object.values(animations[type]).flat().forEach(id => {
        const element = document.getElementById(id);
        if (element) {
          element.endElement();
        }
      });
    }

    // 重置到初始状态
    function resetToInitialState() {
      // 重置四个点到中心
      ['dot1', 'dot2', 'dot3', 'dot4'].forEach(id => {
        const element = document.getElementById(id);
        element.setAttribute('transform', 'translate(150,150)');
      });
      
      // 重置圆形 - 修复圆形显示问题
      const outerCircle = document.getElementById('outerCircle');
      outerCircle.setAttribute('opacity', '0');
      outerCircle.setAttribute('transform', 'scale(0)');
      
      const innerCircle = document.getElementById('innerCircle');
      innerCircle.setAttribute('opacity', '0');
      innerCircle.setAttribute('transform', 'scale(0)');
      
      const glowCircle = document.getElementById('glowCircle');
      glowCircle.setAttribute('opacity', '0');
      glowCircle.setAttribute('transform', 'scale(0)');
      
      // 重置对勾
      const checkmark = document.getElementById('checkmark');
      checkmark.setAttribute('opacity', '0');
      checkmark.setAttribute('stroke-dashoffset', '100');
    }

    // 暴露控制函数到全局
    window.checkIconControl = {
      open: playOpenAnimation,
      close: playCloseAnimation,
      reset: resetToInitialState
    };

    // 添加点击事件来测试动画
    let isOpen = false;
    document.getElementById('checkIcon').addEventListener('click', function() {
      if (isOpen) {
        playCloseAnimation();
        isOpen = false;
      } else {
        playOpenAnimation();
        isOpen = true;
      }
    });
    ]]>
  </script>
</svg>