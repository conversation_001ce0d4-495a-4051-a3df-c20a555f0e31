<svg width="300" height="300" viewBox="0 0 300 300" xmlns="http://www.w3.org/2000/svg" id="checkIcon" xmlns:xlink="http://www.w3.org/1999/xlink">
  <!-- 透明背景 -->
  
  <!-- 四个角的点 - 同时移动，加快速度 -->
  <!-- 左上角点 -->
  <circle r="4" fill="#ef4444" transform="translate(150,150)" id="dot1">
    <animateTransform
      attributeName="transform"
      type="translate"
      values="150,150; 30,30"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="dot1Open"/>
    <animateTransform
      attributeName="transform"
      type="translate"
      values="30,30; 150,150"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="dot1Close"/>
  </circle>
  
  <!-- 右上角点 -->
  <circle r="4" fill="#ef4444" transform="translate(150,150)" id="dot2">
    <animateTransform
      attributeName="transform"
      type="translate"
      values="150,150; 270,30"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="dot2Open"/>
    <animateTransform
      attributeName="transform"
      type="translate"
      values="270,30; 150,150"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="dot2Close"/>
  </circle>
  
  <!-- 左下角点 -->
  <circle r="4" fill="#ef4444" transform="translate(150,150)" id="dot3">
    <animateTransform
      attributeName="transform"
      type="translate"
      values="150,150; 30,270"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="dot3Open"/>
    <animateTransform
      attributeName="transform"
      type="translate"
      values="30,270; 150,150"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="dot3Close"/>
  </circle>
  
  <!-- 右下角点 -->
  <circle r="4" fill="#ef4444" transform="translate(150,150)" id="dot4">
    <animateTransform
      attributeName="transform"
      type="translate"
      values="150,150; 270,270"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="dot4Open"/>
    <animateTransform
      attributeName="transform"
      type="translate"
      values="270,270; 150,150"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="dot4Close"/>
  </circle>

  <!-- 发光效果滤镜 -->
  <defs>
    <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="5" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <filter id="softEdge" x="-15%" y="-15%" width="130%" height="130%">
      <feGaussianBlur stdDeviation="1.5" in="SourceGraphic" result="blur"/>
      <feColorMatrix in="blur" type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 18 -7" result="softEdge"/>
      <feFlood flood-color="#ef4444" result="color"/>
      <feComposite in="color" in2="softEdge" operator="in" result="glowColor"/>
      <feComposite in="SourceGraphic" in2="glowColor" operator="over"/>
    </filter>
    
    <filter id="antiAlias" x="-15%" y="-15%" width="130%" height="130%">
      <feGaussianBlur stdDeviation="0.8" in="SourceGraphic" result="blur"/>
      <feColorMatrix in="blur" type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 20 -8" result="aa"/>
      <feComposite in="SourceGraphic" in2="aa" operator="over"/>
    </filter>
    
    <!-- 创建渐变效果减轻锯齿 -->
    <linearGradient id="outerTriangleGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#dc2626"/>
      <stop offset="100%" stop-color="#b91c1c"/>
    </linearGradient>
    
    <linearGradient id="innerTriangleGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#ef4444"/>
      <stop offset="100%" stop-color="#dc2626"/>
    </linearGradient>
  </defs>
  
  <!-- 外层三角形 - 从中心扩散，带弹簧效果，添加圆角 -->
  <g>
    <!-- 外层三角形背景 - 用于抗锯齿效果 -->
    <path d="M 150 60 Q 155 58, 160 60 L 237 215 Q 242 223, 235 226 L 65 226 Q 58 223, 63 215 Z" 
        fill="url(#outerTriangleGradient)" 
        opacity="0" 
        id="outerCircleBg" 
        transform-origin="150 150"
        style="filter: url(#softEdge); shape-rendering: geometricPrecision;">
      <animate
        attributeName="opacity"
        values="0; 0.6"
        dur="0.3s"
        begin="indefinite"
        fill="freeze"
        id="outerCircleBgOpacityOpen"/>
      <animateTransform
        attributeName="transform"
        type="scale"
        values="0; 1.2; 0.9; 1.1; 1"
        dur="0.6s"
        begin="indefinite"
        fill="freeze"
        id="outerCircleBgScaleOpen"/>
      <animate
        attributeName="opacity"
        values="0.6; 0"
        dur="0.3s"
        begin="indefinite"
        fill="freeze"
        id="outerCircleBgOpacityClose"/>
      <animateTransform
        attributeName="transform"
        type="scale"
        values="1; 0"
        dur="0.3s"
        begin="indefinite"
        fill="freeze"
        id="outerCircleBgScaleClose"/>
    </path>
    
    <!-- 外层三角形主体 -->
    <path d="M 150 60 Q 155 58, 160 60 L 237 215 Q 242 223, 235 226 L 65 226 Q 58 223, 63 215 Z" 
        fill="url(#outerTriangleGradient)" 
        opacity="0" 
        id="outerCircle" 
        transform-origin="150 150"
        style="shape-rendering: geometricPrecision;">
      <animate
        attributeName="opacity"
        values="0; 0.6"
        dur="0.3s"
        begin="indefinite"
        fill="freeze"
        id="outerCircleOpacityOpen"/>
      <animateTransform
        attributeName="transform"
        type="scale"
        values="0; 1.2; 0.9; 1.1; 1"
        dur="0.6s"
        begin="indefinite"
        fill="freeze"
        id="outerCircleScaleOpen"/>
      <animate
        attributeName="opacity"
        values="0.6; 0"
        dur="0.3s"
        begin="indefinite"
        fill="freeze"
        id="outerCircleOpacityClose"/>
      <animateTransform
        attributeName="transform"
        type="scale"
        values="1; 0"
        dur="0.3s"
        begin="indefinite"
        fill="freeze"
        id="outerCircleScaleClose"/>
    </path>
  </g>
  
  <!-- 内层三角形 - 从中心扩散，带弹簧效果，添加圆角，位置下移 -->
  <g>
    <!-- 内层三角形背景 - 用于抗锯齿效果 -->
    <path d="M 150 90 Q 154 88, 158 90 L 218 208 Q 222 215, 216 217 L 84 217 Q 78 215, 82 208 Z" 
        fill="url(#innerTriangleGradient)" 
        opacity="0" 
        id="innerCircleBg" 
        transform-origin="150 150"
        style="filter: url(#antiAlias); shape-rendering: geometricPrecision;">
      <animate
        attributeName="opacity"
        values="0; 1"
        dur="0.3s"
        begin="indefinite"
        fill="freeze"
        id="innerCircleBgOpacityOpen"/>
      <animateTransform
        attributeName="transform"
        type="scale"
        values="0; 1.3; 0.85; 1.15; 1"
        dur="0.7s"
        begin="indefinite"
        fill="freeze"
        id="innerCircleBgScaleOpen"/>
      <animate
        attributeName="opacity"
        values="1; 0"
        dur="0.3s"
        begin="indefinite"
        fill="freeze"
        id="innerCircleBgOpacityClose"/>
      <animateTransform
        attributeName="transform"
        type="scale"
        values="1; 0"
        dur="0.3s"
        begin="indefinite"
        fill="freeze"
        id="innerCircleBgScaleClose"/>
    </path>
    
    <!-- 内层三角形主体 -->
    <path d="M 150 90 Q 154 88, 158 90 L 218 208 Q 222 215, 216 217 L 84 217 Q 78 215, 82 208 Z" 
        fill="url(#innerTriangleGradient)" 
        opacity="0" 
        id="innerCircle" 
        transform-origin="150 150"
        style="shape-rendering: geometricPrecision;">
      <animate
        attributeName="opacity"
        values="0; 1"
        dur="0.3s"
        begin="indefinite"
        fill="freeze"
        id="innerCircleOpacityOpen"/>
      <animateTransform
        attributeName="transform"
        type="scale"
        values="0; 1.3; 0.85; 1.15; 1"
        dur="0.7s"
        begin="indefinite"
        fill="freeze"
        id="innerCircleScaleOpen"/>
      <animate
        attributeName="opacity"
        values="1; 0"
        dur="0.3s"
        begin="indefinite"
        fill="freeze"
        id="innerCircleOpacityClose"/>
      <animateTransform
        attributeName="transform"
        type="scale"
        values="1; 0"
        dur="0.3s"
        begin="indefinite"
        fill="freeze"
        id="innerCircleScaleClose"/>
    </path>
  </g>

  <!-- 感叹号 - 更快速度 -->
  <g opacity="0" id="checkmark" transform="scale(1)" transform-origin="150 150">
    <!-- 感叹号主体 -->
    <rect x="145" y="120" width="10" height="50" rx="5" fill="#1a1a1a"/>
    <!-- 感叹号底部点 -->
    <circle cx="150" cy="185" r="6" fill="#1a1a1a"/>
    
    <animate
      attributeName="opacity"
      values="0; 1"
      dur="0.2s"
      begin="indefinite"
      fill="freeze"
      id="checkOpacityOpen"/>
    <animateTransform
      attributeName="transform"
      type="scale"
      values="0; 1.15; 0.95; 1"
      dur="0.4s"
      begin="indefinite"
      fill="freeze"
      id="checkDrawOpen"/>
    <animate
      attributeName="opacity"
      values="1; 0"
      dur="0.2s"
      begin="indefinite"
      fill="freeze"
      id="checkOpacityClose"/>
    <animateTransform
      attributeName="transform"
      type="scale"
      values="1; 0"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="checkDrawClose"/>
  </g>

  <!-- 为外层三角形添加发光效果 - 带弹簧效果，圆角与主三角形匹配 -->
  <path d="M 150 60 Q 155 58, 160 60 L 237 215 Q 242 223, 235 226 L 65 226 Q 58 223, 63 215 Z" 
        fill="none" 
        stroke="#dc2626" 
        stroke-width="2" 
        opacity="0" 
        filter="url(#glow)" 
        id="glowCircle" 
        transform-origin="150 150"
        style="shape-rendering: geometricPrecision;">
    <animate
      attributeName="opacity"
      values="0; 0.8; 0.4"
      dur="0.8s"
      begin="indefinite"
      fill="freeze"
      id="glowOpacityOpen"/>
    <animateTransform
      attributeName="transform"
      type="scale"
      values="0; 1.4; 0.8; 1.2; 1"
      dur="0.8s"
      begin="indefinite"
      fill="freeze"
      id="glowScaleOpen"/>
    <animate
      attributeName="opacity"
      values="0.4; 0"
      dur="0.5s"
      begin="indefinite"
      fill="freeze"
      id="glowOpacityClose"/>
    <animateTransform
      attributeName="transform"
      type="scale"
      values="1; 0"
      dur="0.5s"
      begin="indefinite"
      fill="freeze"
      id="glowScaleClose"/>
  </path>

  <script>
    <![CDATA[
    // 获取所有动画元素
    const animations = {
      open: {
        dots: ['dot1Open', 'dot2Open', 'dot3Open', 'dot4Open'],
        outerCircle: ['outerCircleOpacityOpen', 'outerCircleScaleOpen', 
                     'outerCircleBgOpacityOpen', 'outerCircleBgScaleOpen'],
        innerCircle: ['innerCircleOpacityOpen', 'innerCircleScaleOpen',
                     'innerCircleBgOpacityOpen', 'innerCircleBgScaleOpen'],
        checkmark: ['checkOpacityOpen', 'checkDrawOpen'],
        glow: ['glowOpacityOpen', 'glowScaleOpen']
      },
      close: {
        dots: ['dot1Close', 'dot2Close', 'dot3Close', 'dot4Close'],
        outerCircle: ['outerCircleOpacityClose', 'outerCircleScaleClose',
                     'outerCircleBgOpacityClose', 'outerCircleBgScaleClose'],
        innerCircle: ['innerCircleOpacityClose', 'innerCircleScaleClose',
                     'innerCircleBgOpacityClose', 'innerCircleBgScaleClose'],
        checkmark: ['checkOpacityClose', 'checkDrawClose'],
        glow: ['glowOpacityClose', 'glowScaleClose']
      }
    };

    // 播放开启动画
    function playOpenAnimation() {
      // 停止所有关闭动画
      stopAllAnimations('close');
      
      // 重置初始状态
      resetToInitialState();
      
      // 播放开启动画序列
      setTimeout(() => {
        // 四个点同时移动 (0ms, 300ms duration)
        animations.open.dots.forEach(id => {
          document.getElementById(id).beginElement();
        });
      }, 0);
      
      setTimeout(() => {
        // 外层三角形和发光效果 (300ms)
        animations.open.outerCircle.forEach(id => {
          document.getElementById(id).beginElement();
        });
        animations.open.glow.forEach(id => {
          document.getElementById(id).beginElement();
        });
      }, 300);
      
      setTimeout(() => {
        // 内层三角形 (450ms)
        animations.open.innerCircle.forEach(id => {
          document.getElementById(id).beginElement();
        });
      }, 450);
      
      setTimeout(() => {
        // 感叹号 (600ms) - 确保先设置初始状态
        const checkmark = document.getElementById('checkmark');
        checkmark.setAttribute('transform', 'scale(0)');
        checkmark.setAttribute('opacity', '0');
        
        // 然后播放动画
        animations.open.checkmark.forEach(id => {
          document.getElementById(id).beginElement();
        });
      }, 600);
    }

    // 播放关闭动画
    function playCloseAnimation() {
      // 停止所有开启动画
      stopAllAnimations('open');
      
      // 播放关闭动画序列（倒序）
      setTimeout(() => {
        // 感叹号消失 (0ms)
        const checkmark = document.getElementById('checkmark');
        if(parseFloat(checkmark.getAttribute('opacity')) > 0) {
          animations.close.checkmark.forEach(id => {
            document.getElementById(id).beginElement();
          });
        }
      }, 0);
      
      setTimeout(() => {
        // 内层三角形消失 (300ms)
        animations.close.innerCircle.forEach(id => {
          document.getElementById(id).beginElement();
        });
      }, 300);
      
      setTimeout(() => {
        // 外层三角形和发光效果消失 (450ms)
        animations.close.outerCircle.forEach(id => {
          document.getElementById(id).beginElement();
        });
        animations.close.glow.forEach(id => {
          document.getElementById(id).beginElement();
        });
      }, 450);
      
      setTimeout(() => {
        // 四个点回到中心 (950ms)
        animations.close.dots.forEach(id => {
          document.getElementById(id).beginElement();
        });
      }, 950);
    }

    // 停止指定类型的所有动画
    function stopAllAnimations(type) {
      Object.values(animations[type]).flat().forEach(id => {
        try {
          const element = document.getElementById(id);
          if (element) {
            element.endElement();
          }
        } catch(e) {
          console.error("动画停止错误:", e);
        }
      });
    }

    // 重置到初始状态
    function resetToInitialState() {
      // 重置四个点到中心
      ['dot1', 'dot2', 'dot3', 'dot4'].forEach(id => {
        const element = document.getElementById(id);
        element.setAttribute('transform', 'translate(150,150)');
      });
      
      // 重置三角形 - 修复三角形显示问题
      const outerCircle = document.getElementById('outerCircle');
      outerCircle.setAttribute('opacity', '0');
      outerCircle.setAttribute('transform', 'scale(0)');
      
      const outerCircleBg = document.getElementById('outerCircleBg');
      if(outerCircleBg) {
        outerCircleBg.setAttribute('opacity', '0');
        outerCircleBg.setAttribute('transform', 'scale(0)');
      }
      
      const innerCircle = document.getElementById('innerCircle');
      innerCircle.setAttribute('opacity', '0');
      innerCircle.setAttribute('transform', 'scale(0)');
      
      const innerCircleBg = document.getElementById('innerCircleBg');
      if(innerCircleBg) {
        innerCircleBg.setAttribute('opacity', '0');
        innerCircleBg.setAttribute('transform', 'scale(0)');
      }
      
      const glowCircle = document.getElementById('glowCircle');
      glowCircle.setAttribute('opacity', '0');
      glowCircle.setAttribute('transform', 'scale(0)');
      
      // 重置感叹号
      const checkmark = document.getElementById('checkmark');
      checkmark.setAttribute('opacity', '0');
      checkmark.removeAttribute('transform');  // 移除所有transform属性
    }

    // 暴露控制函数到全局
    window.checkIconControl = {
      open: playOpenAnimation,
      close: playCloseAnimation,
      reset: resetToInitialState
    };

    // 添加点击事件来测试动画
    let isOpen = false;
    document.getElementById('checkIcon').addEventListener('click', function() {
      if (isOpen) {
        playCloseAnimation();
        isOpen = false;
      } else {
        playOpenAnimation();
        isOpen = true;
      }
    });
    ]]>
  </script>
</svg>