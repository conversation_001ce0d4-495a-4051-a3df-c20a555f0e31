<svg width="300" height="300" viewBox="0 0 300 300" xmlns="http://www.w3.org/2000/svg" id="checkIcon">
  <!-- 透明背景 -->
  
  <!-- 四个角的点 - 同时移动，加快速度 -->
  <!-- 左上角点 -->
  <circle r="4" fill="#F59F25" transform="translate(150,150)" id="dot1">
    <animateTransform
      attributeName="transform"
      type="translate"
      values="150,150; 30,30"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="dot1Open"/>
    <animateTransform
      attributeName="transform"
      type="translate"
      values="30,30; 150,150"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="dot1Close"/>
  </circle>
  
  <!-- 右上角点 -->
  <circle r="4" fill="#F59F25" transform="translate(150,150)" id="dot2">
    <animateTransform
      attributeName="transform"
      type="translate"
      values="150,150; 270,30"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="dot2Open"/>
    <animateTransform
      attributeName="transform"
      type="translate"
      values="270,30; 150,150"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="dot2Close"/>
  </circle>
  
  <!-- 左下角点 -->
  <circle r="4" fill="#F59F25" transform="translate(150,150)" id="dot3">
    <animateTransform
      attributeName="transform"
      type="translate"
      values="150,150; 30,270"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="dot3Open"/>
    <animateTransform
      attributeName="transform"
      type="translate"
      values="30,270; 150,150"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="dot3Close"/>
  </circle>
  
  <!-- 右下角点 -->
  <circle r="4" fill="#F59F25" transform="translate(150,150)" id="dot4">
    <animateTransform
      attributeName="transform"
      type="translate"
      values="150,150; 270,270"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="dot4Open"/>
    <animateTransform
      attributeName="transform"
      type="translate"
      values="270,270; 150,150"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="dot4Close"/>
  </circle>

  <!-- 外层正六边形 - 从中心扩散，带弹簧效果 -->
  <polygon points="150,50 236.6,100 236.6,200 150,250 63.4,200 63.4,100" 
           fill="none" 
           stroke="#F59F25" 
           stroke-width="4" 
           opacity="0" 
           id="outerCircle" 
           transform-origin="150 150">
    <animate
      attributeName="opacity"
      values="0; 0.8"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="outerCircleOpacityOpen"/>
    <animateTransform
      attributeName="transform"
      type="scale"
      values="0; 1.2; 0.9; 1.1; 1"
      dur="0.6s"
      begin="indefinite"
      fill="freeze"
      id="outerCircleScaleOpen"/>
    <animate
      attributeName="opacity"
      values="0.8; 0"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="outerCircleOpacityClose"/>
    <animateTransform
      attributeName="transform"
      type="scale"
      values="1; 0"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="outerCircleScaleClose"/>
  </polygon>
  
  <!-- 内层正六边形 - 从中心扩散，带弹簧效果 -->
  <polygon points="150,60 223.2,105 223.2,195 150,240 76.8,195 76.8,105" 
           fill="#F59F25" 
           opacity="0" 
           id="innerCircle" 
           transform-origin="150 150">
    <animate
      attributeName="opacity"
      values="0; 1"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="innerCircleOpacityOpen"/>
    <animateTransform
      attributeName="transform"
      type="scale"
      values="0; 1.3; 0.85; 1.15; 1"
      dur="0.7s"
      begin="indefinite"
      fill="freeze"
      id="innerCircleScaleOpen"/>
    <animate
      attributeName="opacity"
      values="1; 0"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="innerCircleOpacityClose"/>
    <animateTransform
      attributeName="transform"
      type="scale"
      values="1; 0"
      dur="0.3s"
      begin="indefinite"
      fill="freeze"
      id="innerCircleScaleClose"/>
  </polygon>

  <!-- 问号 -->
  <g id="checkmark" opacity="0" transform-origin="150 150">
    <!-- 问号主体 -->
    <path d="M 130 120 
             Q 130 100, 150 100
             Q 170 100, 170 120
             Q 170 140, 150 150
             L 150 170" 
          stroke="#1a1a1a" 
          stroke-width="10" 
          stroke-linecap="round" 
          stroke-linejoin="round" 
          fill="none"
          stroke-dasharray="200"
          stroke-dashoffset="200"
          id="questionPath">
      <animate
        attributeName="stroke-dashoffset"
        values="200; 0"
        dur="0.4s"
        begin="indefinite"
        fill="freeze"
        id="checkDrawOpen"/>
      <animate
        attributeName="stroke-dashoffset"
        values="0; 200"
        dur="0.4s"
        begin="indefinite"
        fill="freeze"
        id="checkDrawClose"/>
    </path>
    
    <!-- 问号整体透明度动画 -->
    <animate
      attributeName="opacity"
      values="0; 1"
      dur="0.1s"
      begin="indefinite"
      fill="freeze"
      id="checkOpacityOpen"/>
    <animate
      attributeName="opacity"
      values="1; 0"
      dur="0.1s"
      begin="indefinite"
      fill="freeze"
      id="checkOpacityClose"/>
  </g>

  <!-- 问号下面的点 - 直接固定显示，无动画 -->
  <circle cx="150" cy="190" r="6" fill="#1a1a1a" opacity="1" id="questionDot"/>

  <!-- 发光效果滤镜 -->
  <defs>
    <filter id="glow">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 为外层正六边形添加发光效果 - 带弹簧效果 -->
  <polygon points="150,40 249.8,95 249.8,205 150,260 50.2,205 50.2,95" 
           fill="none" 
           stroke="#F59F25" 
           stroke-width="3" 
           opacity="0" 
           filter="url(#glow)" 
           id="glowCircle" 
           transform-origin="150 150">
    <animate
      attributeName="opacity"
      values="0; 0.8; 0.4"
      dur="0.8s"
      begin="indefinite"
      fill="freeze"
      id="glowOpacityOpen"/>
    <animateTransform
      attributeName="transform"
      type="scale"
      values="0; 1.4; 0.8; 1.2; 1"
      dur="0.8s"
      begin="indefinite"
      fill="freeze"
      id="glowScaleOpen"/>
    <animate
      attributeName="opacity"
      values="0.4; 0"
      dur="0.5s"
      begin="indefinite"
      fill="freeze"
      id="glowOpacityClose"/>
    <animateTransform
      attributeName="transform"
      type="scale"
      values="1; 0"
      dur="0.5s"
      begin="indefinite"
      fill="freeze"
      id="glowScaleClose"/>
  </polygon>

  <script>
    <![CDATA[
    // ... existing code ...
    ]]>
  </script>
</svg>