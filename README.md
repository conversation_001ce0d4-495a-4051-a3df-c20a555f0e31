# React + Vite

This template provides a minimal setup to get <PERSON><PERSON> working in Vite with HMR and some ESLint rules.

Currently, two official plugins are available:

- [@vitejs/plugin-react](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react/README.md) uses [Babel](https://babeljs.io/) for Fast Refresh
- [@vitejs/plugin-react-swc](https://github.com/vitejs/vite-plugin-react-swc) uses [SWC](https://swc.rs/) for Fast Refresh

## 项目结构

```
.
├── public/          # 静态资源 (会被直接复制到打包目录的根目录)
├── src/             # 主要源代码
│   ├── assets/      # 模块化资源 (会被webpack处理)
│   ├── components/  # React 组件
│   ├── App.jsx      # 应用根组件
│   └── main.jsx     # 应用入口
├── .eslintrc.cjs    # ESLint 配置文件
├── .gitignore       # Git忽略配置
├── index.html       # SPA 入口 HTML
├── package.json     # 项目依赖和脚本
├── README.md        # 项目说明文档
├── tailwind.config.js # Tailwind CSS 配置文件 (如果使用)
└── vite.config.js   # Vite 配置文件
```

## 可用命令

在项目目录中，你可以运行以下命令:

### `npm run dev` 或 `yarn dev`

在开发模式下运行应用。
打开 [http://localhost:5173](http://localhost:5173) (默认端口，具体看启动日志) 在浏览器中查看。

修改代码后页面会自动重新加载。
你也可以在控制台中看到代码检查的错误。

### `npm run build` 或 `yarn build`

将应用打包到 `dist` 文件夹，用于生产环境部署。
它会以最佳方式打包 React 代码并进行优化以获得最佳性能。

打包后的文件会被压缩，文件名中会包含哈希值。
你的应用已经准备好部署了！

### `npm run lint` 或 `yarn lint`

使用 ESLint 检查代码规范。

### `npm run preview` 或 `yarn preview`

在本地运行打包后的应用，用于预览生产版本。
