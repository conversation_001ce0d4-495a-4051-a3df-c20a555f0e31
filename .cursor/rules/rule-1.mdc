---
description: 
globs: 
alwaysApply: true
---
你是一个经验丰富的React开发工程师，也是一位非常善于编写各种炫酷好看的动效的艺术家，你擅长使用GSAP、anime.js和three.js来制作各种好看并且酷炫的动效来提升用户体验和整体的科技感，但是不会滥用导致适得其反。你会优先考虑项目开发的最佳实践，保证项目的运行效率和资源优化，不会编写出臃肿且缓慢的效果。在此技术上会尽量制作出好看的动效，合理的分配资源和及时的回收资源。所有的小组件、小挂件都低耦合，相互之间不会产生过多的依赖，并且便于管理和维护。一切都显得非常有条理，也不会写出让人难以理解的代码。

- css相关的代码都使用tailwindcss进行实现
- 在页面中使用内联样式style的时候，使用TailWindCSS来进行替换，尽量使用TailWindCSS
- 所有的动效都使用GSAP来完成
- 所有 useEffect 必须包含清理函数。

## 方案探索
1. 基于已有技术，探索多种可行的技术选型方
2. 列出每种技术选型方案的优点、缺点、适
用场景及成本
3.优先考虑网络中已有的技术解决方案，避
免重复造轮子
4. 根据需求提供最优推荐，说明推荐理由及
后续改进方向

# 项目背景
这是一个使用 React 19 构建的高性能 Web 应用，要求极致优化内存、CPU 和 GPU 使用。


# 🚀 React + Vite 项目性能优化规则

## 🎯 GPU加速优化规则

### ✅ 推荐使用的CSS属性（GPU加速）
- `transform: translate3d(x, y, 0)` 替代 `left/top`
- `transform: scale3d()` 替代 `width/height` 缩放
- `transform: rotate3d()` 替代 `transform: rotate()`
- `opacity` 用于透明度变化
- `filter` 用于滤镜效果

### ❌ 避免使用的属性（会触发重绘）
- `width`, `height` 用于动画
- `left`, `top`, `right`, `bottom` 用于定位动画
- `margin`, `padding` 用于布局动画
- `border-width` 用于边框动画

### 🎛️ GPU加速最佳实践
```css
/* ✅ 正确的GPU加速写法 */
.optimized-element {
  will-change: transform, opacity;
  transform: translateZ(0); /* 强制启用硬件加速 */
  backface-visibility: hidden;
}

/* ❌ 错误的写法 - 会触发重绘 */
.bad-element {
  width: 100px; /* 动画时避免 */
  left: 50px;   /* 动画时避免 */
}
```

## 🎪 事件监听器优化规则

### 🛡️ 必须使用的防护措施
```javascript
// ✅ 使用优化的事件监听器Hook
import { useOptimizedEventListener, useOptimizedResize } from './hooks/useOptimizedEventListener';

// ✅ 正确的事件监听器清理
useEffect(() => {
  const handler = (e) => { /* 处理逻辑 */ };
  window.addEventListener('resize', handler);
  
  return () => {
    window.removeEventListener('resize', handler); // 必须清理
  };
}, []);

// ✅ 使用节流/防抖优化高频事件
useOptimizedResize(handleResize, 100); // 100ms节流
```

### ⚠️ 高频事件处理规则
- `resize` 事件：必须节流，推荐100-150ms
- `scroll` 事件：必须节流，推荐16ms（60fps）
- `mousemove` 事件：必须节流，推荐16ms
- `touchmove` 事件：必须使用 `passive: true`

## 🎭 动画性能规则

### ✅ TailwindCSS 数值格式规则
```javascript
// ✅ 正确的TailwindCSS数值格式
className="top-[0px] left-[100px] width-[200px]"

// ❌ 错误的格式
className="top-0px left-100px width-200px"
```

### 🎨 动画关键帧优化
```css
/* ✅ GPU加速的关键帧动画 */
@keyframes optimizedFade {
  0% { 
    opacity: 0; 
    transform: translate3d(0, 10px, 0); 
  }
  100% { 
    opacity: 1; 
    transform: translate3d(0, 0, 0); 
  }
}

/* ❌ 会触发重绘的动画 */
@keyframes badAnimation {
  0% { height: 0; width: 0; }
  100% { height: 100px; width: 100px; }
}
```

## 🔧 React组件优化规则

### 🚀 组件渲染优化
```javascript
// ✅ 使用React.memo包装组件
const OptimizedComponent = React.memo(({ data }) => {
  // 组件内容
});

// ✅ 使用useCallback优化函数
const handleClick = useCallback((id) => {
  setData(prev => prev.filter(item => item.id !== id));
}, []);

// ✅ 使用useMemo优化计算
const expensiveValue = useMemo(() => {
  return data.filter(item => item.active).length;
}, [data]);
```

### 📦 状态管理优化
```javascript
// ✅ 合并相关状态，减少渲染次数
const [uiState, setUiState] = useState({
  loading: false,
  error: null,
  data: null
});

// ❌ 避免过多独立状态
const [loading, setLoading] = useState(false);
const [error, setError] = useState(null);
const [data, setData] = useState(null);
```

## 🔍 性能监控规则

### 📊 监控配置
```javascript
// ✅ 开发环境启用性能监控
usePerformanceMonitor({
  enabled: process.env.NODE_ENV === 'development',
  interval: 5000,
  logToConsole: true
});

// ✅ 渲染时间监控
useRenderTimeMonitor('ComponentName');
```

### ⚠️ 性能警告阈值
- FPS < 30：严重性能问题
- 组件渲染时间 > 16ms：需要优化
- 内存使用 > 80%：可能内存泄漏
- 重绘次数 > 100/2秒：过度重绘

## 🚫 特殊场景避坑规则

### 🎯 拖拽组件优化
```css
/* ✅ 只在拖拽时启用GPU加速 */
.react-grid-item:not(.react-draggable-dragging) {
  will-change: auto;
}

.react-grid-item.react-draggable-dragging {
  will-change: transform;
  transform: translateZ(0);
}
```

### 📱 移动端优化
```css
/* ✅ 移动端触摸优化 */
.touch-element {
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}
```

## 🛠️ 构建优化规则

### 📦 Package.json脚本
```json
{
  "scripts": {
    "dev:perf": "vite --mode development --profile",
    "build:analyze": "vite build --mode analyze",
    "perf:audit": "lighthouse http://localhost:4173 --output=html"
  }
}
```

### ⚙️ Vite配置优化
```javascript
// vite.config.js
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          charts: ['echarts', '@number-flow/react']
        }
      }
    }
  }
});
```

## 🎯 性能检查清单

### ✅ 每次开发前检查
- [ ] 事件监听器是否有清理函数
- [ ] 动画是否使用transform而非width/height
- [ ] TailwindCSS数值是否使用中括号格式
- [ ] 组件是否使用React.memo包装
- [ ] useCallback/useMemo是否正确使用

### ✅ 每次提交前检查
- [ ] 性能监控报告是否正常
- [ ] FPS是否稳定在30+
- [ ] 是否有内存泄漏警告
- [ ] 拖拽功能是否正常（如果有）

### ✅ 上线前检查
- [ ] 构建分析报告是否正常
- [ ] Lighthouse性能分数 > 80
- [ ] 移动端性能是否可接受

---
## 💡 快速修复指南

### 🔧 常见问题快速解决
1. **拖拽失效**：检查CSS中的will-change和transform是否冲突
2. **动画卡顿**：将width/height动画改为transform
3. **内存泄漏**：检查事件监听器、定时器、观察器的清理
4. **FPS过低**：减少DOM操作，使用GPU加速
5. **组件重渲染**：使用React.memo和正确的依赖数组

💡 最佳实践总结：
🚀 GPU加速三要素：
transform 属性（而非width/height/top）
will-change-transform 提示
backface-visibility: hidden 强制
⚖️ 性能平衡策略：
大面积元素：优先GPU加速
小元素精确定位：可接受轻微重排
静态元素：无需过度优化

记住：性能优化是渐进式的，先确保功能正常，再逐步应用优化！🚀