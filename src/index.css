@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
    /*font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif, DingTalkJinBuTi;*/
    /* 全局字体 */
    font-family: 'DingTalkJinBuTi', serif;
    line-height: 1.5;
    font-weight: 400;

    color-scheme: light dark;
    color: rgba(255, 255, 255, 0.87);
    /* 主页面背景色 */
    /*background-color: #242424;*/
    /*background-color: #0D0D0D;*/
    background-color: #141414;

    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-text-size-adjust: 100%;


    /* 边框角落颜色 */
    --div-corner-color: #CBC8D4;
    /* 阳澄 */
    --yangcheng-color: #F86C00;
    /* 暗海绿 */
    --anhailv-color: #75A781;
    /* eDEX-科技绿 */
    --edex-color: #9DC5C7;
    /* 苹果绿 */
    --applegreen-color: #B1CF00;
    /* 指示灯状态-停止 */
    --stateGrey-color: #5e5e5e;
}

/* 定义滚动条的整体样式 */
::-webkit-scrollbar {
    width: 1px; /* 滚动条宽度改为3px */
    height: 1px; /* 水平滚动条高度改为3px */
}

/* 定义滚动条轨道的样式 */
::-webkit-scrollbar-track {
}

/* 定义滚动条滑块的样式 */
::-webkit-scrollbar-thumb {
    background: #B4BCBD; /* 滑块颜色改为指定的青色 */
    border-radius: 4px; /* 滑块圆角 */
}

/* 定义鼠标悬停在滑块上时的样式 */
::-webkit-scrollbar-thumb:hover {
    background: #6AD8D6; /* 悬停时颜色稍微变深一些 */
}

@font-face {
    font-family: DingTalkJinBuTi;
    src: url('./assets/fonts/DingTalkJinBuTi.woff2');
}

@font-face {
    font-family: 'ChakraPetch-Light';
    src: url('./assets/fonts/ChakraPetch-Light.ttf') format('truetype');
}

@font-face {
    font-family: 'ChakraPetch-Medium';
    src: url('./assets/fonts/ChakraPetch-Medium.ttf') format('truetype');
}

a {
    font-weight: 500;
    color: #646cff;
    text-decoration: inherit;
}

a:hover {
    color: #535bf2;
}

body {
    margin: 0;
    display: flex;
    place-items: center;
    min-width: 320px;
    min-height: 100vh;

    /* 启用硬件加速 */
    transform: translate3d(0, 0, 0);
}

h1 {
    font-size: 3.2em;
    line-height: 1.1;
}

button {
    border-radius: 8px;
    border: 1px solid transparent;
    padding: 0.6em 1.2em;
    font-size: 1em;
    font-weight: 500;
    font-family: inherit;
    background-color: #1a1a1a;
    cursor: pointer;
    transition: border-color 0.25s;
}

button:hover {
    border-color: #646cff;
}

button:focus,
button:focus-visible {
    outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
    @tailwind base;
@tailwind components;
@tailwind utilities;

:root {
        color: #213547;
        background-color: #ffffff;
    }

    a:hover {
        color: #747bff;
    }

    button {
        background-color: #f9f9f9;
    }
}
