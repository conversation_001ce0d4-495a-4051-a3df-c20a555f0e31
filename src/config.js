export default class {

    // === 应用基础配置 ===
    static app = {
        version: 'v_3.0.0',
        name: 'JHL_HUD',
        title: '津惠隆智慧水务数字孪生平台'
    };

    // === 服务端接口配置 ===
    static api = {
        // baseURL: process.env.NODE_ENV == 'development' ? '/api' : 'http://***************:9091',
        // socketHost: process.env.NODE_ENV == 'development' ? 'ws://dev-water-platform.gzjhlgs.cn' : 'ws://***************:9091'
    };

    // === 第三方服务配置 ===
    static services = {
        map: {
            amapKey: '58a69b9b18b992fde284413245a0004a'
        }
    };
}
