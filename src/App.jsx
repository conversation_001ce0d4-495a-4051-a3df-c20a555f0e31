import React from 'react';
import './App.css'
import { config as AmapReactConfig } from '@amap/amap-react';
import config from "./config.js";
import { BrowserRouter as Router } from 'react-router-dom';

// 国际化
import { ConfigProvider, theme } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
dayjs.locale('zh-cn');

// 拖拽功能
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { DragContextProvider } from './hooks/useDragContext.jsx';
import DragPreviewLayer from './widgets/Content/GridLayout/DragPreviewLayer.jsx';

// widgets
import Background from "./widgets/Background/background";                     // 背景
import Login from "./widgets/Login/login.jsx";                                // 登录
import Header from "./widgets/Header/Header.jsx";                              // 头部导航栏
import Content from "./widgets/Content/Content.jsx";                           // 内容区域



// 高德地图配置
AmapReactConfig.version = '2.0'; // 默认2.0，这里可以不修改
AmapReactConfig.key = '188ec640e48651628f2a44df69efa2dc';
AmapReactConfig.plugins = [
    // 在此配置你需要预加载的插件，如果不配置，在使用到的时候会自动异步加载
    'AMap.ToolBar',
    'AMap.MoveAnimation',
];



export default class App extends React.Component {
    /**
     * 构造函数
     * @param props
     */


    constructor(props) {
        super(props);
        this.state = {
            isLoggedIn: false,    // 是否已登录
            userData: null,       // 用户数据
        };
    }

    // 登录成功的回调函数
    handleLoginSuccess = (userData = {}) => {
        // 设置登录状态
        this.setState({ 
            isLoggedIn: true,
            userData: userData
        });
        
        // 将登录状态和用户数据保存到本地存储
        localStorage.setItem('isLoggedIn', 'true');
        localStorage.setItem('userData', JSON.stringify(userData));
    };
    
    // 退出登录
    handleLogout = () => {
        // 清除登录状态
        this.setState({
            isLoggedIn: false,
            userData: null
        });
        
        // 清除本地存储中的登录数据
        localStorage.removeItem('isLoggedIn');
        localStorage.removeItem('userData');
    };
    
    // 退出系统
    handleExitSystem = () => {
        // 清除登录状态
        this.setState({
            isLoggedIn: false,
            userData: null
        });
        
        // 清除本地存储中的登录数据
        localStorage.removeItem('isLoggedIn');
        localStorage.removeItem('userData');
        
        // console.log('系统已退出，返回登录页面'); // 已注释掉，减少控制台输出
    };

    componentDidMount() {
        // 从本地存储中获取登录状态
        const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
        
        // 如果已登录，则获取用户数据
        let userData = null;
        if (isLoggedIn) {
            try {
                userData = JSON.parse(localStorage.getItem('userData')) || {};
            } catch (error) {
                console.error('解析用户数据出错:', error);
            }
        }
        
        // 更新状态
        this.setState({ isLoggedIn, userData });
    }
    
    componentWillUnmount() {
    }



    render() {
        const { isLoggedIn, userData } = this.state;

        return (
            <DndProvider backend={HTML5Backend}>
                <DragContextProvider>
                    <div className="flex flex-col w-screen h-screen">
                        <Background />

                        {isLoggedIn ? (
                            <>
                                <Header onLogout={this.handleLogout} userData={userData} onExitSystem={this.handleExitSystem} />
                                <Content />
                            </>
                        ) : (
                            <Login success={this.handleLoginSuccess} />
                        )}
                        
                        {/* 拖拽预览层 */}
                        <DragPreviewLayer />
                    </div>
                </DragContextProvider>
            </DndProvider>
        )
    }


}
