import { useEffect, useRef, useCallback } from 'react';

/**
 * 🚀 高性能事件监听器Hook
 * 包含防抖、节流、内存泄漏防护等优化功能
 * @param {string} eventType - 事件类型
 * @param {function} handler - 事件处理函数
 * @param {object} options - 配置选项
 * @param {HTMLElement|Window|Document} options.element - 事件目标元素，默认为window
 * @param {boolean} options.passive - 是否为被动监听器
 * @param {number} options.debounce - 防抖延迟（毫秒）
 * @param {number} options.throttle - 节流延迟（毫秒）
 * @param {boolean} options.immediate - 节流时是否立即执行第一次
 * @param {Array} options.deps - 依赖数组
 */
export const useOptimizedEventListener = (
    eventType,
    handler,
    {
        element = typeof window !== 'undefined' ? window : null,
        passive = true,
        debounce = 0,
        throttle = 0,
        immediate = true,
        deps = []
    } = {}
) => {
    const handlerRef = useRef(handler);
    const debounceTimerRef = useRef(null);
    const throttleTimerRef = useRef(null);
    const lastExecRef = useRef(0);

    // 保持handler引用最新
    handlerRef.current = handler;

    // 🚀 防抖处理函数
    const debouncedHandler = useCallback((event) => {
        if (debounceTimerRef.current) {
            clearTimeout(debounceTimerRef.current);
        }
        debounceTimerRef.current = setTimeout(() => {
            handlerRef.current(event);
        }, debounce);
    }, [debounce]);

    // 🚀 节流处理函数
    const throttledHandler = useCallback((event) => {
        const now = Date.now();
        
        if (immediate && now - lastExecRef.current >= throttle) {
            lastExecRef.current = now;
            handlerRef.current(event);
        } else if (!throttleTimerRef.current) {
            throttleTimerRef.current = setTimeout(() => {
                lastExecRef.current = Date.now();
                handlerRef.current(event);
                throttleTimerRef.current = null;
            }, throttle - (now - lastExecRef.current));
        }
    }, [throttle, immediate]);

    // 🚀 选择合适的处理函数
    const optimizedHandler = useCallback((event) => {
        if (debounce > 0) {
            debouncedHandler(event);
        } else if (throttle > 0) {
            throttledHandler(event);
        } else {
            handlerRef.current(event);
        }
    }, [debounce, throttle, debouncedHandler, throttledHandler]);

    useEffect(() => {
        if (!element || !eventType) return;

        const options = { passive };

        // 添加事件监听器
        element.addEventListener(eventType, optimizedHandler, options);

        // 清理函数
        return () => {
            element.removeEventListener(eventType, optimizedHandler, options);
            
            // 清理定时器
            if (debounceTimerRef.current) {
                clearTimeout(debounceTimerRef.current);
                debounceTimerRef.current = null;
            }
            if (throttleTimerRef.current) {
                clearTimeout(throttleTimerRef.current);
                throttleTimerRef.current = null;
            }
        };
    }, [element, eventType, optimizedHandler, passive, ...deps]);

    // 手动清理函数
    const cleanup = useCallback(() => {
        if (debounceTimerRef.current) {
            clearTimeout(debounceTimerRef.current);
            debounceTimerRef.current = null;
        }
        if (throttleTimerRef.current) {
            clearTimeout(throttleTimerRef.current);
            throttleTimerRef.current = null;
        }
    }, []);

    return { cleanup };
};

/**
 * 🚀 专用的resize事件监听器Hook
 * 针对resize事件的性能优化
 */
export const useOptimizedResize = (handler, throttleMs = 100) => {
    return useOptimizedEventListener('resize', handler, {
        element: typeof window !== 'undefined' ? window : null,
        throttle: throttleMs,
        immediate: false,
        passive: true
    });
};

/**
 * 🚀 专用的鼠标事件监听器Hook
 * 针对鼠标移动的性能优化
 */
export const useOptimizedMouseEvent = (eventType, handler, throttleMs = 16) => {
    return useOptimizedEventListener(eventType, handler, {
        element: typeof document !== 'undefined' ? document : null,
        throttle: throttleMs,
        immediate: true,
        passive: false // 鼠标事件可能需要preventDefault
    });
};

export default useOptimizedEventListener; 