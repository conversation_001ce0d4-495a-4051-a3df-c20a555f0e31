import { useEffect, useRef, useCallback } from 'react';

/**
 * 🚀 性能监控Hook
 * 用于监控FPS、内存使用、重绘次数等性能指标
 * @param {object} options - 监控选项
 * @param {boolean} options.enabled - 是否启用监控
 * @param {number} options.interval - 监控间隔（毫秒）
 * @param {function} options.onReport - 性能报告回调
 * @param {boolean} options.logToConsole - 是否输出到控制台
 */
export const usePerformanceMonitor = ({
    enabled = false,
    interval = 2000,
    onReport = null,
    logToConsole = false
} = {}) => {
    const frameCountRef = useRef(0);
    const lastTimeRef = useRef(performance.now());
    const fpsRef = useRef(60);
    const animationFrameRef = useRef(null);
    const reportIntervalRef = useRef(null);
    const paintCountRef = useRef(0);
    const layoutCountRef = useRef(0);

    // 🚀 FPS计算函数
    const calculateFPS = useCallback(() => {
        const now = performance.now();
        frameCountRef.current++;
        
        if (now - lastTimeRef.current >= 1000) {
            fpsRef.current = Math.round((frameCountRef.current * 1000) / (now - lastTimeRef.current));
            frameCountRef.current = 0;
            lastTimeRef.current = now;
        }
        
        if (enabled) {
            animationFrameRef.current = requestAnimationFrame(calculateFPS);
        }
    }, [enabled]);

    // 🚀 内存使用监控
    const getMemoryUsage = useCallback(() => {
        if (performance.memory) {
            return {
                used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
            };
        }
        return null;
    }, []);

    // 🚀 性能观察器设置
    const setupPerformanceObservers = useCallback(() => {
        if (!window.PerformanceObserver) return;

        // 监控渲染性能
        try {
            const paintObserver = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach(entry => {
                    if (entry.entryType === 'paint') {
                        paintCountRef.current++;
                    }
                    if (entry.entryType === 'layout-shift') {
                        layoutCountRef.current++;
                    }
                });
            });
            
            paintObserver.observe({ entryTypes: ['paint', 'layout-shift'] });
            
            return () => {
                paintObserver.disconnect();
            };
        } catch (e) {
            console.warn('Performance observer not supported:', e);
        }
    }, []);

    // 🚀 生成性能报告
    const generateReport = useCallback(() => {
        const memory = getMemoryUsage();
        const report = {
            timestamp: new Date().toISOString(),
            fps: fpsRef.current,
            memory,
            paintCount: paintCountRef.current,
            layoutShiftCount: layoutCountRef.current,
            // 性能警告
            warnings: []
        };

        // 性能预警
        if (fpsRef.current < 30) {
            report.warnings.push('FPS过低，可能存在性能瓶颈');
        }
        if (memory && memory.used > memory.limit * 0.8) {
            report.warnings.push('内存使用过高，可能存在内存泄漏');
        }
        if (paintCountRef.current > 100) {
            report.warnings.push('重绘次数过多，建议优化CSS动画');
        }
        if (layoutCountRef.current > 50) {
            report.warnings.push('布局偏移过多，建议检查DOM操作');
        }

        // 重置计数器
        paintCountRef.current = 0;
        layoutCountRef.current = 0;

        if (logToConsole) {
            console.group('🚀 性能监控报告');
            console.log('FPS:', report.fps);
            console.log('内存使用:', memory ? `${memory.used}MB / ${memory.limit}MB` : '不支持');
            console.log('重绘次数:', report.paintCount);
            console.log('布局偏移:', report.layoutShiftCount);
            if (report.warnings.length > 0) {
                console.warn('⚠️ 性能警告:', report.warnings);
            }
            console.groupEnd();
        }

        if (onReport) {
            onReport(report);
        }

        return report;
    }, [getMemoryUsage, onReport, logToConsole]);

    // 🚀 启动监控
    useEffect(() => {
        if (!enabled) return;

        // 启动FPS监控
        animationFrameRef.current = requestAnimationFrame(calculateFPS);

        // 设置性能观察器
        const cleanupObservers = setupPerformanceObservers();

        // 定期生成报告
        reportIntervalRef.current = setInterval(generateReport, interval);

        return () => {
            // 清理FPS监控
            if (animationFrameRef.current) {
                cancelAnimationFrame(animationFrameRef.current);
            }

            // 清理报告定时器
            if (reportIntervalRef.current) {
                clearInterval(reportIntervalRef.current);
            }

            // 清理性能观察器
            if (cleanupObservers) {
                cleanupObservers();
            }
        };
    }, [enabled, interval, calculateFPS, setupPerformanceObservers, generateReport]);

    // 手动获取当前性能状态
    const getCurrentPerformance = useCallback(() => {
        return {
            fps: fpsRef.current,
            memory: getMemoryUsage(),
            paintCount: paintCountRef.current,
            layoutShiftCount: layoutCountRef.current
        };
    }, [getMemoryUsage]);

    return {
        getCurrentPerformance,
        generateReport
    };
};

/**
 * 🚀 组件渲染时间监控Hook
 * 监控React组件的渲染时间
 */
export const useRenderTimeMonitor = (componentName = 'Component') => {
    const renderStartRef = useRef(null);

    useEffect(() => {
        renderStartRef.current = performance.now();
        
        return () => {
            if (renderStartRef.current) {
                const renderTime = performance.now() - renderStartRef.current;
                if (renderTime > 16) { // 超过16ms（60fps阈值）记录警告
                    console.warn(`⚠️ ${componentName} 渲染时间过长: ${renderTime.toFixed(2)}ms`);
                }
            }
        };
    });
};

export default usePerformanceMonitor; 