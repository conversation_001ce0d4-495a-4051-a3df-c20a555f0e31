import React, { createContext, useContext, useState, useRef } from 'react';

// 创建拖拽上下文
const DragContext = createContext();

// 拖拽项目类型
export const DRAG_TYPES = {
    DEVICE: 'DEVICE'
};

// 从localStorage加载独立设备数据
const loadIndependentDevicesFromStorage = () => {
    try {
        const saved = localStorage.getItem('independent-devices');
        if (saved) {
            const parsedDevices = JSON.parse(saved);
            // 验证数据结构的有效性
            if (Array.isArray(parsedDevices)) {
                return parsedDevices;
            }
        }
    } catch (error) {
        console.error('加载独立设备缓存失败:', error);
    }
    return [];
};

// 保存独立设备数据到localStorage
const saveIndependentDevicesToStorage = (devices) => {
    try {
        localStorage.setItem('independent-devices', JSON.stringify(devices));
        // console.log('✅ 独立设备数据已保存到缓存'); // 已注释掉，减少控制台输出
    } catch (error) {
        console.error('保存独立设备缓存失败:', error);
    }
};

// 拖拽上下文提供者
export const DragContextProvider = ({ children }) => {
    const [draggedDevice, setDraggedDevice] = useState(null);
    // 改为按容器ID分组存储设备，使用懒初始化从localStorage加载数据
    const [droppedDevicesMap, setDroppedDevicesMap] = useState(() => {
        const initialMap = new Map();
        const savedDevices = loadIndependentDevicesFromStorage();
        if (savedDevices.length > 0) {
            initialMap.set('independentDevices', savedDevices);
        }
        return initialMap;
    });
    // 预览设备（拖拽悬停时的临时设备）
    const [previewDevice, setPreviewDevice] = useState(null);
    // 实时拖拽设备（参与网格布局计算）
    const [liveGridDevice, setLiveGridDevice] = useState(null);
    // 网格布局引用
    const gridLayoutRef = useRef(null);
    // 拖拽监控器引用
    const dragMonitorRef = useRef(null);

    // 设置被拖拽的设备
    const setDraggedDeviceData = (deviceData) => {
        setDraggedDevice(deviceData);
    };

    // 清除被拖拽的设备
    const clearDraggedDevice = () => {
        setDraggedDevice(null);
        setLiveGridDevice(null);
        setPreviewDevice(null);
    };

    // 开始实时网格拖拽
    const startLiveGridDrag = (deviceData, monitor) => {
        const liveDevice = {
            ...deviceData,
            id: `live-${deviceData.id}-${Date.now()}`,
            droppedAt: Date.now(),
            gridLayoutItem: {
                i: `live-${deviceData.id}-${Date.now()}`,
                x: 0,
                y: 0,
                w: 3,
                h: 2,
                isDraggable: true,
                isResizable: true,
                isBeingDragged: true // 标记为正在拖拽
            }
        };
        
        setLiveGridDevice(liveDevice);
        dragMonitorRef.current = monitor;
        
        return liveDevice;
    };

    // 更新实时拖拽位置
    const updateLiveGridPosition = (newLayout) => {
        if (liveGridDevice && newLayout) {
            const updatedItem = newLayout.find(item => item.i === liveGridDevice.gridLayoutItem.i);
            if (updatedItem) {
                setLiveGridDevice(prev => ({
                    ...prev,
                    gridLayoutItem: {
                        ...prev.gridLayoutItem,
                        ...updatedItem
                    }
                }));
            }
        }
    };

    // 结束实时网格拖拽 - 确认放置
    const finalizeLiveGridDrag = () => {
        if (liveGridDevice) {
            const finalDevice = {
                ...liveGridDevice,
                id: `dropped-${liveGridDevice.id.split('-')[1]}-${Date.now()}`,
                gridLayoutItem: {
                    ...liveGridDevice.gridLayoutItem,
                    i: `dropped-${liveGridDevice.id.split('-')[1]}-${Date.now()}`,
                    isBeingDragged: false
                }
            };
            
            // 将设备添加到独立设备列表
            setDroppedDevicesMap(prev => {
                const newMap = new Map(prev);
                const independentDevices = newMap.get('independentDevices') || [];
                const updatedDevices = [...independentDevices, finalDevice];
                newMap.set('independentDevices', updatedDevices);
                
                // 保存到localStorage
                saveIndependentDevicesToStorage(updatedDevices);
                
                return newMap;
            });
            
            setLiveGridDevice(null);
            dragMonitorRef.current = null;
            
            return finalDevice;
        }
        return null;
    };

    // 取消实时网格拖拽
    const cancelLiveGridDrag = () => {
        setLiveGridDevice(null);
        dragMonitorRef.current = null;
    };

    // 更新独立设备的布局
    const updateIndependentDeviceLayout = (deviceLayouts) => {
        setDroppedDevicesMap(prev => {
            const newMap = new Map(prev);
            const independentDevices = [...(newMap.get('independentDevices') || [])];
    
            let changed = false;
            const updatedDevices = independentDevices.map(device => {
                const layoutUpdate = deviceLayouts.find(l => l.i === device.gridLayoutItem.i);
                if (layoutUpdate) {
                    changed = true;
                    // 合并现有布局和更新，确保保留如 isDraggable 等未变属性
                    const newGridLayoutItem = { ...device.gridLayoutItem, ...layoutUpdate };
                    return {
                        ...device,
                        gridLayoutItem: newGridLayoutItem,
                    };
                }
                return device;
            });
    
            if (changed) {
                newMap.set('independentDevices', updatedDevices);
                
                // 保存到localStorage
                saveIndependentDevicesToStorage(updatedDevices);
                
                return newMap;
            }
            return prev;
        });
    };

    // 添加放置的设备作为独立容器（保留兼容性）
    const addDroppedDevice = (deviceData, gridLayoutItem) => {
        const newDevice = {
            ...deviceData,
            id: `dropped-${deviceData.id}-${Date.now()}`, // 确保唯一ID
            droppedAt: Date.now(),
            gridLayoutItem: {
                ...gridLayoutItem, // 使用传入的布局
                i: `dropped-${deviceData.id}-${Date.now()}`, // 覆盖 'i' 以确保唯一性
                isDraggable: true,
                isResizable: true
            }
        };
        
        // 将设备添加到独立设备列表
        setDroppedDevicesMap(prev => {
            const newMap = new Map(prev);
            const independentDevices = newMap.get('independentDevices') || [];
            const updatedDevices = [...independentDevices, newDevice];
            newMap.set('independentDevices', updatedDevices);
            
            // 保存到localStorage
            saveIndependentDevicesToStorage(updatedDevices);
            
            return newMap;
        });
        
        return newDevice;
    };

    // 移除放置的设备
    const removeDroppedDevice = (deviceId) => {
        setDroppedDevicesMap(prev => {
            const newMap = new Map(prev);
            const independentDevices = newMap.get('independentDevices') || [];
            const filteredDevices = independentDevices.filter(device => device.id !== deviceId);
            
            if (filteredDevices.length > 0) {
                newMap.set('independentDevices', filteredDevices);
            } else {
                newMap.delete('independentDevices');
            }
            
            // 保存到localStorage
            saveIndependentDevicesToStorage(filteredDevices);
            
            return newMap;
        });
    };

    // 设置预览设备
    const setPreviewDeviceData = (deviceData) => {
        if (deviceData) {
            const previewDeviceData = {
                ...deviceData,
                id: `preview-${deviceData.id}`,
                droppedAt: Date.now(),
                gridLayoutItem: {
                    i: `preview-${deviceData.id}`,
                    x: 0,
                    y: 0,
                    w: 3,
                    h: 2,
                    isDraggable: true,
                    isResizable: true
                }
            };
            setPreviewDevice(previewDeviceData);
        } else {
            setPreviewDevice(null);
        }
    };

    // 获取独立设备列表（包括实时拖拽设备和预览设备）
    const getIndependentDevices = () => {
        const devices = droppedDevicesMap.get('independentDevices') || [];
        const result = [...devices];
        
        // 添加实时拖拽设备（优先级最高）
        if (liveGridDevice) {
            result.push(liveGridDevice);
        }
        // 如果没有实时拖拽设备，添加预览设备
        else if (previewDevice) {
            result.push(previewDevice);
        }
        
        return result;
    };

    // 获取指定容器的设备列表（保留原有功能）
    const getContainerDevices = (containerId = 'default') => {
        return droppedDevicesMap.get(containerId) || [];
    };

    // 获取所有设备（兼容性）
    const getAllDroppedDevices = () => {
        const allDevices = [];
        droppedDevicesMap.forEach(devices => {
            allDevices.push(...devices);
        });
        return allDevices;
    };

    const value = {
        draggedDevice,
        droppedDevices: getAllDroppedDevices(), // 保持向后兼容
        droppedDevicesMap,
        previewDevice,
        liveGridDevice,
        gridLayoutRef,
        setDraggedDeviceData,
        clearDraggedDevice,
        addDroppedDevice,
        removeDroppedDevice,
        getContainerDevices,
        getIndependentDevices,
        setPreviewDeviceData,
        // 新增实时网格拖拽功能
        startLiveGridDrag,
        updateLiveGridPosition,
        finalizeLiveGridDrag,
        cancelLiveGridDrag,
        updateIndependentDeviceLayout
    };

    return (
        <DragContext.Provider value={value}>
            {children}
        </DragContext.Provider>
    );
};

// 使用拖拽上下文的Hook
export const useDragContext = () => {
    const context = useContext(DragContext);
    if (!context) {
        throw new Error('useDragContext must be used within a DragContextProvider');
    }
    return context;
}; 