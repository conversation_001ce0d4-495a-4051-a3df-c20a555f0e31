import React, { memo, useRef, useState, useEffect, useCallback, useMemo } from 'react';
import { gsap } from 'gsap';

/**
 * 打字机效果组件
 * 逐字显示文本内容
 */
const TypewriterText = memo(({ text, speed = 50, onComplete }) => {
    const [displayText, setDisplayText] = useState('');
    const [currentIndex, setCurrentIndex] = useState(0);
    const [isTyping, setIsTyping] = useState(true);

    // 当新文本传入时重置状态
    useEffect(() => {
        setDisplayText('');
        setCurrentIndex(0);
        setIsTyping(true);
    }, [text]);

    // 打字机效果逻辑
    useEffect(() => {
        if (isTyping && currentIndex < text.length) {
            // 检查页面是否可见，如果不可见则跳过动画直接显示全部文本
            if (document.hidden) {
                setDisplayText(text);
                setCurrentIndex(text.length);
                setIsTyping(false);
                if (onComplete) {
                    onComplete();
                }
                return;
            }
            
            const timer = setTimeout(() => {
                setDisplayText(prev => prev + text[currentIndex]);
                setCurrentIndex(prev => prev + 1);
            }, speed);
            return () => clearTimeout(timer);
        } else if (currentIndex >= text.length && isTyping) {
            setIsTyping(false);
            if (onComplete) {
                onComplete();
            }
        }
    }, [currentIndex, text, speed, onComplete, isTyping]);

    return <span>{displayText}</span>;
});

TypewriterText.displayName = 'TypewriterText';

// 将辅助函数移到组件外部，避免在每次渲染时重新创建
// 获取当前时间字符串
const getCurrentTimeString = () => {
    const now = new Date();
    return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
};

// 生成随机ID
const generateId = () => `msg_${Date.now()}_${Math.floor(Math.random() * 1000)}`;

// 将十六进制颜色转换为rgba格式
function hexToRgba(hex, alpha = 1) {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
}

// 创建闪烁动画的辅助函数
const createFlickerAnimation = (timeline, target, flickerSequence) => {
    flickerSequence.forEach(([autoAlpha, duration]) => {
        timeline.to(target, { autoAlpha, duration, ease: "steps(1)" });
    });
};

/**
 * Socket 控制台组件
 * 用于显示通过 WebSocket 连接到服务器后的通信数据信息
 */
const SocketConsole = memo(() => {
    // 主题色配置 - 方便随时修改
    const themeColors = useMemo(() => ({ // 使用 useMemo 使其稳定，尽管当前是常量
        serverColor: '#6DE99C', // 服务器消息主题色（绿色）
        localColor: '#31A5BD',  // 本地消息主题色（蓝色）
    }), []);

    // 添加状态管理
    const [isLoading, setIsLoading] = useState(false); // 默认不加载
    const [connected, setConnected] = useState(false); // 连接状态
    const [messages, setMessages] = useState([]); // 消息列表
    const [isDataFlashing, setIsDataFlashing] = useState(false); // 数据读取闪烁状态
    const [autoDataExchange, setAutoDataExchange] = useState(false); // 新增：控制是否自动数据交换
    // 添加按钮选中状态
    const [activeButton, setActiveButton] = useState('INFO'); // 默认选中INFO按钮
    // 添加标题文字状态
    const [titleText, setTitleText] = useState(''); // 标题文字内容，初始为空
    const [isInitialTitleAnimationDone, setIsInitialTitleAnimationDone] = useState(false); // 新增：标记初始动画是否完成
    const [isDeletingTitle, setIsDeletingTitle] = useState(false); // 新增：标记是否正在执行标题删除动画
    const [titleAnimationId, setTitleAnimationId] = useState(0); // 新增：用于强制刷新标题打字动画的key
    // 新增：页面焦点状态管理
    const [isPageVisible, setIsPageVisible] = useState(!document.hidden); // 页面是否可见
    const titleTextRef = useRef(titleText); // 用于在回调中获取最新的 titleText
    useEffect(() => {
        titleTextRef.current = titleText;
    }, [titleText]);

    // 新增：消息队列相关状态
    const [messageQueue, setMessageQueue] = useState([]); // 消息队列
    const messageQueueRef = useRef(messageQueue); // 用于在回调中获取最新 messageQueue
    useEffect(() => {
        messageQueueRef.current = messageQueue;
    }, [messageQueue]);

    const [isProcessingQueue, setIsProcessingQueue] = useState(false); // 是否正在处理队列
    const [currentTypingMessage, setCurrentTypingMessage] = useState(null); // 当前正在打字的消息ID

    // 标题栏相关状态和引用
    const [titleBarVisible, setTitleBarVisible] = useState(false);
    const titleBarContainerRef = useRef(null);
    const titleBarTextAreaRef = useRef(null);

    // 列表标题栏相关状态和引用
    const [listHeaderVisible, setListHeaderVisible] = useState(false);
    const listHeaderRef = useRef(null);

    // 🆕 新增：底部状态栏相关状态和引用
    const [bottomStatusBarVisible, setBottomStatusBarVisible] = useState(false);
    const bottomStatusBarRef = useRef(null);

    // 创建滚动区域的引用，用于自动滚动
    const scrollAreaRef = useRef(null);
    // 消息容器引用，用于检测内容高度
    const messagesContainerRef = useRef(null);
    // 用于存储闪烁定时器的引用
    const dataFlashTimerRef = useRef(null);
    // 队列处理定时器引用
    const queueTimerRef = useRef(null);

    // 滚动条显示控制状态
    const [showScrollbar, setShowScrollbar] = useState(false);

    // 触发数据读取闪烁效果
    const triggerDataFlash = useCallback(() => {
        // 如果有正在进行的闪烁，先清除
        if (dataFlashTimerRef.current) {
            clearTimeout(dataFlashTimerRef.current);
        }

        // 开始新的闪烁
        setIsDataFlashing(true);

        // 设置闪烁持续时间（1.2秒）
        dataFlashTimerRef.current = setTimeout(() => {
            setIsDataFlashing(false);
            dataFlashTimerRef.current = null;
        }, 1200);
    }, []); // 依赖项为空，因为 setIsDataFlashing 和 dataFlashTimerRef 是稳定的

    // 添加消息到队列 - 优化：支持消息数量限制和性能优化
    const addMessageToQueue = useCallback((type, message, isFromServer) => {
        const newMessage = {
            id: generateId(), // generateId 现在是外部函数
            type,
            message,
            time: getCurrentTimeString(), // getCurrentTimeString 现在是外部函数
            isFromServer,
            isTyping: true // 添加打字状态标识
        };

        // 将消息添加到队列
        setMessageQueue(prevQueue => [...prevQueue, newMessage]);
        
        // 限制已显示消息的数量，防止过多消息影响性能和动画效果
        setMessages(prevMessages => {
            const MAX_MESSAGES = 50; // 最大消息数量
            if (prevMessages.length >= MAX_MESSAGES) {
                // 当消息数量达到上限时，移除最早的消息，为新消息腾出空间
                return prevMessages.slice(-(MAX_MESSAGES - 1));
            }
            return prevMessages;
        });
    }, []); // 依赖项为空，因为 setMessageQueue, generateId, getCurrentTimeString 是稳定的

    // 队列处理逻辑 (主 useEffect) - 应用增强的占位符策略
    useEffect(() => {
        if (messageQueue.length > 0 && !isProcessingQueue) {
            setIsProcessingQueue(true); // 标记为正在处理

            const messageToProcess = messageQueue[0]; // 获取原始消息

            const runAsyncProcessing = async () => {
                // 步骤 1: 创建占位符消息并添加到显示列表
                const placeholderMessage = {
                    ...messageToProcess, // 继承ID, type, isFromServer, time等
                    message: '',          // 内容为空
                    isTyping: false,      // 非打字状态
                    isPlaceholder: true,  // 标记为占位符
                };
                setMessages(prevMessages => [...prevMessages, placeholderMessage]);
                setMessageQueue(prevQueue => prevQueue.slice(1)); // 从待处理队列移除

                // 步骤 2: 等待DOM更新（占位符已渲染），然后平滑滚动列表
                await new Promise(resolve => requestAnimationFrame(resolve)); // 确保占位符已影响scrollHeight

                if (scrollAreaRef.current) {
                    const scrollContainer = scrollAreaRef.current;
                    const targetScrollTop = scrollContainer.scrollHeight - scrollContainer.clientHeight;

                    // 缩短滚动动画时间，提高响应速度
                    await new Promise(resolveOnAnimationComplete => { // 等待GSAP动画完成
                        gsap.to(scrollContainer, {
                            scrollTop: Math.max(0, targetScrollTop), // 处理内容不足一页的情况
                            duration: 0.15, // 从0.3秒减少到0.15秒
                            ease: "power2.out",
                            onComplete: resolveOnAnimationComplete,
                        });
                    });
                }

                // 步骤 3: 滚动动画完成，用真实消息替换占位符消息
                setMessages(prevMessages =>
                    prevMessages.map(msg =>
                        msg.id === messageToProcess.id && msg.isPlaceholder
                            ? { ...messageToProcess, isTyping: true, isPlaceholder: undefined } // 真实消息，开始打字, 移除placeholder标记
                            : msg
                    )
                );

                setCurrentTypingMessage(messageToProcess.id);
                triggerDataFlash();

                // 步骤 4: DOM更新后 (真实消息已渲染), 进行最终的scrollTop精确校准
                requestAnimationFrame(() => {
                    if (scrollAreaRef.current) {
                        const scrollContainer = scrollAreaRef.current;
                        const finalScrollTo = scrollContainer.scrollHeight - scrollContainer.clientHeight;
                        // 只有在当前scrollTop与理论最终值有差异时才强制设置，避免不必要的DOM操作
                        if (scrollContainer.scrollTop !== finalScrollTo) {
                            scrollContainer.scrollTop = Math.max(0, finalScrollTo);
                        }
                    }
                });
                // isProcessingQueue 保持为 true, handleTypingComplete 将其设为 false 以允许处理下一条
            };

            runAsyncProcessing();
        }
    }, [messageQueue, isProcessingQueue, setMessageQueue, setMessages, setCurrentTypingMessage, triggerDataFlash, setIsProcessingQueue]); // 移除了prepareVisualSpace

    // 消息打字完成回调
    const handleTypingComplete = useCallback((messageId) => {
        setMessages(prevMessages =>
            prevMessages.map(msg =>
                msg.id === messageId ? { ...msg, isTyping: false } : msg
            )
        );

        // 如果当前完成打字的消息是我们正在跟踪的
        if (messageId === currentTypingMessage) {
            setCurrentTypingMessage(null);

            // 减少延迟时间，加快消息处理速度，确保动画效果不会丢失
            queueTimerRef.current = setTimeout(() => {
                setIsProcessingQueue(false); // 解除锁定
            }, 100); // 从500ms减少到100ms，提高处理速度
        }
    }, [currentTypingMessage, setMessages, setCurrentTypingMessage, setIsProcessingQueue, queueTimerRef]); // queueTimerRef is stable

    // 兼容原来的addMessage函数名，现在指向队列版本
    const addMessage = addMessageToQueue;

    // 标题栏动画函数 - 修改为现有状态栏的动画
    const initializeTitleBar = useCallback(() => {
        if (!gsap || !titleBarContainerRef.current || !titleBarTextAreaRef.current) {
            console.error('[SocketConsole] GSAP or refs not found for title bar animation.');
            return gsap.timeline(); // 返回空时间线以避免错误
        }

        const titleBar = titleBarContainerRef.current;
        const textArea = titleBarTextAreaRef.current;

        // 初始化元素状态
        gsap.set(titleBar, { scaleX: 0, transformOrigin: "center", autoAlpha: 0 });
        gsap.set(textArea, { autoAlpha: 0 });

        const tl = gsap.timeline({
            onStart: () => setTitleBarVisible(true), // 动画开始时设置为可见
        });

        const mainFlickerSequence = [
            [0.2, 0.04], [0.8, 0.03], [0.1, 0.05], [0.9, 0.02], [0.3, 0.04],
            [0.7, 0.03], [0.15, 0.04], [0.85, 0.02], [0.4, 0.03], [0.9, 0.03],
            [0.25, 0.04], [1, 0.08]
        ];

        const duringFlickerSequence = [
            [0.6, 0.03, 0.2], [1, 0.02, 0.23], [0.7, 0.04, 0.3], [1, 0.03, 0.34],
            [0.8, 0.02, 0.4], [1, 0.02, 0.42], [0.85, 0.03, 0.5], [1, 0.03, 0.53]
        ];

        // 容器扩散动画
        tl.to(titleBar, {
            scaleX: 1,
            duration: 0.8,
            ease: "power2.out"
        }, 0);

        // 初始闪烁
        const flickerTl = gsap.timeline();
        createFlickerAnimation(flickerTl, titleBar, mainFlickerSequence);
        tl.add(flickerTl, 0);

        // 扩散过程中的闪烁
        duringFlickerSequence.forEach(([autoAlpha, duration, time]) => {
            tl.to(titleBar, { autoAlpha, duration, ease: "steps(1)" }, time);
        });
        
        // 淡入效果
        tl.fromTo(titleBar,
            { opacity: 0.3 },
            { opacity: 1, duration: 0.4, ease: "power1.out" },
            0.5
        );
        
        return tl; // 返回创建的时间线

    }, [setTitleBarVisible]); // 依赖是稳定的

    // 列表标题栏初始化动画函数
    const initializeListHeader = useCallback(() => {
        if (!gsap || !listHeaderRef.current) {
            console.error('[SocketConsole] Cannot initialize list header animation');
            return gsap.timeline();
        }

        const listHeader = listHeaderRef.current;
        gsap.set(listHeader, { scaleY: 0, transformOrigin: "top", autoAlpha: 0 });

        const tl = gsap.timeline({ onStart: () => setListHeaderVisible(true) });

        const mainFlickerSequence = [
            [0.3, 0.05], [0.7, 0.04], [0.2, 0.06], [0.8, 0.03], [0.4, 0.04],
            [0.9, 0.03], [0.25, 0.05], [0.85, 0.04], [0.5, 0.03], [0.95, 0.03],
            [0.3, 0.04], [0.75, 0.03], [0.15, 0.04], [0.9, 0.02], [0.4, 0.04],
            [1, 0.08]
        ];
        const duringFlickerSequence = [
            [0.6, 0.03, 0.2], [0.95, 0.02, 0.23], [0.4, 0.04, 0.3], [0.9, 0.03, 0.34],
            [0.3, 0.03, 0.4], [0.8, 0.02, 0.42], [0.5, 0.04, 0.5], [1, 0.03, 0.54],
            [0.7, 0.05, 0.65], [0.95, 0.03, 0.7], [0.5, 0.04, 0.8], [1, 0.03, 0.84]
        ];
        const finalFlickerSequence = [
            [0.6, 0.04, 1.05], [1, 0.03, 1.09], [0.8, 0.03, 1.15],
            [1, 0.02, 1.18], [0.9, 0.02, 1.24], [1, 0.02, 1.26]
        ];

        // 高度扩散
        tl.to(listHeader, { scaleY: 1, duration: 1.0, ease: "power2.out" }, 0);
        
        // 初始闪烁
        const flickerTl = gsap.timeline();
        createFlickerAnimation(flickerTl, listHeader, mainFlickerSequence);
        tl.add(flickerTl, 0);

        // 扩散中和扩散后闪烁
        [...duringFlickerSequence, ...finalFlickerSequence].forEach(([autoAlpha, duration, time]) => {
            tl.to(listHeader, { autoAlpha, duration, ease: "steps(1)" }, time);
        });

        // 淡入效果
        tl.fromTo(listHeader, { opacity: 0.4 }, { opacity: 1, duration: 0.3, ease: "power1.out" }, 0.5);

        return tl;

    }, [setListHeaderVisible]); // 依赖是稳定的



    // 底部状态栏初始化动画函数
    const initializeBottomStatusBar = useCallback(() => {
        if (!gsap || !bottomStatusBarRef.current) {
            console.error('[SocketConsole] Cannot initialize bottom status bar animation');
            return gsap.timeline();
        }

        const bottomStatusBar = bottomStatusBarRef.current;
        gsap.set(bottomStatusBar, { scaleY: 0, transformOrigin: "bottom", autoAlpha: 0 });

        const tl = gsap.timeline({ onStart: () => setBottomStatusBarVisible(true) });

        // 从底部向上快速扩散
        tl.to(bottomStatusBar, {
            scaleY: 1,
            autoAlpha: 1,
            duration: 0.4,
            ease: "power2.out"
        })
        // 同时开始闪烁效果
        .fromTo(bottomStatusBar, 
            { opacity: 1 },
            {
                opacity: 0.3,
                duration: 0.07,
                repeat: 5, // 6次闪烁
                yoyo: true,
                ease: "steps(1)"
            }, '<') // 与扩散同时开始
        // 最后确保完全显示
        .set(bottomStatusBar, {
            opacity: 1,
            scaleY: 1
        });
        
        return tl;
        
    }, [setBottomStatusBarVisible]); // 依赖是稳定的

    // 检查是否需要显示滚动条
    const checkScrollbarVisibility = useCallback(() => {
        if (scrollAreaRef.current && messagesContainerRef.current) {
            const containerHeight = messagesContainerRef.current.clientHeight;
            const scrollAreaHeight = scrollAreaRef.current.clientHeight;

            // 修改逻辑：只有在有消息且内容超过可视区域时才显示滚动条
            const shouldShowScrollbar = messages.length > 0 && containerHeight > scrollAreaHeight;
            setShowScrollbar(shouldShowScrollbar);
        }
    }, [messages.length]); // 依赖 messages.length

    // 消息列表变化时检查滚动条
    useEffect(() => {
        // 使用setTimeout确保DOM已更新
        setTimeout(() => {
            checkScrollbarVisibility();
        }, 0);
    }, [messages, checkScrollbarVisibility]); // 添加 checkScrollbarVisibility 依赖

    // 组件挂载时确保初始状态不显示滚动条
    useEffect(() => {
        // 初始状态下强制不显示滚动条
        setShowScrollbar(false);
    }, []);

    // 窗口尺寸变化时也需要检查
    useEffect(() => {
        const handleResize = () => {
            checkScrollbarVisibility();
        };

        window.addEventListener('resize', handleResize);
        return () => {
            window.removeEventListener('resize', handleResize);
        };
    }, [checkScrollbarVisibility]); // 添加 checkScrollbarVisibility 依赖



    // 组件加载和初始化动画序列
    useEffect(() => {
        const tl = gsap.timeline();
        // 依次执行标题栏、列表头、底部状态栏的入场动画
        tl.add(initializeTitleBar(), 0.5)
          .add(initializeListHeader(), 1.5) 
          .add(initializeBottomStatusBar(), 1.5) // 🆕 新增：与列表头同时开始

          // 所有容器动画完成后，显示标题文字
          .call(() => {
              if (titleBarTextAreaRef.current) {
                  gsap.set(titleBarTextAreaRef.current, { autoAlpha: 1 });
              }
              setTitleAnimationId(id => id + 1);
              setTitleText('正在连接服务器');
          }, [], 3.5);

    }, [initializeTitleBar, initializeListHeader, initializeBottomStatusBar]);

    // 打字删除动画并显示新IP地址 - GSAP版本
    const changeToIpAddress = useCallback((newTargetText) => {
        setIsDeletingTitle(true);
        const textToDelete = titleTextRef.current || '';

        // 如果没有可删除的文本，直接设置新文本
        if (textToDelete.length === 0) {
            setTitleAnimationId(prevId => prevId + 1);
            setTitleText(newTargetText);
            setIsDeletingTitle(false);
            return;
        }

        const deleteProxy = { index: textToDelete.length };

        // 使用GSAP的tween来模拟删除过程
        gsap.to(deleteProxy, {
            index: 0,
            duration: textToDelete.length * 0.06, // 60ms/字符
            ease: `steps(${textToDelete.length})`,
            onUpdate: () => {
                setTitleText(textToDelete.substring(0, Math.floor(deleteProxy.index)));
            },
            onComplete: () => {
                setTitleText('');
                setIsDeletingTitle(false);
                // 删除完成后，延迟一小段时间再开始打印新文本
                setTimeout(() => {
                    setTitleAnimationId(prevId => prevId + 1);
                    setTitleText(newTargetText);
                }, 50);
            }
        });
    }, [setIsDeletingTitle, setTitleText, setTitleAnimationId]); // 依赖都是稳定函数

    const handleInitialTitleComplete = useCallback(() => {
        if (isInitialTitleAnimationDone) return;
        setIsInitialTitleAnimationDone(true);

        // 使用GSAP时间线来管理后续状态变化
        gsap.timeline()
            .to({}, { // 等待2秒
                duration: 2,
                onComplete: () => setIsLoading(true)
            })
            .to({}, { // 再等待2秒，模拟加载
                duration: 2,
                onComplete: () => {
                    setIsLoading(false);
                    setConnected(true);
                    setAutoDataExchange(true);
                    changeToIpAddress('***********'); // 显示IP地址
                }
            });
    }, [isInitialTitleAnimationDone, changeToIpAddress, setIsLoading, setConnected, setAutoDataExchange, setIsInitialTitleAnimationDone]);

    // 模拟定时发送和接收数据
    useEffect(() => {
        if (!connected || !autoDataExchange || !isPageVisible) return; // 添加页面可见性检查

        const dataExchangeInterval = setInterval(() => {
            // 模拟发送数据
            addMessage('SEND', `发送心跳包`, false); // addMessage is memoized

            // 模拟延迟接收
            setTimeout(() => {
                // 随机选择接收消息类型
                const types = ['INFO', 'RECV', 'WARN'];
                const randomType = types[Math.floor(Math.random() * types.length)];
                const messagesContent = { // Renamed to avoid conflict
                    'INFO': `系统信息: 当前连接稳定`,
                    'RECV': `收到数据包，共${Math.floor(Math.random() * 100)}条记录`,
                    'WARN': `警告: 网络延迟${Math.floor(Math.random() * 200)}ms`
                };

                addMessage(randomType, messagesContent[randomType], true); // addMessage is memoized
            }, 500); // 模拟服务器0.5s后响应

        }, 5000); // 改为每5秒执行一次，平衡动画效果和数据更新频率

        return () => clearInterval(dataExchangeInterval);
    }, [connected, autoDataExchange, isPageVisible, addMessage]); // 添加 isPageVisible 依赖

    // 组件卸载时清理定时器
    useEffect(() => {
        return () => {
            if (dataFlashTimerRef.current) {
                clearTimeout(dataFlashTimerRef.current);
            }
            if (queueTimerRef.current) {
                clearTimeout(queueTimerRef.current);
            }
        };
    }, []);

    // 新增：监听页面可见性变化
    useEffect(() => {
        const handleVisibilityChange = () => {
            setIsPageVisible(!document.hidden);
        };

        // 监听页面可见性变化事件
        document.addEventListener('visibilitychange', handleVisibilityChange);
        
        // 同时监听窗口焦点事件作为备用
        window.addEventListener('focus', () => setIsPageVisible(true));
        window.addEventListener('blur', () => setIsPageVisible(false));

        return () => {
            document.removeEventListener('visibilitychange', handleVisibilityChange);
            window.removeEventListener('focus', () => setIsPageVisible(true));
            window.removeEventListener('blur', () => setIsPageVisible(false));
        };
    }, []);

    // 自定义滚动条样式 - 使用 useMemo 优化
    const scrollbarStyle = useMemo(() => `
    .custom-scrollbar::-webkit-scrollbar {
      width: 0px;
      display: block;
    }
    .custom-scrollbar::-webkit-scrollbar-track {
      /* background: #3A3C3F; */
      width: 1px;
    }
    .custom-scrollbar::-webkit-scrollbar-thumb {
      background-color: #B3BBBF;
      width: 1px;
    }
    
    /* 隐藏滚动条但保留滚动功能 */
    .hide-scrollbar {
      -ms-overflow-style: none;  /* IE and Edge */
      scrollbar-width: none;     /* Firefox */
    }
    
    .hide-scrollbar::-webkit-scrollbar {
      display: none;  /* Chrome, Safari, Opera */
      width: 0;
    }
    
    /* 聊天式布局，消息从底部开始显示 */
    .messages-container {
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      min-height: 100%;
      padding-bottom: 2px; /* 添加底部小间距 */
    }
    
    /* 消息项固定高度和简洁动画 */
    .message-item {
      height: 28px; /* 设置固定高度 */
      min-height: 28px; /* 确保最小高度 */
      opacity: 0;
      animation: messageSlideIn 0.2s ease-out forwards; /* 缩短动画时间 */
      display: flex;
      align-items: center;
      flex-shrink: 0; /* 防止压缩 */
    }
    
    @keyframes messageSlideIn {
      from {
        opacity: 0;
      }
      to {
        opacity: 1;
      }
    }
    
    /* 流动渐变边框所需的CSS */
    @property --border-angle {
      inherits: false;
      initial-value: 0deg;
      syntax: '<angle>';
    }
    
    @keyframes border-rotate {
      to {
        --border-angle: 360deg;
      }
    }
    
    /* 圆灯动画效果 */
    @keyframes fast-blink {
      0% { opacity: 1; box-shadow: 0 0 5px rgba(255, 0, 0, 0.7), 0 0 10px rgba(255, 0, 0, 0.5); }
      50% { opacity: 0.3; box-shadow: 0 0 2px rgba(255, 0, 0, 0.3), 0 0 5px rgba(255, 0, 0, 0.2); }
      100% { opacity: 1; box-shadow: 0 0 5px rgba(255, 0, 0, 0.7), 0 0 10px rgba(255, 0, 0, 0.5); }
    }
    
    @keyframes breath-light {
      0% { opacity: 0.6; box-shadow: 0 0 5px rgba(0, 255, 0, 0.4), 0 0 10px rgba(0, 255, 0, 0.2); }
      50% { opacity: 1; box-shadow: 0 0 8px rgba(0, 255, 0, 0.8), 0 0 15px rgba(0, 255, 0, 0.6), 0 0 20px rgba(0, 255, 0, 0.4); }
      100% { opacity: 0.6; box-shadow: 0 0 5px rgba(0, 255, 0, 0.4), 0 0 10px rgba(0, 255, 0, 0.2); }
    }
    
    /* 数据读取闪烁效果 - 类似老式电脑读盘灯 */
    @keyframes data-flash {
      0% { 
        opacity: 1; 
        box-shadow: 0 0 8px rgba(255, 165, 0, 0.9), 0 0 15px rgba(255, 165, 0, 0.7), 0 0 25px rgba(255, 165, 0, 0.5);
        background-color: #ffa500;
      }
      25% { 
        opacity: 0.2; 
        box-shadow: 0 0 3px rgba(255, 165, 0, 0.3), 0 0 6px rgba(255, 165, 0, 0.2);
        background-color: #ff8c00;
      }
      50% { 
        opacity: 1; 
        box-shadow: 0 0 10px rgba(255, 140, 0, 1), 0 0 20px rgba(255, 140, 0, 0.8), 0 0 30px rgba(255, 140, 0, 0.6);
        background-color: #ff8c00;
      }
      75% { 
        opacity: 0.3; 
        box-shadow: 0 0 4px rgba(255, 165, 0, 0.4), 0 0 8px rgba(255, 165, 0, 0.3);
        background-color: #ffa500;
      }
      100% { 
        opacity: 1; 
        box-shadow: 0 0 8px rgba(255, 165, 0, 0.9), 0 0 15px rgba(255, 165, 0, 0.7), 0 0 25px rgba(255, 165, 0, 0.5);
        background-color: #ffa500;
      }
    }
    
    .indicator-light {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background-color: #f00;
      margin-left: 8px;
      transition: all 0.1s ease;
    }
    
    .indicator-light.loading {
      animation: fast-blink 0.6s infinite;
    }
    
    .indicator-light.connected {
      background-color: #0f0;
      animation: breath-light 2s infinite ease-in-out;
    }
    
    /* 灰色默认状态 */
    .indicator-light.gray {
      background-color: #666;
      opacity: 0.6;
      box-shadow: 0 0 3px rgba(102, 102, 102, 0.3);
    }
    
    /* 数据读取闪烁状态 - 优先级最高 */
    .indicator-light.data-flashing {
      background-color: #ffa500;
      animation: data-flash 0.15s infinite ease-in-out !important;
    }
    
    .gradient-border-button {
      position: relative;
      background: rgba(10, 25, 35, 0.85);
    }
    
    .gradient-border-button::before {
      content: '';
      position: absolute;
      inset: -2px;
      padding: 2px;
      background: linear-gradient(
        var(--border-angle),
        ${themeColors.localColor}, #3283f8, #39EACC, ${themeColors.localColor}
      );
      -webkit-mask: 
        linear-gradient(#fff 0 0) content-box, 
        linear-gradient(#fff 0 0);
      -webkit-mask-composite: xor;
      mask-composite: exclude;
      animation: border-rotate 4s linear infinite;
      z-index: -1;
    }
    
    /* 三角形图标样式 */
    .triangle-up {
      width: 0;
      height: 0;
      border-left: 4px solid transparent;
      border-right: 4px solid transparent;
      border-bottom: 6px solid ${themeColors.localColor};
      margin-right: 5px;
      flex-shrink: 0; /* 防止图标被压缩 */
    }
    
    .triangle-down {
      width: 0;
      height: 0;
      border-left: 4px solid transparent;
      border-right: 4px solid transparent;
      border-top: 6px solid ${themeColors.serverColor};
      margin-right: 5px;
      flex-shrink: 0; /* 防止图标被压缩 */
    }
    
    /* 消息文字颜色 */
    .server-message {
      color: ${themeColors.serverColor}; /* 服务器消息使用绿色 */
    }
    
    .local-message {
      color: ${themeColors.localColor}; /* 本地消息使用蓝色 */
    }
    
    /* 消息行背景颜色（非常淡） */
    .bg-server {
      background-color: ${hexToRgba(themeColors.serverColor, 0.03)}; /* 非常淡的绿色背景，用于服务器消息 */
    }
    
    .bg-local {
      background-color: ${hexToRgba(themeColors.localColor, 0.03)}; /* 非常淡的蓝色背景，用于本地消息 */
    }
    
    /* 交替行颜色，稍微深一点点 */
    .row-alternate {
      background-color: rgba(24, 41, 51, 0.05); /* 非常淡的蓝黑色 */
    }
  `, [themeColors]);



    // 为 TypewriterText 的空 onComplete 创建一个稳定的回调
    const emptyOnComplete = useCallback(() => { }, []);

    return (
        <div
            className="w-[calc(100%-2px)] h-[calc(100%)] flex flex-col 
                 justify-start items-center font-['DingTalkJinBuTi'] text-base 
                 font-bold text-[#FFFFFF] overflow-hidden no-scrollbar relative"
        >
            <style>{scrollbarStyle}</style>

            {/* 固定区域 */}
            <div className="w-full flex-none">
                <div
                    ref={titleBarContainerRef}
                    className="w-full h-[30px] flex items-center justify-between text-[13px] px-4 border-b-1 border-[#82c09e]"
                >
                    {/* <span className={`ml-[30px] text-[12px] font-['ChakraPetch-Light']`}>
                        SOCKET
                    </span> */}
                    <span
                        ref={titleBarTextAreaRef}
                        className="transition-opacity ml-[28px] mb-[2px]"
                    >
                        {isDeletingTitle || !titleText ? (
                            <span>{titleText}</span>
                        ) : (
                            <TypewriterText
                                key={`typewriter-${titleAnimationId}`}
                                text={titleText}
                                speed={80}
                                onComplete={!isInitialTitleAnimationDone ? handleInitialTitleComplete : emptyOnComplete}
                            />
                        )}
                    </span>
                    {/* 状态指示灯 */}
                    <div className={`mr-[10px] indicator-light ${isDataFlashing ? 'data-flashing' :
                        isLoading ? 'loading' :
                            connected ? 'connected' : 'gray'
                        }`}></div>
                </div>

                {/* 列表标题容器 - 添加ref和初始高度为0的样式 */}
                <div
                    ref={listHeaderRef}
                    className="w-full h-[26px] flex items-center bg-[#111C23] text-[#9FF9F9] text-[11px] font-['ChakraPetch-Light'] border-b border-[rgba(90,170,180,0.05)] overflow-hidden"
                >
                    <div className="w-[80px] pl-[5px] flex items-center">TYPE</div>
                    <div className="flex-1 flex items-center">MESSAGE</div>
                    <div className="w-[60px] flex items-center">TIME</div>
                </div>
            </div>

            {/* 可滚动的数据列表区域 - 修改为聊天式布局，动态控制滚动条显示，底部留出状态栏空间 */}
            <div
                ref={scrollAreaRef}
                className={`w-full flex-1 overflow-y-auto overflow-x-hidden ${showScrollbar ? 'custom-scrollbar' : 'hide-scrollbar'}`}
                style={{ paddingBottom: '30px' }} // 给底部状态栏留出空间
            >
                {/* 消息容器，采用聊天式布局 */}
                <div ref={messagesContainerRef} className="messages-container">
                    {/* 动态消息列表 */}
                    {messages.map((msg, index) => (
                        <div
                            key={msg.id}
                            className={`w-full text-[12px] message-item
                        ${msg.isFromServer ? 'bg-server' : 'bg-local'} 
                        ${index % 2 === 1 ? 'row-alternate' : ''}`}
                        >
                            <div className="w-[80px] pl-[5px] text-[12px] font-['ChakraPetch-Light'] flex items-center flex-shrink-0">
                                {/* 根据消息来源显示不同的三角形图标 */}
                                <div className={msg.isFromServer ? 'triangle-down' : 'triangle-up'}></div>
                                <span className={msg.isFromServer ? 'server-message' : 'local-message'}>
                                    [{msg.type}]
                                </span>
                            </div>
                            <div className={`flex-1 text-left ${msg.isFromServer ? 'server-message' : 'local-message'} overflow-hidden`}>
                                {/* 使用打字机效果组件 */}
                                <TypewriterText
                                    text={msg.message}
                                    speed={20}
                                    onComplete={() => handleTypingComplete(msg.id)}
                                />
                            </div>
                            <div className="mr-[10px] w-[60px] text-[12px] font-['ChakraPetch-Light'] flex items-center justify-center flex-shrink-0">{msg.time}</div>
                        </div>
                    ))}
                </div>
            </div>



            {/* 🆕 新增：底部状态栏 */}
            <div 
                ref={bottomStatusBarRef}
                className="absolute bottom-[0px] left-[0px] right-[0px] h-[30px] bg-gradient-to-r from-[#1a1a1a]/50 via-[#31A5BD]/5 to-[#1a1a1a]/50 backdrop-blur-sm border-t border-[#31A5BD]/15"
            >
                <div className="flex items-center justify-start px-[10px] h-full">
                    {/* 🆕 按钮区域 - 左对齐显示 */}
                    <div className="flex items-center space-x-[12px]">
                        {/* INFO 按钮 */}
                        <button
                            id="btn-INFO"
                            className={`h-[22px] px-[12px] flex items-center justify-center cursor-pointer
                              font-['ChakraPetch-Light'] text-[11px] uppercase box-border transition-all
                              focus:outline-none border border-[#31A5BD]/30 hover:border-[#31A5BD]/60`}
                            style={{
                                backgroundColor: activeButton === 'INFO' ? 'rgba(49, 165, 189, 0.2)' : 'rgba(49, 165, 189, 0.05)',
                                color: activeButton === 'INFO' ? '#6DE99C' : '#31A5BD',
                                fontWeight: activeButton === 'INFO' ? 'bold' : 'normal',
                                borderRadius: '0' // 显式设置为直角
                            }}
                            onClick={() => {
                                setActiveButton('INFO');
                                addMessage('INFO', '用户手动触发信息', false);
                            }}
                        >
                            INFO
                        </button>

                        {/* SEND 按钮 */}
                        <button
                            id="btn-SEND"
                            className={`h-[22px] px-[12px] flex items-center justify-center cursor-pointer
                              font-['ChakraPetch-Light'] text-[11px] uppercase box-border transition-all
                              focus:outline-none border border-[#31A5BD]/30 hover:border-[#31A5BD]/60`}
                            style={{
                                backgroundColor: activeButton === 'SEND' ? 'rgba(49, 165, 189, 0.2)' : 'rgba(49, 165, 189, 0.05)',
                                color: activeButton === 'SEND' ? '#6DE99C' : '#31A5BD',
                                fontWeight: activeButton === 'SEND' ? 'bold' : 'normal',
                                borderRadius: '0' // 显式设置为直角
                            }}
                            onClick={() => {
                                setActiveButton('SEND');
                                addMessage('SEND', '用户手动发送数据', false);
                            }}
                        >
                            SEND
                        </button>

                        {/* RECV 按钮 */}
                        <button
                            id="btn-RECV"
                            className={`h-[22px] px-[12px] flex items-center justify-center cursor-pointer
                              font-['ChakraPetch-Light'] text-[11px] uppercase box-border transition-all
                              focus:outline-none border border-[#31A5BD]/30 hover:border-[#31A5BD]/60`}
                            style={{
                                backgroundColor: activeButton === 'RECV' ? 'rgba(49, 165, 189, 0.2)' : 'rgba(49, 165, 189, 0.05)',
                                color: activeButton === 'RECV' ? '#6DE99C' : '#31A5BD',
                                fontWeight: activeButton === 'RECV' ? 'bold' : 'normal',
                                borderRadius: '0' // 显式设置为直角
                            }}
                            onClick={() => {
                                setActiveButton('RECV');
                                addMessage('RECV', '模拟接收到数据', true);
                            }}
                        >
                            RECV
                        </button>

                        {/* BATCH 按钮 */}
                        <button
                            id="btn-BATCH"
                            className={`h-[22px] px-[12px] flex items-center justify-center cursor-pointer
                              font-['ChakraPetch-Light'] text-[11px] uppercase box-border transition-all
                              focus:outline-none border border-[#31A5BD]/30 hover:border-[#31A5BD]/60`}
                            style={{
                                backgroundColor: activeButton === 'BATCH' ? 'rgba(49, 165, 189, 0.2)' : 'rgba(49, 165, 189, 0.05)',
                                color: activeButton === 'BATCH' ? '#6DE99C' : '#31A5BD',
                                fontWeight: activeButton === 'BATCH' ? 'bold' : 'normal',
                                borderRadius: '0' // 显式设置为直角
                            }}
                            onClick={() => {
                                setActiveButton('BATCH');
                                // 快速添加多条消息测试队列
                                addMessage('SEND', '批量消息 1/5', false);
                                addMessage('RECV', '批量响应 2/5', true);
                                addMessage('INFO', '系统信息 3/5', false);
                                addMessage('RECV', '数据包 4/5', true);
                                addMessage('SEND', '完成信号 5/5', false);
                            }}
                        >
                            BATCH
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
});

// 添加组件显示名称以便于调试
SocketConsole.displayName = 'SocketConsole';

export default SocketConsole;
