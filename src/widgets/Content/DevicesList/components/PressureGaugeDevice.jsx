import React from 'react';
import BaseDevice from './BaseDevice';

// 定义压力计设备特有的颜色方案
const PRESSURE_GAUGE_COLOR_SCHEMES = [
    {
        name: 'blue',
        borderColor: '#1E5A6B',
        progressColor: '#2BA4B9'
    },
    {
        name: 'darkBlue',
        borderColor: '#1A4A5C',
        progressColor: '#1E8AAD'
    }
];

/**
 * 压力计设备组件
 * @param {Object} props 
 * @param {Object} props.data - 初始设备数据
 * @param {boolean} props.useKonva - 是否使用Konva版本（可选，默认false）
 * @returns {JSX.Element}
 */
const PressureGaugeDevice = ({ data, useKonva = true }) => {
    return (
        <BaseDevice 
            data={data}
            unit="MPa"
            colorSchemes={PRESSURE_GAUGE_COLOR_SCHEMES}
            useKonva={useKonva}
        />
    );
};

// 导出颜色方案以便外部使用
export { PRESSURE_GAUGE_COLOR_SCHEMES };
export default PressureGaugeDevice; 