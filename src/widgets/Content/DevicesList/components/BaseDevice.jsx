import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { Stage, Layer, Line, Rect, Group } from 'react-konva';
import Konva from 'konva';
import NumberFlow from '@number-flow/react';
import { gsap } from 'gsap';

// 定义颜色常量
const BG_COLOR = '#262629';
const PROGRESS_BG_COLOR = '#363A3E';

/**
 * 自定义Hook - 定时器
 * @param {Function} callback - 回调函数
 * @param {number} delay - 延迟时间（毫秒）
 */
function useInterval(callback, delay) {
    const savedCallback = useRef();
  
    // 记住最新的回调函数
    useEffect(() => {
        savedCallback.current = callback;
    }, [callback]);
  
    // 设置定时器
    useEffect(() => {
        function tick() {
            savedCallback.current();
        }
        if (delay !== null) {
            const id = setInterval(tick, delay);
            return () => clearInterval(id);
        }
    }, [delay]);
}

/**
 * 自定义Hook - 设备数据管理
 * @param {Object} initialData - 初始设备数据
 * @param {Object} config - 设备配置
 * @returns {Object} { deviceData, setDeviceData }
 */
export const useDeviceData = (initialData, config = {}) => {
    // 内部数据状态管理
    const [deviceData, setDeviceData] = useState(initialData);
    const nextUpdateTimeRef = useRef(Date.now() + Math.floor(Math.random() * 8000));
    
    // 合并默认配置 - 使用useMemo缓存
    const deviceConfig = useMemo(() => ({
        updateProbability: 0.10,        // 更新概率
        checkInterval: 2000,            // 检查间隔（毫秒）
        minUpdateDelay: 8000,           // 最小更新延迟
        maxUpdateDelay: 25000,          // 最大更新延迟
        valueChangeRange: 41,           // 数值变化范围（-20到+20）
        progressMinChange: 15,          // 进度条最小变化幅度
        progressChangeRange: 40,        // 进度条变化范围
        animationDuration: 500,         // 数字动画时长
        ...config
    }), [config]);
    
    // 当初始数据变化时更新内部状态（用于分类切换等场景）
    useEffect(() => {
        // 保留动画配置，只更新其他数据
        setDeviceData(prev => ({
            ...initialData,
            _animationConfig: initialData._animationConfig || prev._animationConfig
        }));
        nextUpdateTimeRef.current = Date.now() + Math.floor(Math.random() * deviceConfig.minUpdateDelay);
    }, [initialData.id, initialData.value, initialData._animationConfig]); // 监听ID、值和动画配置变化
    
    // 更新进度条的函数 - 确保产生明显变化 (缓存优化)
    const getProgressChange = useCallback((currentProgress) => {
        // 大幅度变化进度条
        const minChange = deviceConfig.progressMinChange;
        let progressDelta = (Math.random() * deviceConfig.progressChangeRange) - (deviceConfig.progressChangeRange / 2);
        
        // 确保变化幅度足够大
        if (Math.abs(progressDelta) < minChange) {
            progressDelta = progressDelta >= 0 ? minChange : -minChange;
        }
        
        // 确保进度条值在合理范围内
        return Math.min(90, Math.max(10, currentProgress + progressDelta));
    }, [deviceConfig.progressMinChange, deviceConfig.progressChangeRange]);
    
    // 定时检查设备更新
    useInterval(() => {
        const now = Date.now();
        
        // 检查是否到达更新时间
        if (now >= nextUpdateTimeRef.current) {
            // 按配置概率更新设备数值和进度条
            if (Math.random() < deviceConfig.updateProbability) {
                // 随机变化数值
                const valueDelta = Math.floor(Math.random() * deviceConfig.valueChangeRange) - Math.floor(deviceConfig.valueChangeRange / 2);
                // 生成新的小数部分
                const newDecimal = parseFloat((Math.random()).toFixed(2));
                // 新的值是整数部分变化加上新的小数部分
                const newValue = Math.max(0, deviceData.value - (deviceData.value % 1) + valueDelta) + newDecimal;
                
                // 当且仅当数值变化时，同时更新进度条
                const newProgress = getProgressChange(deviceData.progress);
                
                // 更新设备数据
                setDeviceData(prev => ({
                    ...prev,
                    value: newValue,
                    progress: newProgress
                }));
            }
            
            // 设置下一次更新时间
            const nextUpdateDelay = deviceConfig.minUpdateDelay + 
                Math.floor(Math.random() * (deviceConfig.maxUpdateDelay - deviceConfig.minUpdateDelay));
            nextUpdateTimeRef.current = now + nextUpdateDelay;
        }
    }, deviceConfig.checkInterval);
    
    return { deviceData, setDeviceData, deviceConfig };
};

/**
 * 自定义Hook - 设备颜色管理 - 优化版本
 * @param {Object} deviceData - 设备数据
 * @param {Array} colorSchemes - 颜色方案数组
 * @returns {Object} { borderColor, progressColor, borderLineClass }
 */
export const useDeviceColors = (deviceData, colorSchemes) => {
    return useMemo(() => {
        // 确定报警状态
        let borderLineClass = '';
        if (deviceData.progress > deviceData.alarmUpperLimit) {
            borderLineClass = 'border-line-high';
        } else if (deviceData.progress < deviceData.alarmLowerLimit) {
            borderLineClass = 'border-line-low';
        }

        // 获取默认颜色
        const defaultBorderColor = deviceData.colorScheme ? 
            deviceData.colorScheme.borderColor : 
            (colorSchemes && colorSchemes[0] ? colorSchemes[0].borderColor : '#26764D');
        
        const defaultProgressColor = deviceData.colorScheme ? 
            deviceData.colorScheme.progressColor : 
            (colorSchemes && colorSchemes[0] ? colorSchemes[0].progressColor : '#23C26D');

        // 确定最终颜色
        let borderColor = defaultBorderColor;
        let progressColor = defaultProgressColor;
        
        if (deviceData.progress > deviceData.alarmUpperLimit) {
            borderColor = '#B83F31'; // 超过上限
            progressColor = '#B83F31';
        } else if (deviceData.progress < deviceData.alarmLowerLimit) {
            borderColor = '#E39D25'; // 低于下限
            progressColor = '#E39D25';
        }

        return { borderColor, progressColor, borderLineClass };
    }, [deviceData.progress, deviceData.alarmUpperLimit, deviceData.alarmLowerLimit, 
        deviceData.colorScheme, colorSchemes]);
};

/**
 * 默认数字显示组件
 * @param {Object} props
 * @param {Object} props.deviceData - 设备数据
 * @param {string} props.unit - 单位
 * @param {number} props.animationDuration - 动画时长
 * @returns {JSX.Element}
 */
export const DefaultNumberDisplay = React.memo(({ deviceData, unit, animationDuration = 500 }) => {
    const [shouldStartAnimation, setShouldStartAnimation] = useState(false);
    
    // 如果是从0开始的数字动画，使用更长的动画时间 (缓存计算结果)
    const finalAnimationDuration = useMemo(() => {
        return deviceData._animationConfig?.numberAnimated && deviceData.value > 0 ? 1500 : animationDuration;
    }, [deviceData._animationConfig?.numberAnimated, deviceData.value, animationDuration]);
    
    // 添加延迟启动NumberFlow动画
    useEffect(() => {
        if (deviceData._animationConfig?.numberAnimated && deviceData.value > 0) {
            // 延迟1秒后开始NumberFlow动画
            const timer = setTimeout(() => {
                setShouldStartAnimation(true);
            }, 1000);
            
            return () => clearTimeout(timer);
        } else {
            setShouldStartAnimation(true);
        }
    }, [deviceData._animationConfig?.numberAnimated, deviceData.value]);
    
    return (
    <div className="flex-grow flex items-center justify-center">
        {/* 单位显示 - 右上角 */}
        <div className="absolute top-[1px] right-[1px] bg-[#161A1B] px-[5px] text-[10px] font-['DingTalkJinBuTi']" style={{ 
            color: '#CAC9CE',
        }}>
            <TypewriterText 
                text={unit} 
                isAnimated={deviceData._animationConfig?.numberAnimated}
                delay={1800}
            />
        </div>
        <div className="flex items-baseline">
            <NumberFlow 
                value={shouldStartAnimation ? parseInt(deviceData.value) : 0} 
                format={{ minimumIntegerDigits: 3, useGrouping: false }}
                className="text-[#CAC9CE] text-[24px] md:text-[26px] lg:text-[30px] font-['ChakraPetch-Light']"
                style={{ '--number-flow-char-height': '1.2em' }}
                animationDuration={finalAnimationDuration}
            />
            <span className="text-[#CAC9CE]">.</span>
            <NumberFlow 
                value={shouldStartAnimation ? parseInt((deviceData.value % 1) * 100) : 0} 
                format={{ minimumIntegerDigits: 2, useGrouping: false }}
                className="text-[#CAC9CE] text-[16px] md:text-[18px] lg:text-[20px] font-['ChakraPetch-Light']"
                style={{ '--number-flow-char-height': '1.2em' }}
                animationDuration={finalAnimationDuration}
            />
        </div>
    </div>
    );
});

/**
 * 打字机效果组件 - 修复版本，确保开场动画正确
 */
const TypewriterText = React.memo(({ text, isAnimated, delay = 0 }) => {
    const [displayText, setDisplayText] = useState(''); // 始终从空字符串开始
    const [isTyping, setIsTyping] = useState(false);
    
    useEffect(() => {
        // 重置显示文本
        setDisplayText('');
        setIsTyping(false);
        
        if (!isAnimated) {
            // 非动画模式下也添加延迟，保持一致性
            const timer = setTimeout(() => {
                setDisplayText(text);
            }, delay);
            
            return () => clearTimeout(timer);
        }
        
        // 动画模式
        let currentIndex = 0;
        let typeInterval;
        
        const timer = setTimeout(() => {
            setIsTyping(true);
            typeInterval = setInterval(() => {
                if (currentIndex <= text.length) {
                    setDisplayText(text.slice(0, currentIndex));
                    currentIndex++;
                } else {
                    clearInterval(typeInterval);
                    setIsTyping(false);
                }
            }, 50); // 每50ms显示一个字符
        }, delay);
        
        return () => {
            clearTimeout(timer);
            if (typeInterval) clearInterval(typeInterval);
        };
    }, [text, isAnimated, delay]);
    
    return (
        <span className="truncate">
            {displayText}
            {isTyping && <span className="animate-pulse">|</span>}
        </span>
    );
});

/**
 * 默认设备名称组件
 * @param {Object} props
 * @param {Object} props.deviceData - 设备数据
 * @returns {JSX.Element}
 */
export const DefaultDeviceName = ({ deviceData }) => (
    <div 
        className="h-[20px] flex items-center px-[5px] mt-auto overflow-hidden bg-[#161A1B] font-['ChakraPetch-Light'] text-[10px] text-left"
        style={{
            color: '#CAC9CE',
        }}
    >
        <TypewriterText 
            text={deviceData.name} 
            isAnimated={deviceData._animationConfig?.numberAnimated}
            delay={1200}
        />
    </div>
);

/**
 * Konva版本的边框组件
 * @param {Object} props
 * @param {string} props.borderColor - 边框颜色
 * @param {string} props.borderLineClass - 边框样式类
 * @param {number} props.containerWidth - 容器宽度
 * @param {number} props.containerHeight - 容器高度
 * @returns {JSX.Element}
 */
export const DeviceBorder = ({ borderColor, borderLineClass, containerWidth, containerHeight }) => {
    const borderGroupRef = useRef(null);
    
    // 实现边框闪烁动画 - 强制重绘版本
    useEffect(() => {
        if (borderGroupRef.current) {
            const borderGroup = borderGroupRef.current;
            
            // 先重置状态
            borderGroup.opacity(1);
            borderGroup.getLayer()?.batchDraw();
            
            if (borderLineClass === 'border-line-high' || borderLineClass === 'border-line-low') {
                // 创建呼吸灯动画 - 更急促的报警效果
                const tween = new Konva.Tween({
                    node: borderGroup,
                    duration: 0.5, // 更快的动画速度
                    opacity: 0.3, // 更低的透明度，增强视觉效果
                    easing: Konva.Easings.EaseInOut,
                    yoyo: true,
                    repeat: -1,
                    onUpdate: () => {
                        borderGroup.getLayer()?.batchDraw(); // 强制重绘
                    }
                });
                
                tween.play();
                
                return () => {
                    tween.destroy();
                    borderGroup.opacity(1);
                    borderGroup.getLayer()?.batchDraw();
                };
            }
        }
    }, [borderLineClass, containerWidth, containerHeight]);
    
    return (
        <Group ref={borderGroupRef}>
            {/* 上边线 */}
            <Line
                points={[0, 0, containerWidth, 0]}
                stroke={borderColor}
                strokeWidth={1.5}
            />
            
            {/* 右边线 - 带缺口 */}
            <Line
                points={[containerWidth, 0, containerWidth, containerHeight / 2 - 5]}
                stroke={borderColor}
                strokeWidth={1.5}
            />
            <Line
                points={[containerWidth, containerHeight / 2 + 5, containerWidth, containerHeight]}
                stroke={borderColor}
                strokeWidth={1.5}
            />
            {/* 右边缺口处的横线 */}
            <Line
                points={[containerWidth, containerHeight / 2, containerWidth - 10, containerHeight / 2]}
                stroke={borderColor}
                strokeWidth={1.5}
            />
            
            {/* 下边线 */}
            <Line
                points={[0, containerHeight, containerWidth, containerHeight]}
                stroke={borderColor}
                strokeWidth={1.5}
            />
            
            {/* 左边线 - 带缺口 */}
            <Line
                points={[0, 0, 0, containerHeight / 2 - 5]}
                stroke={borderColor}
                strokeWidth={1.5}
            />
            <Line
                points={[0, containerHeight / 2 + 5, 0, containerHeight]}
                stroke={borderColor}
                strokeWidth={1.5}
            />
            {/* 左边缺口处的横线 */}
            <Line
                points={[0, containerHeight / 2, 10, containerHeight / 2]}
                stroke={borderColor}
                strokeWidth={1.5}
            />
        </Group>
    );
};

/**
 * Konva版本的进度条组件
 * @param {Object} props
 * @param {Object} props.deviceData - 设备数据
 * @param {string} props.progressColor - 进度条颜色
 * @param {number} props.containerWidth - 容器宽度
 * @returns {JSX.Element}
 */
export const DeviceProgressBar = ({ deviceData, progressColor, containerWidth = 100 }) => {
    const progressBarRef = useRef(null);
    const triangleRef = useRef(null);
    const previousProgressRef = useRef(null); // 存储上次的进度值
    const isFirstRenderRef = useRef(true); // 标记是否第一次渲染
    const [shouldStartProgressAnimation, setShouldStartProgressAnimation] = useState(false);
    
    // 添加进度条动画延迟启动
    useEffect(() => {
        if (deviceData._animationConfig?.numberAnimated && deviceData.progress > 0) {
            // 延迟1.5秒后开始进度条动画
            const timer = setTimeout(() => {
                setShouldStartProgressAnimation(true);
            }, 1500);
            
            return () => clearTimeout(timer);
        } else {
            setShouldStartProgressAnimation(true);
        }
    }, [deviceData._animationConfig?.numberAnimated, deviceData.progress]);
    
    // 进度条动画 - GSAP优化版本
    useEffect(() => {
        if (!shouldStartProgressAnimation) return;
        if (progressBarRef.current && triangleRef.current) {
            const progressBar = progressBarRef.current;
            const triangle = triangleRef.current;
            const currentProgress = deviceData.progress;
            const previousProgress = previousProgressRef.current;
            
            // 判断是否是真正的初始动画（第一次渲染且有_animationConfig标记）
            const isRealInitialAnimation = isFirstRenderRef.current && deviceData._animationConfig?.numberAnimated && deviceData.progress > 0;
            const duration = isRealInitialAnimation ? 1.5 : 0.7;
            
            // 确定起始进度：只有真正的初始动画才从0开始，其他情况从上次进度开始
            let startProgress;
            if (isRealInitialAnimation) {
                startProgress = 0;
            } else if (previousProgress !== null) {
                startProgress = previousProgress;
            } else {
                // 如果没有之前的进度记录，直接使用当前进度（无动画）
                startProgress = currentProgress;
            }
            
            // 缓存计算结果，避免重复计算
            const targetWidth = (containerWidth * currentProgress) / 100;
            const startWidth = (containerWidth * startProgress) / 100;
            const targetX = targetWidth; // 三角形位置与进度条宽度相同
            const startX = startWidth;
            
            // 设置初始位置
            progressBar.width(startWidth);
            triangle.x(startX);
            
            // 只有当起始位置和目标位置不同时才执行动画
            const progressDiff = Math.abs(startProgress - currentProgress);
            if (progressDiff > 0.1) {
                // 使用GSAP创建动画对象，实现更精确的动画控制
                const animationData = { progress: 0 };
                
                const progressAnimation = gsap.to(animationData, {
                    progress: 1,
                    duration: duration,
                    ease: "power2.inOut",
                    onUpdate: () => {
                        const progress = animationData.progress;
                        const currentWidth = startWidth + (targetWidth - startWidth) * progress;
                        const currentX = startX + (targetX - startX) * progress;
                        progressBar.width(currentWidth);
                        triangle.x(currentX);
                        progressBar.getLayer()?.batchDraw(); // 批量重绘，提升性能
                    },
                    onComplete: () => {
                        // 确保最终状态正确
                        progressBar.width(targetWidth);
                        triangle.x(targetX);
                        progressBar.getLayer()?.batchDraw();
                    }
                });
                
                // 更新上次的进度值和首次渲染标记
                previousProgressRef.current = currentProgress;
                isFirstRenderRef.current = false;
                
                return () => {
                    progressAnimation.kill(); // GSAP的清理方法
                };
            } else {
                // 如果没有变化，直接设置最终位置
                progressBar.width(targetWidth);
                triangle.x(targetX);
                previousProgressRef.current = currentProgress;
                isFirstRenderRef.current = false;
            }
        }
    }, [deviceData.progress, containerWidth, deviceData._animationConfig?.numberAnimated, shouldStartProgressAnimation]);
    
    const progressBarHeight = 7;
    
    return (
        <div className="w-full h-[7px] mt-[4px] relative">
            <Stage width={containerWidth} height={progressBarHeight}>
                <Layer imageSmoothingEnabled={false}>
                    {/* 进度条背景 */}
                    <Rect
                        x={0}
                        y={0}
                        width={containerWidth}
                        height={2}
                        fill={PROGRESS_BG_COLOR}
                    />
                    
                    {/* 进度条填充 - 带动画 */}
                    <Rect
                        ref={progressBarRef}
                        x={0}
                        y={0}
                        width={shouldStartProgressAnimation ? (containerWidth * deviceData.progress) / 100 : 0}
                        height={2}
                        fill={progressColor}
                    />
                    
                    {/* 白色三角形指示器 - 带动画 */}
                    <Line
                        ref={triangleRef}
                        points={[0, 2, -4, 7, 4, 7]}
                        closed={true}
                        fill="#C1BBB9"
                        x={shouldStartProgressAnimation ? (containerWidth * deviceData.progress) / 100 : 0}
                        y={0}
                    />
                    
                    {/* 报警下限刻度 */}
                    <Rect
                        x={(containerWidth * deviceData.alarmLowerLimit) / 100 - 2}
                        y={0}
                        width={4}
                        height={2}
                        fill="#FFFFFF"
                    />
                    
                    {/* 报警上限刻度 */}
                    <Rect
                        x={(containerWidth * deviceData.alarmUpperLimit) / 100 - 2}
                        y={0}
                        width={4}
                        height={2}
                        fill="#FFFFFF"
                    />
                </Layer>
            </Stage>
        </div>
    );
};

/**
 * 通用设备基础组件 - 使用Konva渲染
 * @param {Object} props 
 * @param {Object} props.data - 初始设备数据
 * @param {string} props.unit - 设备单位（如 'm³/h', 'MPa'）
 * @param {Array} props.colorSchemes - 设备颜色方案数组
 * @param {Object} props.config - 设备配置（可选）
 * @param {Function} props.renderContent - 自定义内容渲染函数（可选）
 * @param {Function} props.renderBorder - 自定义边框渲染函数（可选）
 * @param {Function} props.renderProgressBar - 自定义进度条渲染函数（可选）
 * @param {string} props.containerClassName - 容器自定义样式类（可选）
 * @returns {JSX.Element}
 */
const BaseDevice = ({ 
    data: initialData, 
    unit, 
    colorSchemes,
    config = {},
    renderContent,
    renderBorder,
    renderProgressBar,
    containerClassName = ""
}) => {
    // 使用自定义Hook管理设备数据
    const { deviceData, deviceConfig } = useDeviceData(initialData, config);
    
    // 使用自定义Hook管理设备颜色
    const { borderColor, progressColor, borderLineClass } = useDeviceColors(deviceData, colorSchemes);
    
    // 容器尺寸管理
    const containerRef = useRef(null);
    const [containerSize, setContainerSize] = useState({ width: 100, height: 70 });
    
    // 获取容器尺寸用于Konva绘制 - 优化版本
    useEffect(() => {
        if (containerRef.current) {
            const updateSize = () => {
                const rect = containerRef.current.getBoundingClientRect();
                const newSize = { width: rect.width, height: rect.height };
                // 只有尺寸真正变化时才更新状态，避免不必要的重渲染
                setContainerSize(prevSize => {
                    if (Math.abs(prevSize.width - newSize.width) > 1 || Math.abs(prevSize.height - newSize.height) > 1) {
                        return newSize;
                    }
                    return prevSize;
                });
            };
            
            updateSize();
            // 使用防抖的 ResizeObserver
            let resizeTimer;
            const resizeObserver = new ResizeObserver(() => {
                if (resizeTimer) clearTimeout(resizeTimer);
                resizeTimer = setTimeout(updateSize, 16); // 约60fps的防抖
            });
            resizeObserver.observe(containerRef.current);
            
            return () => {
                if (resizeTimer) clearTimeout(resizeTimer);
                resizeObserver.disconnect();
            };
        }
    }, []);

    // 默认内容渲染函数
    const defaultRenderContent = () => (
        <>
            <DefaultNumberDisplay 
                deviceData={deviceData} 
                unit={unit} 
                animationDuration={deviceConfig.animationDuration} 
            />
            <DefaultDeviceName deviceData={deviceData} />
        </>
    );

    // 默认边框渲染函数
    const defaultRenderBorder = () => (
        <div className="absolute inset-[0px] pointer-events-none">
            <Stage width={containerSize.width} height={containerSize.height}>
                <Layer imageSmoothingEnabled={false}>
                    <DeviceBorder 
                        borderColor={borderColor} 
                        borderLineClass={borderLineClass}
                        containerWidth={containerSize.width}
                        containerHeight={containerSize.height}
                    />
                </Layer>
            </Stage>
        </div>
    );

    // 默认进度条渲染函数
    const defaultRenderProgressBar = () => (
        <DeviceProgressBar 
            deviceData={deviceData} 
            progressColor={progressColor}
            containerWidth={containerSize.width}
        />
    );

    return (
        <>
            {/* 上部分容器：包含四周边线、中间文字和设备名称 */}
            <div 
                ref={containerRef}
                className={`w-full h-[calc(100%-10px)] relative flex flex-col mb-3 ${containerClassName}`}
                style={{ backgroundColor: BG_COLOR }}
            >
                
                {/* 渲染边框 */}
                {renderBorder ? renderBorder({ borderColor, borderLineClass, deviceData, containerSize }) : defaultRenderBorder()}
                
                {/* 渲染内容 */}
                {renderContent ? renderContent({ deviceData, unit, deviceConfig }) : defaultRenderContent()}
            </div>
            
            {/* 下部分容器：进度条 */}
            {renderProgressBar ? renderProgressBar({ deviceData, progressColor, containerSize }) : defaultRenderProgressBar()}
        </>
    );
};

// 导出Hook和组件供外部使用
export { useInterval, BG_COLOR, PROGRESS_BG_COLOR };
export default BaseDevice; 