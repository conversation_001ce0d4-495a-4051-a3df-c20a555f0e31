import React from 'react';
import BaseListItem from './BaseListItem';
import { FLOW_METER_COLOR_SCHEMES } from './FlowMeterDevice';

/**
 * 流量计列表项组件
 * @param {Object} props 
 * @param {Object} props.data - 流量计设备数据
 */
const FlowMeterListItem = ({ data, isClosing }) => {
    return (
        <BaseListItem 
            data={data}
            unit="m³/h"
            colorScheme={FLOW_METER_COLOR_SCHEMES[0]}
            icon="💧"
            isClosing={isClosing}
        />
    );
};

export default FlowMeterListItem; 