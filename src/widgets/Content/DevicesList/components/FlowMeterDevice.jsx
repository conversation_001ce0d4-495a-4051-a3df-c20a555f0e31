import React from 'react';
import BaseDevice from './BaseDevice';

// 定义流量计设备特有的颜色方案
const FLOW_METER_COLOR_SCHEMES = [
    {
        name: 'green',
        borderColor: '#26764D',
        progressColor: '#23C26D'
    }
];

/**
 * 流量计设备组件
 * @param {Object} props 
 * @param {Object} props.data - 初始设备数据
 * @returns {JSX.Element}
 */
const FlowMeterDevice = ({ data }) => {
    return (
        <BaseDevice 
            data={data}
            unit="m³/h"
            colorSchemes={FLOW_METER_COLOR_SCHEMES}
        />
    );
};

// 导出颜色方案以便外部使用
export { FLOW_METER_COLOR_SCHEMES };
export default FlowMeterDevice; 