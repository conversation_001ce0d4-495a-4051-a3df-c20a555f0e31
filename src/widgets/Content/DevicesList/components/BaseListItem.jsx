import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import NumberFlow from '@number-flow/react';

/**
 * 自定义Hook - 定时器
 * @param {Function} callback - 回调函数
 * @param {number} delay - 延迟时间（毫秒）
 */
function useInterval(callback, delay) {
    const savedCallback = useRef();
  
    // 记住最新的回调函数
    useEffect(() => {
        savedCallback.current = callback;
    }, [callback]);
  
    // 设置定时器
    useEffect(() => {
        function tick() {
            savedCallback.current();
        }
        if (delay !== null) {
            const id = setInterval(tick, delay);
            return () => clearInterval(id);
        }
    }, [delay]);
}

/**
 * 自定义Hook - 列表项设备数据管理
 * @param {Object} initialData - 初始设备数据
 * @param {boolean} isVisible - 组件是否可见
 * @param {Object} config - 设备配置
 * @returns {Object} { deviceData, setDeviceData, chartData, setChartData }
 */
const useListItemData = (initialData, isVisible, config = {}) => {
    // 内部数据状态管理
    const [deviceData, setDeviceData] = useState(initialData);
    const [chartData, setChartData] = useState([]); // 图表数据状态
    const nextUpdateTimeRef = useRef(Date.now() + Math.floor(Math.random() * 8000));
    
    // 添加图表数据范围引用，用于控制新数据点的生成范围
    const chartDataRangeRef = useRef({ min: 100, max: 900 }); // 默认范围
    
    // 添加开场动画完成状态引用，控制何时开始添加新数据
    const animationCompletedRef = useRef(false);
    
    // 合并默认配置
    const deviceConfig = {
        updateProbability: 0.45,        // 更新概率（进一步降低到45%，显著减少更新频率）
        checkInterval: 1800,            // 检查间隔（调整到1.8秒，进一步降低检查频率）
        minUpdateDelay: 3000,           // 最小更新延迟（调整到3秒）
        maxUpdateDelay: 8000,           // 最大更新延迟（调整到8秒）
        valueChangeRange: 35,           // 数值变化范围
        progressMinChange: 12,          // 进度条最小变化幅度
        progressChangeRange: 35,        // 进度条变化范围
        maxChartPoints: 22,             // 图表最大数据点数
        ...config
    };
    
    // 当初始数据变化时更新内部状态
    useEffect(() => {
        setDeviceData(prev => ({
            ...initialData,
            _lastUpdate: initialData._lastUpdate || prev._lastUpdate
        }));
        nextUpdateTimeRef.current = Date.now() + Math.floor(Math.random() * deviceConfig.minUpdateDelay);
    }, [initialData.id, initialData.value]);
    
    // 监听图表数据变化，初始化或更新数据范围
    useEffect(() => {
        if (chartData.length > 0) {
            updateChartDataRange(chartData);
        }
    }, [chartData.length]); // 只在数据长度变化时触发，避免过度更新
    
    // 更新进度条的函数 - 确保产生明显变化
    const getProgressChange = (currentProgress) => {
        const minChange = deviceConfig.progressMinChange;
        let progressDelta = (Math.random() * deviceConfig.progressChangeRange) - (deviceConfig.progressChangeRange / 2);
        
        // 确保变化幅度足够大
        if (Math.abs(progressDelta) < minChange) {
            progressDelta = progressDelta >= 0 ? minChange : -minChange;
        }
        
        // 确保进度条值在合理范围内
        return Math.min(90, Math.max(10, currentProgress + progressDelta));
    };
    
    // 生成新的图表数据点 - 控制在初始数据范围内
    const generateNewChartPoint = (baseValue, deviceId) => {
        // 使用设备ID和当前时间生成随机种子
        const deviceStr = deviceId ? deviceId.toString() : '1';
        const timeSeed = Date.now() % 10000;
        const hashSeed = deviceStr.split('').reduce((acc, char, index) => {
            return acc + char.charCodeAt(0) * (index + 1) * 137;
        }, timeSeed);
        
        // 基于当前设备数值生成相关的图表数据
        const random1 = ((hashSeed * 9301 + 49297) % 233280) / 233280;
        const random2 = ((hashSeed * 16807 + 2147483647) % 2147483647) / 2147483647;
        
        // 获取当前图表数据的实际范围
        const currentRange = chartDataRangeRef.current;
        const rangeSize = currentRange.max - currentRange.min;
        
        // 在实际数据范围内生成新值，保持数据的连续性和合理性
        const centerValue = currentRange.min + rangeSize * 0.5; // 中心值
        const maxVariation = rangeSize * 0.15; // 最大变化幅度为范围的15%
        
        // 基于设备数值的小幅变化
        const deviceInfluence = (baseValue % 10) * (rangeSize * 0.02); // 设备数值影响
        const variation = (random1 - 0.5) * maxVariation; // 主要变化
        const trend = Math.sin(timeSeed * 0.001) * (rangeSize * 0.08); // 趋势变化
        const noise = (random2 - 0.5) * (rangeSize * 0.05); // 细微噪声
        
        const newValue = centerValue + deviceInfluence + variation + trend + noise;
        
        // 确保新值在合理范围内，稍微收紧边界以保持视觉效果
        const minBound = currentRange.min + rangeSize * 0.1; // 距离最小值10%
        const maxBound = currentRange.max - rangeSize * 0.1; // 距离最大值10%
        
        return Math.max(minBound, Math.min(maxBound, Math.round(newValue)));
    };
    
    // 更新图表数据范围的函数
    const updateChartDataRange = (data) => {
        if (data && data.length > 0) {
            const min = Math.min(...data);
            const max = Math.max(...data);
            // 确保范围不会太小，至少保持50的差值
            const minRange = 50;
            if (max - min < minRange) {
                const center = (min + max) / 2;
                chartDataRangeRef.current = {
                    min: center - minRange / 2,
                    max: center + minRange / 2
                };
            } else {
                chartDataRangeRef.current = { min, max };
            }
        }
    };
    
    // 定时检查设备更新
    useInterval(() => {
        // 只有当组件可见时才进行数据更新检查
        if (!isVisible) return;

        const now = Date.now();
        
        // 检查是否到达更新时间且开场动画已完成
        if (now >= nextUpdateTimeRef.current && animationCompletedRef.current) {
            // 按配置概率更新设备数值和进度条
            if (Math.random() < deviceConfig.updateProbability) {
                // 随机变化数值
                const valueDelta = Math.floor(Math.random() * deviceConfig.valueChangeRange) - Math.floor(deviceConfig.valueChangeRange / 2);
                // 生成新的小数部分
                const newDecimal = parseFloat((Math.random()).toFixed(2));
                // 新的值是整数部分变化加上新的小数部分
                const newValue = Math.max(0, deviceData.value - (deviceData.value % 1) + valueDelta) + newDecimal;
                
                // 当且仅当数值变化时，同时更新进度条
                const newProgress = getProgressChange(deviceData.progress);
                
                // 生成新的图表数据点
                const newChartPoint = generateNewChartPoint(newValue, deviceData.id);
                
                // 更新设备数据
                setDeviceData(prev => ({
                    ...prev,
                    value: newValue,
                    progress: newProgress,
                    _lastUpdate: now
                }));
                
                // 更新图表数据 - 实现真正的滑动窗口效果
                setChartData(prev => {
                    let newData;
                    // 如果已经达到最大点数，实现滑动窗口
                    if (prev.length >= deviceConfig.maxChartPoints) {
                        // 移除第一个点，添加新点到末尾
                        newData = [...prev.slice(1), newChartPoint];
                    } else {
                        // 还没达到最大点数，直接添加
                        newData = [...prev, newChartPoint];
                    }
                    
                    // 每次更新图表数据时，同时更新数据范围
                    updateChartDataRange(newData);
                    
                    return newData;
                });
            }
            
            // 设置下一次更新时间
            const nextUpdateDelay = deviceConfig.minUpdateDelay + 
                Math.floor(Math.random() * (deviceConfig.maxUpdateDelay - deviceConfig.minUpdateDelay));
            nextUpdateTimeRef.current = now + nextUpdateDelay;
        }
    }, deviceConfig.checkInterval);
    
    // 设置动画完成状态的函数
    const setAnimationCompleted = () => {
        animationCompletedRef.current = true;
    };
    
    return { deviceData, setDeviceData, chartData, setChartData, deviceConfig, setAnimationCompleted };
};

// TinyArea组件 - 自定义SVG实现
const TinyAreaChart = ({ deviceId, color, height = 70, animated = false, data: externalData = null }) => {
    const [chartData, setChartData] = useState([]); // 图表数据状态
    const [isAnimating, setIsAnimating] = useState(false); // 是否正在播放动画
    const [showChart, setShowChart] = useState(false); // 是否显示图表
    const [newPointAnimation, setNewPointAnimation] = useState(false); // 新数据点动画状态
    const [lastDataLength, setLastDataLength] = useState(0); // 记录上次数据长度
    const [isDataFlowing, setIsDataFlowing] = useState(false); // 数据是否正在流动
    const [previousData, setPreviousData] = useState([]); // 保存上一次的数据用于动画
    
    // 根据设备ID生成随机但固定的数据，确保每个设备数据差异明显
    const generateRandomData = (seed) => {
        // 使用设备ID的字符串生成更复杂的种子
        const deviceStr = deviceId ? deviceId.toString() : '1';
        const hashSeed = deviceStr.split('').reduce((acc, char, index) => {
            return acc + char.charCodeAt(0) * (index + 1) * 137;
        }, seed * 1000);
        
        const rawData = [];
        
        // 生成22个数据点，每个设备都有明显不同的数据
        for (let i = 0; i < 22; i++) {
            // 使用更复杂的随机算法确保差异
            const random1 = ((hashSeed + i * 47 + deviceStr.charCodeAt(i % deviceStr.length) * 73) * 9301 + 49297) % 233280 / 233280;
            const random2 = ((hashSeed + i * 83 + deviceStr.length * 157) * 16807 + 2147483647) % 2147483647 / 2147483647;
            const random3 = ((hashSeed * 13 + i * 29 + deviceStr.charCodeAt(0) * 211) * 7919 + 65537) % 65537 / 65537;
            
            // 基础值根据设备ID变化
            const baseValue = 250 + (hashSeed % 500) + deviceStr.length * 20;
            
            // 变化幅度也根据设备不同
            const variation = (random1 - 0.5) * (250 + (hashSeed % 400));
            const trend = Math.sin(i * 0.4 + hashSeed * 0.003) * (120 + (hashSeed % 200));
            const noise = (random2 - 0.5) * 50 + (random3 - 0.5) * 30;
            
            const value = baseValue + variation + trend + noise;
            rawData.push(Math.max(100, Math.min(900, Math.round(value))));
        }
        
        return rawData;
    };
    
    // 监听外部数据变化，实现推动式数据流动效果
    useEffect(() => {
        if (externalData && externalData.length > 0) {
            // 检查是否有新数据点添加
            const isNewDataAdded = externalData.length !== lastDataLength && lastDataLength > 0;
            
            if (isNewDataAdded) {
                // 保存当前数据作为动画起始状态
                setPreviousData([...chartData]);
                
                // 触发推动动画
                setIsDataFlowing(true);
                setNewPointAnimation(true);
                
                // 延迟设置新数据，让动画有时间准备
                setTimeout(() => {
                    setChartData(externalData);
                }, 50);
                
                // 动画结束
                setTimeout(() => {
                    setNewPointAnimation(false);
                    setIsDataFlowing(false);
                    setPreviousData([]);
                }, 600);
            } else {
                // 初始数据设置
                setChartData(externalData);
                setPreviousData([]);
            }
            
            setShowChart(true);
            setLastDataLength(externalData.length);
            
            // 启动绘制动画（仅首次）
            if (!showChart) {
                setTimeout(() => {
                    setIsAnimating(true);
                }, 100);
            }
        } else {
            // 如果没有外部数据，清空图表
            setChartData([]);
            setPreviousData([]);
            setShowChart(false);
            setIsAnimating(false);
            setLastDataLength(0);
            setIsDataFlowing(false);
        }
    }, [externalData]);
    
    const data = chartData.length > 0 ? chartData : [];
    
    if (!data || data.length === 0) {
        // 数据为空时显示加载状态
        return (
            <div className="w-full h-full flex items-center justify-center">
                <div className="text-[#666] text-[10px] font-['DingTalkJinBuTi']">等待数据...</div>
            </div>
        );
    }
    
    // 调试信息：确保每个设备的数据都不同
    // console.log(`Device ${deviceId} data flowing:`, data.length, isDataFlowing);
    
    // 计算数据范围，确保不超过显示范围
    const minValue = Math.min(...data);
    const maxValue = Math.max(...data);
    const range = maxValue - minValue || 1;
    
    // 去掉padding，让图表完全填充容器，与进度条完全对齐
    const padding = 0; // 完全去掉padding
    const svgWidth = 100; // SVG viewBox宽度
    const innerWidth = svgWidth;
    const innerHeight = height - 15; // 为底部进度条预留15px空间
    
    // 固定22个点的位置
    const maxPoints = 22;
    const stepX = innerWidth / (maxPoints - 1);
    
    // 生成当前数据的路径
    const generatePath = (dataArray, offsetX = 0) => {
        if (!dataArray || dataArray.length === 0) return '';
        
        // 重新计算数据范围（使用当前数据的范围）
        const dataMin = Math.min(...dataArray);
        const dataMax = Math.max(...dataArray);
        const dataRange = dataMax - dataMin || 1;
        
        const points = dataArray.map((value, index) => {
            const x = index * stepX + offsetX;
            const y = innerHeight - ((value - dataMin) / dataRange) * innerHeight;
            return { x, y };
        });
        
        if (points.length === 0) return '';
        
        let pathD = `M ${points[0].x},${points[0].y}`;
        
        for (let i = 1; i < points.length; i++) {
            const prev = points[i - 1];
            const curr = points[i];
            const next = points[i + 1];
            
            if (i === 1) {
                const cp1x = prev.x + (curr.x - prev.x) * 0.5;
                const cp1y = prev.y;
                pathD += ` Q ${cp1x},${cp1y} ${curr.x},${curr.y}`;
            } else if (i === points.length - 1) {
                const cp1x = prev.x + (curr.x - prev.x) * 0.5;
                const cp1y = curr.y;
                pathD += ` Q ${cp1x},${cp1y} ${curr.x},${curr.y}`;
            } else {
                const cp1x = prev.x + (curr.x - prev.x) * 0.3;
                const cp1y = prev.y;
                const cp2x = curr.x - (next.x - prev.x) * 0.3;
                const cp2y = curr.y;
                pathD += ` C ${cp1x},${cp1y} ${cp2x},${cp2y} ${curr.x},${curr.y}`;
            }
        }
        
        // 闭合路径形成面积
        pathD += ` L ${points[points.length - 1].x},${height} L ${points[0].x},${height} Z`;
        return pathD;
    };
    
    // 当前数据的路径
    const currentPath = generatePath(data);
    
    // 上一次数据的路径（用于动画起始状态）
    const previousPath = isDataFlowing && previousData.length > 0 ? generatePath(previousData, -stepX) : '';
    
    // 生成唯一的动画ID
    const animationId = `chart-draw-${deviceId || 'default'}`;
    const newPointAnimationId = `new-point-${deviceId || 'default'}`;
    const flowAnimationId = `flow-${deviceId || 'default'}`;
    
    return (
        <div className="w-full h-full">
            <svg 
                width="100%" 
                height={height} 
                viewBox={`0 0 ${svgWidth} ${height}`}
                preserveAspectRatio="none"
                className="w-full h-full"
                style={{ display: 'block' }}
            >
                <defs>
                    {/* 从上往下的渐变，底部透明让进度条更清晰 */}
                    <linearGradient id={`gradient-${deviceId || 'default'}`} x1="0%" y1="0%" x2="0%" y2="100%">
                        <stop offset="0%" stopColor={color} stopOpacity="0.7" />
                        <stop offset="50%" stopColor={color} stopOpacity="0.3" />
                        <stop offset="100%" stopColor={color} stopOpacity="0.0" />
                    </linearGradient>
                    
                    {/* 绘制动画的遮罩 */}
                    <mask id={`drawMask-${deviceId || 'default'}`}>
                        <rect 
                            x="0" 
                            y="0" 
                            width="0" 
                            height={height}
                            fill="white"
                            style={{
                                animation: isAnimating ? `${animationId} 0.5s ease-out forwards` : 'none'
                            }}
                        />
                    </mask>
                </defs>
                
                {/* 主要的面积图 */}
                {currentPath && showChart && (
                    <path
                        d={currentPath}
                        fill={`url(#gradient-${deviceId || 'default'})`}
                        stroke="none"
                        mask={`url(#drawMask-${deviceId || 'default'})`}
                        style={{
                            opacity: 1,
                            transition: 'all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)', // 更自然的缓动
                            transformOrigin: 'center center'
                        }}
                    />
                )}
                
                {/* 新数据点的动画效果 */}
                {newPointAnimation && data.length > 0 && (() => {
                    // 计算最新数据点的位置
                    const lastIndex = data.length - 1;
                    const lastX = lastIndex * stepX;
                    const dataMin = Math.min(...data);
                    const dataMax = Math.max(...data);
                    const dataRange = dataMax - dataMin || 1;
                    const lastY = innerHeight - ((data[lastIndex] - dataMin) / dataRange) * innerHeight;
                    
                    return (
                        <g>
                            {/* 最新数据点的脉冲效果 */}
                            <circle
                                cx={lastX}
                                cy={lastY}
                                r="3"
                                fill={color}
                                opacity="0.8"
                                style={{
                                    animation: `${newPointAnimationId} 0.5s ease-out forwards`
                                }}
                            />
                            
                            {/* 新数据点的波纹效果 */}
                            <circle
                                cx={lastX}
                                cy={lastY}
                                r="1"
                                fill="none"
                                stroke={color}
                                strokeWidth="1"
                                opacity="0.6"
                                style={{
                                    animation: `${newPointAnimationId}-ripple 0.5s ease-out forwards`
                                }}
                            />
                        </g>
                    );
                })()}
            </svg>
            
            {/* 添加绘制动画样式 */}
            <style>
                {`
                    @keyframes ${animationId} {
                        0% { 
                            width: 0;
                        }
                        100% { 
                            width: 100%;
                        }
                    }
                    
                    @keyframes ${newPointAnimationId} {
                        0% { 
                            opacity: 0;
                            transform: scale(0);
                        }
                        30% { 
                            opacity: 1;
                            transform: scale(1.3);
                        }
                        100% { 
                            opacity: 0.3;
                            transform: scale(0.8);
                        }
                    }
                    
                    @keyframes ${newPointAnimationId}-ripple {
                        0% { 
                            r: 1;
                            opacity: 0.5;
                        }
                        100% { 
                            r: 6;
                            opacity: 0;
                        }
                    }
                `}
            </style>
        </div>
    );
};

/**
 * 基础列表项组件 - 所有设备列表项的基础组件
 * @param {Object} props 
 * @param {Object} props.data - 设备数据
 * @param {string} props.unit - 数值单位
 * @param {Object} props.colorScheme - 颜色方案
 * @param {string} props.icon - 设备图标（可选）
 * @param {Object} props.config - 设备配置（可选）
 */
// 常量样式对象，避免每次渲染重新创建
const STATIC_STYLES = {
    unitBackground: { backgroundColor: '#161A1B' },
    progressIndicator: {
        borderLeft: '4px solid transparent',
        borderRight: '4px solid transparent',
        borderBottom: '5px solid #C1BBB9',
        transition: 'transform 0.3s ease-in-out'
    }
};

const BaseListItem = ({ data, unit, colorScheme, icon, config = {}, isClosing }) => {
    const itemRef = useRef(null); // 列表项引用

    // 使用 Intersection Observer API 监测组件是否在可视范围内
    const [isVisible, setIsVisible] = useState(false);
    useEffect(() => {
        const observer = new IntersectionObserver(
            ([entry]) => {
                if (entry.isIntersecting) {
                    setIsVisible(true);
                    // 优化：一旦可见，就可以停止观察，除非需要处理移出视口的情况
                    observer.unobserve(entry.target);
                }
            },
            {
                root: null, // 使用浏览器视口作为根
                rootMargin: '0px',
                threshold: 0.1 // 元素可见10%时触发
            }
        );

        if (itemRef.current) {
            observer.observe(itemRef.current);
        }

        return () => {
            if (itemRef.current) {
                observer.unobserve(itemRef.current);
            }
        };
    }, [itemRef]);

    // 使用自管理数据Hook，并传入isVisible状态
    const { deviceData, setDeviceData, chartData, setChartData, setAnimationCompleted } = useListItemData(data, isVisible, config);
    
    const [displayValue, setDisplayValue] = useState(0.00); // 显示的数值，初始为0.00（包含小数）
    const [displayUnit, setDisplayUnit] = useState(''); // 显示的单位，初始为空
    const [displayName, setDisplayName] = useState(''); // 显示的设备名称，初始为空
    const [displayProgress, setDisplayProgress] = useState(0); // 显示的进度条值，初始为0
    const [displayLowerLimit, setDisplayLowerLimit] = useState(0); // 显示的下限值，初始为0
    const [displayUpperLimit, setDisplayUpperLimit] = useState(0); // 显示的上限值，初始为0
    const [displayAverage, setDisplayAverage] = useState(0); // 显示的平均值，初始为0
    const [displayExtraValue, setDisplayExtraValue] = useState(0); // 显示的额外维度值，初始为0
    const [containerAnimated, setContainerAnimated] = useState(false); // 容器动画是否完成
    const [numberAnimated, setNumberAnimated] = useState(false); // 数字动画是否开始
    const [trendAnimating, setTrendAnimating] = useState(false); // 趋势文字动画状态
    const [previousTrendState, setPreviousTrendState] = useState(null); // 上一次的趋势状态
    
    if (!deviceData) {
        return null;
    }

    const {
        id,
        name,
        value,
        progress,
        alarmLowerLimit,
        alarmUpperLimit,
        type
    } = deviceData;

    // 根据设备ID生成随机但固定的初始图表数据
    const generateRandomData = useCallback((seed) => {
        // 使用设备ID的字符串生成更复杂的种子
        const deviceStr = id ? id.toString() : '1';
        const hashSeed = deviceStr.split('').reduce((acc, char, index) => {
            return acc + char.charCodeAt(0) * (index + 1) * 137;
        }, seed * 1000);
        
        const rawData = [];
        
        // 生成22个数据点，每个设备都有明显不同的数据
        for (let i = 0; i < 22; i++) {
            // 使用更复杂的随机算法确保差异
            const random1 = ((hashSeed + i * 47 + deviceStr.charCodeAt(i % deviceStr.length) * 73) * 9301 + 49297) % 233280 / 233280;
            const random2 = ((hashSeed + i * 83 + deviceStr.length * 157) * 16807 + 2147483647) % 2147483647 / 2147483647;
            const random3 = ((hashSeed * 13 + i * 29 + deviceStr.charCodeAt(0) * 211) * 7919 + 65537) % 65537 / 65537;
            
            // 基础值根据设备ID变化
            const baseValue = 250 + (hashSeed % 500) + deviceStr.length * 20;
            
            // 变化幅度也根据设备不同
            const variation = (random1 - 0.5) * (250 + (hashSeed % 400));
            const trend = Math.sin(i * 0.4 + hashSeed * 0.003) * (120 + (hashSeed % 200));
            const noise = (random2 - 0.5) * 50 + (random3 - 0.5) * 30;
            
            const value = baseValue + variation + trend + noise;
            rawData.push(Math.max(100, Math.min(900, Math.round(value))));
        }
        
        return rawData;
    }, [id]);

    // 根据设备ID生成固定的随机延迟，使用useMemo缓存结果
    const randomDelay = useMemo(() => {
        const seed = id ? id.toString().charCodeAt(0) || 1 : 1;
        return (seed * 137 % 800) / 1000; // 0-0.8秒的随机延迟
    }, [id]);

    // 生成固定的随机动画持续时间，使用useMemo缓存结果
    const animationDuration = useMemo(() => 0.2 + Math.random() * 0.3, [id]);
    
    // 确定报警状态和边框颜色
    const { borderColor, progressColor, borderLineClass } = useMemo(() => {
        // 确定报警状态
        let borderLineClass = '';
        if (displayProgress > alarmUpperLimit) {
            borderLineClass = 'border-line-high';
        } else if (displayProgress < alarmLowerLimit) {
            borderLineClass = 'border-line-low';
        }

        // 确定边框颜色
        let borderColor = colorScheme?.borderColor || '#26764D';
        if (displayProgress > alarmUpperLimit) {
            borderColor = '#B83F31'; // 超过上限
        } else if (displayProgress < alarmLowerLimit) {
            borderColor = '#E39D25'; // 低于下限
        }

        // 确定进度条颜色
        let progressColor = colorScheme?.progressColor || '#23C26D';
        if (displayProgress > alarmUpperLimit) {
            progressColor = '#B83F31'; // 超过上限
        } else if (displayProgress < alarmLowerLimit) {
            progressColor = '#E39D25'; // 低于下限
        }

        return { borderColor, progressColor, borderLineClass };
    }, [displayProgress, alarmUpperLimit, alarmLowerLimit, colorScheme]);
    
    // 缓存动态样式对象，避免每次渲染重新创建
    const dynamicStyles = useMemo(() => ({
        animationDuration: { '--animation-duration': `${animationDuration}s` },
        borderColor: { backgroundColor: borderColor },
        progressBar: { 
            width: `${displayProgress}%`,
            backgroundColor: progressColor
        },
        lowerLimitScale: { 
            left: `${alarmLowerLimit}%`,
            transform: 'translateX(-50%)'
        },
        upperLimitScale: { 
            left: `${alarmUpperLimit}%`,
            transform: 'translateX(-50%)'
        }
    }), [animationDuration, borderColor, progressColor, displayProgress, alarmLowerLimit, alarmUpperLimit]);
    
    // 监听设备数据变化，实时更新显示值（只在数字动画开始后才更新）
    useEffect(() => {
        // 只有在数字动画开始后才更新显示值，否则保持初始的0值
        if (numberAnimated) {
            // setDisplayValue(value); 
            setDisplayProgress(progress);
            
            // 根据设备类型更新额外维度值（实时更新）
            if (type === 'flowMeter') {
                setDisplayExtraValue(value * 24); // 累计值
            } else if (type === 'pressureGauge') {
                setDisplayExtraValue(25 + Math.sin(value) * 5); // 温补值
            }
        }
    }, [value, progress, type, numberAnimated]);
    
    // 监听进度条变化，触发趋势动画
    useEffect(() => {
        // 确定当前趋势状态
        let currentTrendState;
        if (displayProgress > 60) {
            currentTrendState = 'up';
        } else if (displayProgress < 40) {
            currentTrendState = 'down';
        } else {
            currentTrendState = 'stable';
        }
        
        // 如果趋势状态发生变化且不是初始状态，触发动画
        if (previousTrendState !== null && previousTrendState !== currentTrendState) {
            setTrendAnimating(true);
            
            // 收缩阶段：200ms
            setTimeout(() => {
                setPreviousTrendState(currentTrendState);
            }, 200);
            
            // 扩散阶段：再200ms后结束动画
            setTimeout(() => {
                setTrendAnimating(false);
            }, 400);
        } else if (previousTrendState === null) {
            // 初始状态设置
            setPreviousTrendState(currentTrendState);
        }
    }, [displayProgress, previousTrendState]);
    
    // 初始化动画和静态内容（只执行一次）
    useEffect(() => {
        // 只有当组件可见时才执行动画初始化
        if (!isVisible) return;

        // 使用固定的随机动画持续时间，与网格块状视图保持一致
        const duration = animationDuration;
        
        // 开始容器闪烁动画
        const containerTimer = setTimeout(() => {
            setContainerAnimated(true);
        }, randomDelay * 1000);
        
        // 在容器闪烁动画结束时开始数字动画
        const numberTimer = setTimeout(() => {
            setNumberAnimated(true);
        }, randomDelay * 1000 + duration * 1000); // 与容器动画结束时间同步
        
        // 错峰显示数字、单位和设备名称，避免卡顿
        // 在闪烁动画完成后等待一段延迟再设置实际数据
        const baseDelay = randomDelay * 1000 + duration * 1000 + 100; // 容器动画结束后延迟100ms
        
        // 第一步：显示数字和进度条（容器动画结束后）
        const numberTimer2 = setTimeout(() => {
            // 在定时器内部访问当前的设备数据
            const currentData = deviceData;
            setDisplayValue(currentData.value); // 设置实际数值（包含小数）
            setDisplayProgress(currentData.progress); // 设置实际进度条值
            setDisplayLowerLimit(currentData.alarmLowerLimit || 0); // 设置实际下限值
            setDisplayUpperLimit(currentData.alarmUpperLimit || 100); // 设置实际上限值
            setDisplayAverage((currentData.alarmLowerLimit + currentData.alarmUpperLimit) / 2); // 设置实际平均值
            
            // 根据设备类型设置额外维度值
            if (currentData.type === 'flowMeter') {
                setDisplayExtraValue(currentData.value * 24); // 累计值
            } else if (currentData.type === 'pressureGauge') {
                setDisplayExtraValue(25 + Math.sin(currentData.value) * 5); // 温补值
            }
        }, baseDelay);
        
        // 第二步：显示单位（数字显示完成后500ms，以打字机效果展示）
        const unitTimer = setTimeout(() => {
            if (unit) {
                setDisplayUnit(''); // 先清空单位显示
                let currentIndex = 0;
                const typewriterTimer = setInterval(() => {
                    if (currentIndex <= unit.length) {
                        setDisplayUnit(unit.substring(0, currentIndex));
                        currentIndex++;
                    } else {
                        clearInterval(typewriterTimer);
                    }
                }, 80); // 适中的打字机速度，让用户能清楚看到效果
            }
        }, baseDelay + 500); // 数字变化后500ms再开始单位打字机效果
        
        // 第三步：显示设备名称（单位显示完成后300ms）
        const nameTimer = setTimeout(() => {
            // 在定时器内部访问当前的设备数据
            const currentData = deviceData;
            const deviceName = currentData.name || `${currentData.type} ${currentData.id}`;
            if (deviceName) {
                let currentIndex = 0;
                const nameTypewriterTimer = setInterval(() => {
                    if (currentIndex <= deviceName.length) {
                        setDisplayName(deviceName.substring(0, currentIndex));
                        currentIndex++;
                    } else {
                        clearInterval(nameTypewriterTimer);
                    }
                }, 40); // 更快的打字机速度
            }
        }, baseDelay + 800); // 单位打字机完成后再显示设备名称
        
        // 第四步：加载初始图表数据（开场闪烁动画结束后延迟2.5秒，确保其他动画完成）
        const chartTimer = setTimeout(() => {
            // 只在图表数据为空时生成初始数据
            if (chartData.length === 0) {
                const currentData = deviceData;
                const generatedData = generateRandomData(currentData.id ? currentData.id.toString().charCodeAt(0) || 1 : 1);
                setChartData(generatedData);
            }
        }, randomDelay * 1000 + duration * 1000 + 2500); // 闪烁动画结束后延迟2.5秒
        
        // 第五步：标记开场动画完成，允许开始添加新数据（图表数据加载后再延迟0.5秒）
        const animationCompleteTimer = setTimeout(() => {
            setAnimationCompleted();
        }, randomDelay * 1000 + duration * 1000 + 3000); // 图表数据加载后再延迟0.5秒
        
        return () => {
            clearTimeout(containerTimer);
            clearTimeout(numberTimer);
            clearTimeout(numberTimer2);
            clearTimeout(unitTimer);
            clearTimeout(nameTimer);
            clearTimeout(chartTimer);
            clearTimeout(animationCompleteTimer);
        };
    }, [id, randomDelay, animationDuration, isVisible]); // 依赖isVisible，当可见时触发

    return (
        <>
            {/* 添加趋势动画样式和开场动画 */}
            <style>
                {`
                    @keyframes breathe-up {
                        0%, 100% { 
                            opacity: 0.3; 
                            transform: translateY(0px) scale(0.8);
                        }
                        50% { 
                            opacity: 1; 
                            transform: translateY(-3px) scale(1.2);
                        }
                    }
                    
                    @keyframes breathe-down {
                        0%, 100% { 
                            opacity: 0.3; 
                            transform: translateY(0px) scale(0.8);
                        }
                        50% { 
                            opacity: 1; 
                            transform: translateY(3px) scale(1.2);
                        }
                    }
                    
                    @keyframes borderBreathingHigh {
                        0%, 100% { background-color: rgba(184, 63, 49, 0.4); }
                        50% { background-color: rgba(184, 63, 49, 1); }
                    }
                    @keyframes borderBreathingLow {
                        0%, 100% { background-color: rgba(227, 157, 37, 0.4); }
                        50% { background-color: rgba(227, 157, 37, 1); }
                    }
                    .border-line-high {
                        animation: borderBreathingHigh 0.8s infinite;
                    }
                    .border-line-low {
                        animation: borderBreathingLow 0.8s infinite;
                    }
                    
                    @keyframes fadeIn {
                        0% {
                            opacity: 0;
                        }
                        100% {
                            opacity: 1;
                        }
                    }
                    
                    @keyframes deviceEntrance {
                        0% { opacity: 0; }
                        15% { opacity: 0.8; }
                        30% { opacity: 0.2; }
                        45% { opacity: 0.7; }
                        60% { opacity: 0.1; }
                        75% { opacity: 1; }
                        90% { opacity: 0.2; }
                        100% { opacity: 1; }
                    }
                    
                    .device-list-item {
                        opacity: 0;
                    }
                    
                    .device-list-item-animated {
                        animation: deviceEntrance var(--animation-duration, 0.3s) ease-in-out forwards;
                    }
                    
                    .device-icon-container {
                        opacity: 0;
                    }
                    
                    .device-icon-container-animated {
                        animation: deviceEntrance var(--animation-duration, 0.3s) ease-in-out forwards;
                    }
                    
                    .device-info-container {
                        opacity: 0;
                    }
                    
                    .device-info-container-animated {
                        animation: deviceEntrance var(--animation-duration, 0.3s) ease-in-out forwards;
                    }
                    
                    .device-chart-container {
                        opacity: 0;
                    }
                    
                    .device-chart-container-animated {
                        animation: deviceEntrance var(--animation-duration, 0.3s) ease-in-out forwards;
                    }

                    /* 设备列表项关闭动画 - 急促闪烁关闭效果 */
                    @keyframes deviceExit {
                        0% { opacity: 1; }
                        10% { opacity: 0.2; }
                        25% { opacity: 1; }
                        40% { opacity: 0.1; }
                        55% { opacity: 0.7; }
                        70% { opacity: 0.2; }
                        85% { opacity: 0.8; }
                        100% { opacity: 0; }
                    }

                    .device-list-item-closing {
                        animation: deviceExit var(--closing-duration, 0.15s) ease-in-out forwards;
                    }
                `}
            </style>
            <div 
                ref={itemRef}
                className={`device-list-item ${
                    isClosing
                        ? 'device-list-item-closing'
                        : containerAnimated
                        ? 'device-list-item-animated'
                        : ''
                } rounded-lg mb-[10px] ml-[10px] overflow-hidden transition-all duration-200`}
                style={{
                    ...dynamicStyles.animationDuration,
                    '--closing-duration': `${isClosing ? 0.15 : 0}s`,
                }}
            >
            {/* 主要信息行 */}
            <div className="flex items-center justify-between p-4">
                {/* 左侧：设备名称和图标 - 添加最小宽度设置 */}
                <div className="flex items-center space-x-3 flex-shrink-0 min-w-[420px]">
                    {icon && (
                        <div 
                            className={`device-icon-container ${containerAnimated ? 'device-icon-container-animated' : ''} w-[90px] h-[70px] bg-[#262629]  rounded-lg flex items-center justify-center relative`}
                            style={dynamicStyles.animationDuration}
                        >
                            {/* 块状视图样式的边框 */}
                            {/* 上边线 */}
                            <div className={`absolute top-[0] left-[0] w-full h-[1px] ${borderLineClass}`}
                                style={dynamicStyles.borderColor}></div>
                            
                            {/* 右边线 - 带缺口 */}
                            <div className={`absolute top-[0] right-[0] h-[calc(50%-5px)] w-[1px] ${borderLineClass}`}
                                style={dynamicStyles.borderColor}></div>
                            <div className={`absolute bottom-[0] right-[0] h-[calc(50%-5px)] w-[1px] ${borderLineClass}`}
                                style={dynamicStyles.borderColor}></div>
                            {/* 右边缺口处的横线 */}
                            <div className={`absolute top-[50%] right-[0] w-[10px] h-[1px] transform -translate-y-[50%] ${borderLineClass}`}
                                style={dynamicStyles.borderColor}></div>
                            
                            {/* 下边线 */}
                            <div className={`absolute bottom-[0] left-[0] w-full h-[1px] ${borderLineClass}`}
                                style={dynamicStyles.borderColor}></div>
                            
                            {/* 左边线 - 带缺口 */}
                            <div className={`absolute top-[0] left-[0] h-[calc(50%-5px)] w-[1px] ${borderLineClass}`}
                                style={dynamicStyles.borderColor}></div>
                            <div className={`absolute bottom-[0] left-[0] h-[calc(50%-5px)] w-[1px] ${borderLineClass}`}
                                style={dynamicStyles.borderColor}></div>
                            {/* 左边缺口处的横线 */}
                            <div className={`absolute top-[50%] left-[0] w-[10px] h-[1px] transform -translate-y-[50%] ${borderLineClass}`}
                                style={dynamicStyles.borderColor}></div>

                            {/* 中间显示设备数值 */}
                            <div className="text-white font-['ChakraPetch-Light'] leading-none flex items-baseline">
                                <NumberFlow 
                                    value={Math.floor(displayValue)}
                                    className="text-white text-[18px]"
                                    animationDuration={2500}
                                />
                                <span className="text-[12px]">.</span>
                                <NumberFlow 
                                    value={Math.round((displayValue % 1) * 100)}
                                    format={{ minimumIntegerDigits: 2 }}
                                    className="text-white text-[12px]"
                                    animationDuration={2500}
                                />
                            </div>
                            {/* 单位显示在右下角 */}
                            <div 
                                className="absolute bottom-[1px] right-[1px] text-white text-[10px] font-['DingTalkJinBuTi'] px-[5px]"
                                style={STATIC_STYLES.unitBackground}
                            >
                                {displayUnit}
                            </div>
                        </div>
                    )}
                    
                    {/* 右侧信息容器 */}
                    <div 
                        className={`device-info-container ${containerAnimated ? 'device-info-container-animated' : ''} flex-1 h-[70px] ml-[10px] flex flex-col`}
                        style={dynamicStyles.animationDuration}
                    >
                        {/* 合并的信息容器 */}
                        <div className="flex-1 px-[5px] py-[2px]">
                            <div className="w-full h-full bg-[#1A1D1F] rounded-[4px] border border-[#404348] flex flex-col">
                                {/* 上半部分 - 显示所有重要维度 (减少垂直padding) */}
                                <div className="flex-1 flex items-center justify-between px-[8px] py-[2px]">
                                    {/* 始终显示下限、上限、平均值 */}
                                    <div className="text-center flex-1">
                                        <div className="text-[11px] text-[#8B8B8B] font-['DingTalkJinBuTi']">下限</div>
                                        <div className="text-[#FFB366] text-[11px] font-['DingTalkJinBuTi'] transition-all duration-500 ease-in-out">{displayLowerLimit}{displayUnit}</div>
                                    </div>
                                    <div className="w-[1px] h-[18px] bg-[#404348]"></div>
                                    <div className="text-center flex-1">
                                        <div className="text-[11px] text-[#8B8B8B] font-['DingTalkJinBuTi']">上限</div>
                                        <div className="text-[#FF6B6B] text-[11px] font-['DingTalkJinBuTi'] transition-all duration-500 ease-in-out">{displayUpperLimit}{displayUnit}</div>
                                    </div>
                                    <div className="w-[1px] h-[18px] bg-[#404348]"></div>
                                    <div className="text-center flex-1">
                                        <div className="text-[11px] text-[#8B8B8B] font-['DingTalkJinBuTi']">平均</div>
                                        <div className="text-[#4ECDC4] text-[11px] font-['DingTalkJinBuTi'] transition-all duration-500 ease-in-out">{displayAverage.toFixed(1)}{displayUnit}</div>
                                    </div>
                                    
                                    {/* 根据设备类型显示额外维度 */}
                                    {type === 'flowMeter' && (
                                        <>
                                            <div className="w-[1px] h-[18px] bg-[#404348]"></div>
                                            <div className="text-center flex-1">
                                                <div className="text-[11px] text-[#8B8B8B] font-['DingTalkJinBuTi']">累计</div>
                                                <div className="text-[#9B59B6] text-[11px] font-['DingTalkJinBuTi'] transition-all duration-500 ease-in-out">{displayExtraValue.toFixed(0)}L</div>
                                            </div>
                                        </>
                                    )}
                                    
                                    {type === 'pressureGauge' && (
                                        <>
                                            <div className="w-[1px] h-[18px] bg-[#404348]"></div>
                                            <div className="text-center flex-1">
                                                <div className="text-[11px] text-[#8B8B8B] font-['DingTalkJinBuTi']">温补</div>
                                                <div className="text-[#E67E22] text-[11px] font-['DingTalkJinBuTi'] transition-all duration-500 ease-in-out">{displayExtraValue.toFixed(1)}°C</div>
                                            </div>
                                        </>
                                    )}
                                    
                                    {/* 所有设备都显示趋势（放在最后） */}
                                    <div className="w-[1px] h-[18px] bg-[#404348]"></div>
                                    <div className="text-center flex-1">
                                        <div className="text-[11px] text-[#8B8B8B] font-['DingTalkJinBuTi']">趋势</div>
                                        <div className="text-[11px] font-['DingTalkJinBuTi'] flex items-center justify-center w-full relative">
                                            {displayProgress > 60 ? (
                                                <>
                                                    <div 
                                                        className="flex items-center transition-all duration-200 ease-in-out"
                                                        style={{
                                                            transform: trendAnimating ? 'scaleX(0.3)' : 'scaleX(1)',
                                                            opacity: trendAnimating ? 0.3 : 1
                                                        }}
                                                    >
                                                        <div 
                                                            className="w-[0] h-[0] border-l-[3px] border-r-[3px] border-b-[5px] border-l-transparent border-r-transparent border-b-[#FF6B6B] animate-bounce"
                                                            style={{ animationDuration: '1.5s' }}
                                                        ></div>
                                                        <span className="ml-[2px] text-[#FF6B6B]">上升</span>
                                                    </div>
                                                    {/* 右侧三个小三角形垂直排列呼吸效果 */}
                                                    <div className="absolute right-[2px] top-[20%] transform -translate-y-[50%] flex flex-col space-y-[1px] transition-all duration-500 ease-in-out">
                                                        <div 
                                                            className="w-[0] h-[0] border-l-[4px] border-r-[4px] border-b-[5px] border-l-transparent border-r-transparent border-b-[#FF6B6B] transition-all duration-300 ease-in-out"
                                                            style={{ 
                                                                animation: 'breathe-up 1.2s infinite ease-in-out 0.4s'
                                                            }}
                                                        ></div>
                                                        <div 
                                                            className="w-[0] h-[0] border-l-[4px] border-r-[4px] border-b-[5px] border-l-transparent border-r-transparent border-b-[#FF6B6B] transition-all duration-300 ease-in-out"
                                                            style={{ 
                                                                animation: 'breathe-up 1.2s infinite ease-in-out 0.2s'
                                                            }}
                                                        ></div>
                                                        <div 
                                                            className="w-[0] h-[0] border-l-[4px] border-r-[4px] border-b-[5px] border-l-transparent border-r-transparent border-b-[#FF6B6B] transition-all duration-300 ease-in-out"
                                                            style={{ 
                                                                animation: 'breathe-up 1.2s infinite ease-in-out 0s'
                                                            }}
                                                        ></div>
                                                    </div>
                                                </>
                                            ) : displayProgress < 40 ? (
                                                <>
                                                    <div 
                                                        className="flex items-center transition-all duration-200 ease-in-out"
                                                        style={{
                                                            transform: trendAnimating ? 'scaleX(0.3)' : 'scaleX(1)',
                                                            opacity: trendAnimating ? 0.3 : 1
                                                        }}
                                                    >
                                                        <div 
                                                            className="w-[0] h-[0] border-l-[3px] border-r-[3px] border-t-[5px] border-l-transparent border-r-transparent border-t-[#FFB366] animate-bounce"
                                                            style={{ animationDuration: '1.5s' }}
                                                        ></div>
                                                        <span className="ml-[2px] text-[#FFB366]">下降</span>
                                                    </div>
                                                    {/* 右侧三个小三角形垂直排列呼吸效果（向下） */}
                                                    <div className="absolute right-[2px] top-[0] transform -translate-y-[50%] flex flex-col space-y-[1px] transition-all duration-500 ease-in-out">
                                                        <div 
                                                            className="w-[0] h-[0] border-l-[4px] border-r-[4px] border-t-[5px] border-l-transparent border-r-transparent border-t-[#FFB366] transition-all duration-300 ease-in-out"
                                                            style={{ 
                                                                animation: 'breathe-down 1.2s infinite ease-in-out 0s'
                                                            }}
                                                        ></div>
                                                        <div 
                                                            className="w-[0] h-[0] border-l-[4px] border-r-[4px] border-t-[5px] border-l-transparent border-r-transparent border-t-[#FFB366] transition-all duration-300 ease-in-out"
                                                            style={{ 
                                                                animation: 'breathe-down 1.2s infinite ease-in-out 0.2s'
                                                            }}
                                                        ></div>
                                                        <div 
                                                            className="w-[0] h-[0] border-l-[4px] border-r-[4px] border-t-[5px] border-l-transparent border-r-transparent border-t-[#FFB366] transition-all duration-300 ease-in-out"
                                                            style={{ 
                                                                animation: 'breathe-down 1.2s infinite ease-in-out 0.4s'
                                                            }}
                                                        ></div>
                                                    </div>
                                                </>
                                            ) : (
                                                <div 
                                                    className="flex items-center justify-center w-full transition-all duration-200 ease-in-out"
                                                    style={{
                                                        transform: trendAnimating ? 'scaleX(0.3)' : 'scaleX(1)',
                                                        opacity: trendAnimating ? 0.3 : 1
                                                    }}
                                                >
                                                    <div className="w-[6px] h-[2px] bg-[#2ECC71] transition-all duration-300 ease-in-out"></div>
                                                    <span className="ml-[2px] text-[#2ECC71] transition-all duration-300 ease-in-out">稳定</span>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>
                                
                                {/* 中间分隔线 */}
                                <div className="w-full h-[1px] bg-[#404348]"></div>
                                
                                {/* 下半部分 - 设备名称 (减少高度) */}
                                <div className="flex items-center px-[8px] h-[22px]">
                                    <div className="text-white text-[12px] font-['DingTalkJinBuTi'] truncate">
                                        {displayName}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* 右侧：进度条容器 */}
                <div 
                    className={`device-chart-container ${containerAnimated ? 'device-chart-container-animated' : ''} flex-1 h-[70px] relative`}
                    style={dynamicStyles.animationDuration}
                >
                    {/* TinyArea图表 - 启用图表显示 */}
                    <div className="absolute top-[0] left-[10px] right-[10px] h-[65px]">
                        <TinyAreaChart
                            deviceId={id}
                            color={colorScheme?.progressColor || '#1890ff'}
                            data={chartData}
                        />
                    </div>
                    
                    {/* 进度条 - 固定在底部，最上层显示，与图表完全对齐 */}
                    <div className="absolute bottom-[0] left-[10px] right-[10px] z-[10]">
                        <div className="w-full h-[7px] relative">
                            {/* 进度条背景 */}
                            <div className="absolute top-[0] left-[0] w-full h-[2px] bg-[#363A3E]">
                                <div 
                                    className="h-full relative transition-all ease-in-out duration-700"
                                    style={dynamicStyles.progressBar}
                                >
                                    {/* 白色小三角形指示器 */}
                                    <div 
                                        className="absolute bottom-[-5px] right-[0] transform translate-x-[50%]"
                                        style={STATIC_STYLES.progressIndicator}
                                    ></div>
                                </div>
                                
                                {/* 报警下限刻度 */}
                                <div 
                                    className="absolute top-[0px] h-[3px] w-[2px] bg-[#FFFFFF]" 
                                    style={dynamicStyles.lowerLimitScale}
                                ></div>
                                
                                {/* 报警上限刻度 */}
                                <div 
                                    className="absolute top-[0px] h-[3px] w-[2px] bg-[#FFFFFF]" 
                                    style={dynamicStyles.upperLimitScale}
                                ></div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>


        </div>
        </>
    );
};

export default BaseListItem;