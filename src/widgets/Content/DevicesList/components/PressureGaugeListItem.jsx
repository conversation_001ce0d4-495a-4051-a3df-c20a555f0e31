import React from 'react';
import BaseListItem from './BaseListItem';
import { PRESSURE_GAUGE_COLOR_SCHEMES } from './PressureGaugeDevice';

/**
 * 压力计列表项组件
 * @param {Object} props 
 * @param {Object} props.data - 压力计设备数据
 */
const PressureGaugeListItem = ({ data, isClosing }) => {
    return (
        <BaseListItem 
            data={data}
            unit="MPa"
            colorScheme={PRESSURE_GAUGE_COLOR_SCHEMES[0]}
            icon="⚡"
            isClosing={isClosing}
        />
    );
};

export default PressureGaugeListItem; 