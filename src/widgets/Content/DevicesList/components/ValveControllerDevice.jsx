import React from 'react';
import BaseDevice, { 
    useDeviceData, 
    useDeviceColors, 
    BG_COLOR,
    DefaultDeviceName 
} from './BaseDevice';
import NumberFlow from '@number-flow/react';

// 定义阀门控制器设备特有的颜色方案
const VALVE_CONTROLLER_COLOR_SCHEMES = [
    {
        name: 'purple',
        borderColor: '#6A1B9A',
        progressColor: '#9C27B0'
    },
    {
        name: 'indigo',
        borderColor: '#3F51B5',
        progressColor: '#5C6BC0'
    }
];

/**
 * 自定义圆形边框组件 - 阀门控制器专用
 */
const CircularBorder = ({ borderColor, borderLineClass }) => (
    <>
        {/* 圆形外边框 */}
        <div 
            className={`absolute top-[5px] left-[50%] transform -translate-x-[50%] w-[60px] h-[60px] rounded-full border-2 ${borderLineClass}`}
            style={{ borderColor: borderColor }}
        ></div>
        
        {/* 四个角的装饰线 */}
        <div className={`absolute top-[0] left-[0] w-[15px] h-[1px] ${borderLineClass}`}
            style={{ backgroundColor: borderColor }}></div>
        <div className={`absolute top-[0] right-[0] w-[15px] h-[1px] ${borderLineClass}`}
            style={{ backgroundColor: borderColor }}></div>
        <div className={`absolute bottom-[0] left-[0] w-[15px] h-[1px] ${borderLineClass}`}
            style={{ backgroundColor: borderColor }}></div>
        <div className={`absolute bottom-[0] right-[0] w-[15px] h-[1px] ${borderLineClass}`}
            style={{ backgroundColor: borderColor }}></div>
    </>
);

/**
 * 自定义圆形进度条组件 - 阀门控制器专用
 */
const CircularProgressBar = ({ deviceData, progressColor }) => {
    const radius = 25;
    const circumference = 2 * Math.PI * radius;
    const strokeDasharray = circumference;
    const strokeDashoffset = circumference - (deviceData.progress / 100) * circumference;

    return (
        <div className="absolute top-[5px] left-[50%] transform -translate-x-[50%] w-[60px] h-[60px]">
            <svg className="w-full h-full transform -rotate-90" viewBox="0 0 60 60">
                {/* 背景圆环 */}
                <circle
                    cx="30"
                    cy="30"
                    r={radius}
                    stroke="#363A3E"
                    strokeWidth="3"
                    fill="transparent"
                />
                {/* 进度圆环 */}
                <circle
                    cx="30"
                    cy="30"
                    r={radius}
                    stroke={progressColor}
                    strokeWidth="3"
                    fill="transparent"
                    strokeDasharray={strokeDasharray}
                    strokeDashoffset={strokeDashoffset}
                    strokeLinecap="round"
                    className="transition-all duration-700 ease-in-out"
                />
            </svg>
            
            {/* 中心百分比显示 */}
            <div className="absolute inset-0 flex items-center justify-center">
                <NumberFlow 
                    value={Math.round(deviceData.progress)} 
                    format={{ minimumIntegerDigits: 1, useGrouping: false }}
                    className="text-[#CAC9CE] text-[12px] font-['ChakraPetch-Light']"
                    suffix="%"
                />
            </div>
        </div>
    );
};

/**
 * 自定义内容渲染 - 阀门控制器专用
 */
const ValveContent = ({ deviceData, unit, deviceConfig }) => (
    <>
        {/* 圆形进度条区域 */}
        <div className="flex-grow flex items-start justify-center pt-[5px]">
            {/* 这里会被CircularProgressBar覆盖，但保持结构 */}
        </div>
        
        {/* 数值显示区域 - 在圆形下方 */}
        <div className="flex items-center justify-center mt-[10px] mb-[5px]">
            <div className="flex items-baseline">
                <NumberFlow 
                    value={parseInt(deviceData.value)} 
                    format={{ minimumIntegerDigits: 2, useGrouping: false }}
                    className="text-[#CAC9CE] text-[16px] font-['ChakraPetch-Light']"
                    style={{ '--number-flow-char-height': '1.2em' }}
                    animationDuration={deviceConfig.animationDuration}
                />
                <span className="text-[#CAC9CE] text-[10px] ml-[2px]">{unit}</span>
            </div>
        </div>
        
        {/* 设备名称 */}
        <DefaultDeviceName deviceData={deviceData} />
    </>
);

/**
 * 自定义边框渲染 - 阀门控制器专用
 */
const ValveBorder = ({ borderColor, borderLineClass, deviceData }) => (
    <>
        <CircularBorder borderColor={borderColor} borderLineClass={borderLineClass} />
        <CircularProgressBar deviceData={deviceData} progressColor={borderColor} />
    </>
);

/**
 * 阀门控制器设备组件 - 完全不同的UI布局
 * @param {Object} props 
 * @param {Object} props.data - 初始设备数据
 * @returns {JSX.Element}
 */
const ValveControllerDevice = ({ data }) => {
    // 阀门控制器的特殊配置：更慢的更新频率
    const valveConfig = {
        updateProbability: 0.05,        // 5%更新概率（更慢）
        checkInterval: 3000,            // 3秒检查一次
        minUpdateDelay: 15000,          // 15秒最小延迟
        maxUpdateDelay: 45000,          // 45秒最大延迟
        valueChangeRange: 11,           // 数值变化范围（-5到+5，阀门变化很小）
        progressMinChange: 5,           // 进度条最小变化5%
        progressChangeRange: 20,        // 进度条变化范围20%
        animationDuration: 1000,        // 更长的动画时长
    };

    return (
        <BaseDevice 
            data={data}
            unit="%"
            colorSchemes={VALVE_CONTROLLER_COLOR_SCHEMES}
            config={valveConfig}
            renderContent={ValveContent}
            renderBorder={ValveBorder}
            renderProgressBar={() => null} // 不渲染默认进度条，因为已经在边框中渲染了
            containerClassName="valve-controller-container"
        />
    );
};

// 导出颜色方案以便外部使用
export { VALVE_CONTROLLER_COLOR_SCHEMES };
export default ValveControllerDevice; 