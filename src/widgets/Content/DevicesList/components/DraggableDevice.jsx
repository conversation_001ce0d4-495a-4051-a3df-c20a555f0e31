import React, { useEffect } from 'react';
import { useDrag } from 'react-dnd';
import { getEmptyImage } from 'react-dnd-html5-backend';
import { useDragContext, DRAG_TYPES } from '../../../../hooks/useDragContext.jsx';

/**
 * 可拖拽的设备组件包装器
 * @param {Object} props
 * @param {Object} props.deviceData - 设备数据
 * @param {React.ReactNode} props.children - 设备组件
 * @param {Function} props.renderDevice - 渲染设备的函数
 * @param {Object} props.animationConfig - 动画配置
 * @returns {JSX.Element}
 */
const DraggableDevice = ({ deviceData, children, renderDevice, animationConfig }) => {
    const { 
        setDraggedDeviceData, 
        clearDraggedDevice, 
    } = useDragContext();

    const [{ isDragging }, drag, preview] = useDrag({
        type: DRAG_TYPES.DEVICE,
        item: () => {
            // 根据屏幕宽度动态设置宽高，使用最小尺寸
            const screenWidth = window.innerWidth;
            let w, h;
            
            if (screenWidth >= 1200) { // lg断点
                w = 1; h = 2; // 进一步减小宽度
            } else if (screenWidth >= 996) { // md断点
                w = 1; h = 2; // 进一步减小宽度
            } else if (screenWidth >= 768) { // sm断点
                w = 1; h = 2; // 进一步减小宽度
            } else { // xs断点
                w = 1; h = 2; // 进一步减小宽度
            }
            
            // 为被拖拽的设备添加 w 和 h 属性
            const deviceWithLayout = {
                ...deviceData,
                w, // 动态宽度
                h, // 动态高度
            };
            // 设置被拖拽的设备数据，供 react-dnd 内部使用
            setDraggedDeviceData(deviceWithLayout);
            return {
                ...deviceWithLayout,
                id: deviceData.id,
                type: deviceData.type,
                name: deviceData.name
            };
        },
        end: () => {
            // 拖拽结束时清除数据
            clearDraggedDevice();
        },
        collect: (monitor) => ({
            isDragging: monitor.isDragging(),
        }),
    });

    // 隐藏默认的拖拽预览，使用自定义预览
    useEffect(() => {
        preview(getEmptyImage(), { captureDraggingState: true });
    }, [preview]);

    // 拖拽时的样式
    const dragStyle = {
        opacity: isDragging ? 0.3 : 1,
        cursor: isDragging ? 'grabbing' : 'grab',
        transform: isDragging ? 'scale(0.9)' : 'scale(1)',
        transition: 'all 0.2s ease-in-out',
        position: 'relative',
        zIndex: isDragging ? 10 : 1,
    };

    // 为 react-grid-layout 的 onDrop 提供数据
    const handleDragStart = (e) => {
        // 根据屏幕宽度动态设置宽高，使用最小尺寸
        const screenWidth = window.innerWidth;
        let w, h;
        
        if (screenWidth >= 1200) { // lg断点
            w = 1; h = 2; // 进一步减小宽度
        } else if (screenWidth >= 996) { // md断点
            w = 1; h = 2; // 进一步减小宽度
        } else if (screenWidth >= 768) { // sm断点
            w = 1; h = 2; // 进一步减小宽度
        } else { // xs断点
            w = 1; h = 2; // 进一步减小宽度
        }
        
        const deviceWithLayout = {
            ...deviceData,
            w, // 动态宽度
            h, // 动态高度
        };
        e.dataTransfer.setData(DRAG_TYPES.DEVICE, JSON.stringify(deviceWithLayout));
    };

    return (
        <div
            ref={(node) => {
                drag(node);
                preview(node);
            }}
            onDragStart={handleDragStart}
            style={dragStyle}
            className={`draggable-device ${isDragging ? 'dragging' : ''}`}
            title={`拖拽 ${deviceData.name} 到网格布局页面`}
        >
            {/* 渲染设备组件 */}
            {children || (renderDevice && renderDevice(deviceData, animationConfig))}
            
            {/* 拖拽时的视觉反馈 */}
            {isDragging && (
                <div className="absolute inset-0 bg-blue-500 bg-opacity-20 rounded pointer-events-none animate-pulse">
                    <div className="absolute inset-0 border-2 border-dashed border-blue-400 rounded animate-ping"></div>
                </div>
            )}
        </div>
    );
};

export default DraggableDevice; 