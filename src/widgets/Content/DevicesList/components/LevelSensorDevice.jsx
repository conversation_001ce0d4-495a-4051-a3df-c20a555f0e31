import React from 'react';
import BaseDevice, { 
    useDeviceData, 
    useDeviceColors, 
    BG_COLOR,
    DefaultDeviceName 
} from './BaseDevice';
import NumberFlow from '@number-flow/react';

// 定义液位计设备特有的颜色方案
const LEVEL_SENSOR_COLOR_SCHEMES = [
    {
        name: 'cyan',
        borderColor: '#00838F',
        progressColor: '#00BCD4'
    },
    {
        name: 'teal',
        borderColor: '#00695C',
        progressColor: '#009688'
    }
];

/**
 * 自定义垂直边框组件 - 液位计专用
 */
const VerticalBorder = ({ borderColor, borderLineClass }) => (
    <>
        {/* 左边框 */}
        <div className={`absolute top-[0] left-[0] w-[1px] h-full ${borderLineClass}`}
            style={{ backgroundColor: borderColor }}></div>
        
        {/* 右边框 */}
        <div className={`absolute top-[0] right-[0] w-[1px] h-full ${borderLineClass}`}
            style={{ backgroundColor: borderColor }}></div>
        
        {/* 上边框 */}
        <div className={`absolute top-[0] left-[0] w-full h-[1px] ${borderLineClass}`}
            style={{ backgroundColor: borderColor }}></div>
        
        {/* 下边框 */}
        <div className={`absolute bottom-[0] left-[0] w-full h-[1px] ${borderLineClass}`}
            style={{ backgroundColor: borderColor }}></div>
        
        {/* 左上角装饰 */}
        <div className={`absolute top-[0] left-[0] w-[8px] h-[8px] border-l-2 border-t-2 ${borderLineClass}`}
            style={{ borderColor: borderColor }}></div>
        
        {/* 右上角装饰 */}
        <div className={`absolute top-[0] right-[0] w-[8px] h-[8px] border-r-2 border-t-2 ${borderLineClass}`}
            style={{ borderColor: borderColor }}></div>
    </>
);

/**
 * 自定义垂直进度条组件 - 液位计专用
 */
const VerticalProgressBar = ({ deviceData, progressColor }) => (
    <div className="absolute right-[5px] top-[15px] bottom-[25px] w-[8px] bg-[#363A3E] rounded-sm">
        {/* 垂直进度条 */}
        <div 
            className="absolute bottom-0 left-0 w-full rounded-sm transition-all duration-700 ease-in-out"
            style={{ 
                height: `${deviceData.progress}%`,
                backgroundColor: progressColor,
            }}
        >
            {/* 顶部指示器 */}
            <div 
                className="absolute top-[-2px] left-[-2px] w-[12px] h-[2px] rounded-sm"
                style={{ backgroundColor: progressColor }}
            ></div>
        </div>
        
        {/* 刻度线 */}
        <div className="absolute top-0 left-[-3px] w-[6px] h-[1px] bg-[#FFFFFF]"></div>
        <div className="absolute top-[25%] left-[-3px] w-[4px] h-[1px] bg-[#FFFFFF]"></div>
        <div className="absolute top-[50%] left-[-3px] w-[6px] h-[1px] bg-[#FFFFFF]"></div>
        <div className="absolute top-[75%] left-[-3px] w-[4px] h-[1px] bg-[#FFFFFF]"></div>
        <div className="absolute bottom-0 left-[-3px] w-[6px] h-[1px] bg-[#FFFFFF]"></div>
    </div>
);

/**
 * 自定义内容渲染 - 液位计专用
 */
const LevelContent = ({ deviceData, unit, deviceConfig }) => (
    <>
        {/* 单位显示 - 左上角 */}
        <div className="absolute top-[2px] left-[2px] bg-[#161A1B] px-[3px] text-[8px] font-['DingTalkJinBuTi']" style={{ 
            color: '#CAC9CE',
        }}>
            <span>{unit}</span>
        </div>
        
        {/* 百分比显示 - 右上角 */}
        <div className="absolute top-[2px] right-[18px] text-[8px] font-['ChakraPetch-Light']" style={{ 
            color: '#CAC9CE',
        }}>
            <NumberFlow 
                value={Math.round(deviceData.progress)} 
                format={{ minimumIntegerDigits: 1, useGrouping: false }}
                suffix="%"
            />
        </div>
        
        {/* 主要数值显示区域 - 居中 */}
        <div className="flex-grow flex items-center justify-center pr-[15px]">
            <div className="flex flex-col items-center">
                <div className="flex items-baseline">
                    <NumberFlow 
                        value={parseInt(deviceData.value)} 
                        format={{ minimumIntegerDigits: 2, useGrouping: false }}
                        className="text-[#CAC9CE] text-[20px] font-['ChakraPetch-Light']"
                        style={{ '--number-flow-char-height': '1.2em' }}
                        animationDuration={deviceConfig.animationDuration}
                    />
                    <span className="text-[#CAC9CE]">.</span>
                    <NumberFlow 
                        value={parseInt((deviceData.value % 1) * 100)} 
                        format={{ minimumIntegerDigits: 2, useGrouping: false }}
                        className="text-[#CAC9CE] text-[14px] font-['ChakraPetch-Light']"
                        style={{ '--number-flow-char-height': '1.2em' }}
                        animationDuration={deviceConfig.animationDuration}
                    />
                </div>
                
                {/* 状态指示 */}
                <div className="text-[8px] text-[#A9A6A9] mt-[2px]">
                    {deviceData.progress > 80 ? '高位' : 
                     deviceData.progress > 20 ? '正常' : '低位'}
                </div>
            </div>
        </div>
        
        {/* 设备名称 */}
        <DefaultDeviceName deviceData={deviceData} />
    </>
);

/**
 * 自定义边框渲染 - 液位计专用
 */
const LevelBorder = ({ borderColor, borderLineClass, deviceData }) => (
    <>
        <VerticalBorder borderColor={borderColor} borderLineClass={borderLineClass} />
        <VerticalProgressBar deviceData={deviceData} progressColor={borderColor} />
    </>
);

/**
 * 液位计设备组件 - 垂直进度条布局
 * @param {Object} props 
 * @param {Object} props.data - 初始设备数据
 * @returns {JSX.Element}
 */
const LevelSensorDevice = ({ data }) => {
    // 液位计的特殊配置：中等更新频率
    const levelConfig = {
        updateProbability: 0.08,        // 8%更新概率
        checkInterval: 2500,            // 2.5秒检查一次
        minUpdateDelay: 10000,          // 10秒最小延迟
        maxUpdateDelay: 30000,          // 30秒最大延迟
        valueChangeRange: 31,           // 数值变化范围（-15到+15）
        progressMinChange: 8,           // 进度条最小变化8%
        progressChangeRange: 25,        // 进度条变化范围25%
        animationDuration: 600,         // 动画时长
    };

    return (
        <BaseDevice 
            data={data}
            unit="m"
            colorSchemes={LEVEL_SENSOR_COLOR_SCHEMES}
            config={levelConfig}
            renderContent={LevelContent}
            renderBorder={LevelBorder}
            renderProgressBar={() => null} // 不渲染默认进度条，因为已经在边框中渲染了
            containerClassName="level-sensor-container"
        />
    );
};

// 导出颜色方案以便外部使用
export { LEVEL_SENSOR_COLOR_SCHEMES };
export default LevelSensorDevice; 