import React, { useState, useEffect, useRef } from 'react';
import BlockView from './views/BlockView'; // 引入块状视图组件
import ListView from './views/ListView'; // 引入列表视图组件
import { gsap } from 'gsap';
import NumberFlow from '@number-flow/react'; // 引入数字流动组件



// DevicesList 组件现在包含一个顶部标题栏和下部的内容区域
const DevicesList = () => {
    // 添加视图类型状态
    const [viewType, setViewType] = useState('block'); // 默认选中块状视图，可选: 'block', 'list'
    // 添加分类选择状态，默认选中第一个分类
    const [selectedCategory, setSelectedCategory] = useState('全部'); // 默认选中第一个分类
    // 添加设备数量状态，初始值为0
    const [deviceCount, setDeviceCount] = useState(0);
    // 添加视图切换动画状态管理
    const [isViewSwitching, setIsViewSwitching] = useState(false);
    // 存储下一个要切换到的视图类型
    const [nextViewType, setNextViewType] = useState(null);
    // 引用当前内容区域，用于传递关闭动画指令
    const contentAreaRef = useRef(null);
    
    // 添加标题容器和文字的引用
    const titleContainerRef = useRef(null);
    const titleTextRef = useRef(null);
    const deviceCountRef = useRef(null);
    const decorativeLineRef = useRef(null); // 新增装饰线引用
    const titleBottomLineRef = useRef(null); // 新增标题底部线引用
    const blockViewButtonRef = useRef(null); // 块状视图按钮引用
    const listViewButtonRef = useRef(null); // 列表视图按钮引用
    // 添加分类项动画引用
    const categoryItemsRef = useRef([]);
    // 添加分类容器底部线引用
    const categoryBottomLineRef = useRef(null); // 新增分类容器底部线引用
    
    // 添加分类列表容器引用
    const categoryListAreaRef = useRef(null);
    
    // 添加装饰容器显示状态
    const [showDecorativeContainer, setShowDecorativeContainer] = useState(true);

    // 所有分类数据
    const allCategories = [
        '全部',
        '流量计',
        '压力计',
        '阀门',
        '液位计',
        '泵站',
        '水质检测',
        '水厂'
    ];

    // 处理视图切换的函数
    const handleViewSwitch = (newViewType) => {
        // 设置切换状态
        setIsViewSwitching(true);
        setNextViewType(newViewType);
        
        // 检查当前视图容器并触发关闭动画
        const viewContainer = contentAreaRef.current.querySelector(
            viewType === 'block' ? '.block-view-container' : '.list-view-container'
        );

        if (viewContainer) {
            const customEvent = new CustomEvent('triggerCloseAnimation', {
                detail: { 
                    callback: () => {
                        performViewSwitch(newViewType);
                    }
                }
            });
            viewContainer.dispatchEvent(customEvent);
            return;
        }
        
        // 如果没有找到视图容器，直接切换
        performViewSwitch(newViewType);
    };

    // 执行实际的视图切换
    const performViewSwitch = (newViewType) => {
        setViewType(newViewType);
        setIsViewSwitching(false);
        setNextViewType(null);
    };

    // 处理分类切换的函数
    const handleCategoryChange = (newCategory) => {
        // 如果点击的是当前选中的分类，则不执行任何操作
        if (selectedCategory === newCategory) return;

        // 检查当前视图容器并触发关闭动画
        const viewContainer = contentAreaRef.current.querySelector(
            viewType === 'block' ? '.block-view-container' : '.list-view-container'
        );

        if (viewContainer) {
            const customEvent = new CustomEvent('triggerCloseAnimation', {
                detail: {
                    callback: () => {
                        // 动画完成后再更新分类
                        setSelectedCategory(newCategory);
                    }
                }
            });
            viewContainer.dispatchEvent(customEvent);
        } else {
            // 如果没有找到视图容器，直接更新分类
            setSelectedCategory(newCategory);
        }
    };

    // 检测分类列表是否需要滚动的函数
    const checkScrollNeed = () => {
        const categoryListArea = categoryListAreaRef.current;
        if (categoryListArea) {
            // 检查是否有滚动条（内容高度大于容器高度）
            const hasScrollbar = categoryListArea.scrollHeight > categoryListArea.clientHeight;
            setShowDecorativeContainer(!hasScrollbar);
        }
    };
    
    // 监听分类列表容器尺寸变化
    useEffect(() => {
        const categoryListArea = categoryListAreaRef.current;
        if (categoryListArea) {
            // 初始检测
            checkScrollNeed();
            
            // 创建 ResizeObserver 监听容器尺寸变化
            const resizeObserver = new ResizeObserver(() => {
                checkScrollNeed();
            });
            
            resizeObserver.observe(categoryListArea);
            
            // 清理函数
            return () => {
                resizeObserver.disconnect();
            };
        }
    }, [allCategories]); // 依赖分类数据变化
    
    // 初始化动画效果
    useEffect(() => {
        // 获取DOM元素
        const titleContainer = titleContainerRef.current;
        const titleText = titleTextRef.current;
        const deviceCountElement = deviceCountRef.current;
        const decorativeLine = decorativeLineRef.current; // 获取装饰线元素
        const titleBottomLine = titleBottomLineRef.current; // 获取标题底部线元素
        const blockViewButton = blockViewButtonRef.current; // 获取块状视图按钮
        const listViewButton = listViewButtonRef.current; // 获取列表视图按钮
        const categoryItems = categoryItemsRef.current; // 获取分类项元素
        const categoryBottomLine = categoryBottomLineRef.current; // 获取分类容器底部线元素
        const categoryListArea = categoryListAreaRef.current; // 获取分类列表容器元素
        
        if (titleContainer && titleText && deviceCountElement && decorativeLine && titleBottomLine && blockViewButton && listViewButton && categoryBottomLine && categoryListArea) {
            // 设置初始状态 - 确保容器完全隐藏
            gsap.set(titleContainer, { 
                width: 0, 
                overflow: "hidden",
                transformOrigin: "left center" // 确保从左侧开始扩散
            });
            gsap.set(titleText, { 
                opacity: 0,
                color: "#ffffff" // 确保文字颜色为白色
            });
            gsap.set(deviceCountElement, { opacity: 0 });
            // 设置装饰线初始状态
            gsap.set(decorativeLine, { 
                width: 0,
                transformOrigin: "left center"
            });
            // 设置标题底部线初始状态
            gsap.set(titleBottomLine, { 
                width: 0,
                transformOrigin: "left center"
            });
            // 设置视图按钮初始状态 - 从下方向上扩散
            gsap.set([blockViewButton, listViewButton], {
                scaleY: 0, // 初始垂直缩放为0
                opacity: 0,
                transformOrigin: "bottom center" // 从底部开始扩散
            });
            
            // 设置分类项初始状态 - 宽度为0，从左侧扩散
            gsap.set(categoryItems, {
                scaleX: 0, // 初始水平缩放为0
                opacity: 0,
                transformOrigin: "left center" // 从左侧开始扩散
            });
            
            // 设置分类容器底部线初始状态
            gsap.set(categoryBottomLine, { 
                width: 0,
                transformOrigin: "left center"
            });
            
            // 设置分类列表背景初始状态 - 确保完全不可见
            gsap.set(categoryListArea, { 
                opacity: 0,
                visibility: "hidden"
            });
            
            // 创建动画时间线，延迟1秒开始
            const tl = gsap.timeline({ delay: 1 });
            
            // 容器从左向右扩散动画 - 改回原速度
            tl.to(titleContainer, {
                width: "100%", 
                duration: 1.2,
                ease: "power2.out"
            });
            
            // 标题底部线同时扩散动画
            tl.to(titleBottomLine, {
                width: "calc(100% - 130px)", // 扩散到合适宽度，减去右侧按钮区域，增加10px间距
                duration: 1.2,
                ease: "power2.out"
            }, "<"); // 与容器同时开始
            
            // 装饰线同时扩散动画
            tl.to(decorativeLine, {
                width: "150px", // 扩散到原来的宽度
                duration: 1.2,
                ease: "power2.out"
            }, "<"); // 与容器同时开始
            
            // 标题底部线急促闪烁效果 - 与扩散动画完全同步，使用 autoAlpha 和在同一时间开始
            tl.fromTo(titleBottomLine, 
                { autoAlpha: 0.6 },
                { 
                    autoAlpha: 1, 
                    duration: 0.06, 
                    ease: "steps(1)",
                    repeat: 20, // 增加重复次数以覆盖整个扩散时间
                    yoyo: true
                }, "<") // 与扩散动画完全同时开始
            
            // 装饰线急促闪烁效果 - 与扩散动画完全同步，使用 autoAlpha 和在同一时间开始  
            tl.fromTo(decorativeLine, 
                { autoAlpha: 0.6 },
                { 
                    autoAlpha: 1, 
                    duration: 0.05, 
                    ease: "steps(1)",
                    repeat: 24, // 增加重复次数以覆盖整个扩散时间
                    yoyo: true
                }, "<"); // 与扩散动画完全同时开始
            
            // 分类列表背景闪烁显示动画 - 与装饰线同步开始，但持续时间更短
            tl.set(categoryListArea, { visibility: "visible" }, "<") // 与装饰线闪烁同时设置为可见
            .fromTo(categoryListArea, 
                { autoAlpha: 0.3 },
                { 
                    autoAlpha: 1, 
                    duration: 0.05, 
                    ease: "steps(1)",
                    repeat: 12, // 减少重复次数，缩短闪烁时间
                    yoyo: true
                }, "<") // 与装饰线闪烁完全同时开始
            .to(categoryListArea, {
                autoAlpha: 1,
                duration: 0.1,
                ease: "power2.out"
            }); // 最后确保完全显示
            
            // 打字机效果
            tl.to(titleText, {
                opacity: 1,
                color: "#ffffff", // 确保文字颜色为白色
                duration: 0.1,
                onStart: () => {
                    // 打字机效果
                    let text = "监测点列表";
                    titleText.innerHTML = "";
                    titleText.style.color = "#ffffff"; // 强制设置白色
                    let i = 0;
                    const typeInterval = setInterval(() => {
                        if (i < text.length) {
                            titleText.innerHTML += text.charAt(i);
                            i++;
                        } else {
                            clearInterval(typeInterval);
                            // 显示设备数量
                            gsap.to(deviceCountElement, {
                                opacity: 1,
                                duration: 0.3
                            });
                        }
                    }, 100);
                }
            }, "-=0.7"); // 在扩散过程中开始打字机效果
            
            // 块状视图按钮从下往上扩散动效 - 在打字机效果开始时播放
            tl.to(blockViewButton, {
                scaleY: 1, // 垂直扩散到完整尺寸
                opacity: 1,
                duration: 0.2,
                // ease: "back.out(1.7)" // 回弹效果
            }, "<"); // 与打字机效果同时开始
            
            // 列表视图按钮从下往上扩散动效 - 延迟0.3秒后播放
            tl.to(listViewButton, {
                scaleY: 1, // 垂直扩散到完整尺寸
                opacity: 1,
                duration: 0.2,
                // ease: "back.out(1.7)" // 回弹效果
            }, "-=0.4"); // 延迟0.3秒，在块状视图按钮之后播放
            
            // 块状视图按钮急促闪烁效果 - 增加闪烁次数
            tl.to(blockViewButton, {
                filter: "brightness(1.5)", // 使用亮度变化而不是透明度
                duration: 0.03, // 更急促的闪烁
                repeat: 10, // 增加闪烁次数
                yoyo: true,
                ease: "none"
            }, "-=0.3"); // 在扩散过程中开始闪烁
            
            // 列表视图按钮急促闪烁效果 - 增加闪烁次数
            tl.to(listViewButton, {
                filter: "brightness(1.5)", // 使用亮度变化而不是透明度
                duration: 0.03, // 更急促的闪烁
                repeat: 10, // 增加闪烁次数
                yoyo: true,
                ease: "none"
            }, "-=0.25"); // 与块状视图按钮闪烁稍微错开时间
            
            // 分类项开场动画 - 随机顺序播放
            if (categoryItems.length > 0) {
                // 创建随机顺序的索引数组
                const randomIndexes = [...Array(categoryItems.length).keys()].sort(() => Math.random() - 0.5);
                
                // 分类容器底部线扩散动画 - 在分类项动画之前开始
                tl.to(categoryBottomLine, {
                    width: "155px", // 扩散到分类容器宽度
                    duration: 1.0,
                    ease: "power2.out"
                }, "-=0.3"); // 稍微提前开始
                
                // 分类容器底部线急促闪烁效果 - 与扩散动画同步
                tl.fromTo(categoryBottomLine, 
                    { autoAlpha: 0.6 },
                    { 
                        autoAlpha: 1, 
                        duration: 0.06, 
                        ease: "steps(1)",
                        repeat: 16, // 增加重复次数以覆盖整个扩散时间
                        yoyo: true
                    }, "<") // 与扩散动画同时开始
                
                // 延迟0.2秒后开始分类项动画（减少延迟时间，让动画更早开始）
                tl.call(() => {
                    randomIndexes.forEach((index, i) => {
                        const categoryItem = categoryItems[index];
                        if (categoryItem) {
                            // 每个分类项延迟0.1秒播放
                            const delay = i * 0.1;
                            
                            // 左右扩散动画
                            gsap.to(categoryItem, {
                                scaleX: 1,
                                opacity: 1,
                                duration: 0.3,
                                delay: delay,
                                ease: "power2.out"
                            });
                            
                            // 急促闪烁效果 - 使用fromTo实现类似底部线的闪烁效果
                            const flashTl = gsap.timeline({ delay: delay });
                            
                            // 添加类似底部线的闪烁效果 - 与扩散动画同步
                            flashTl.fromTo(categoryItem, 
                                { autoAlpha: 0.6 },
                                { 
                                    autoAlpha: 1, 
                                    duration: 0.06, 
                                    ease: "steps(1)",
                                    repeat: 10, // 增加重复次数以覆盖整个扩散时间
                                    yoyo: true
                                }, 0); // 与扩散动画同时开始
                            
                            // 盒子阴影闪烁效果 - 使用timeline+多个to动画
                            // 初始状态：无发光
                            flashTl.set(categoryItem, { boxShadow: "0 0 0px rgba(62, 174, 102, 0)" }, 0);
                            // 连续闪烁7次
                            for (let j = 0; j < 7; j++) {
                                flashTl.to(categoryItem, { 
                                    boxShadow: "0 0 8px rgba(62, 174, 102, 0.8)", 
                                    duration: 0.02 
                                })
                                .to(categoryItem, { 
                                    boxShadow: "0 0 0px rgba(62, 174, 102, 0)", 
                                    duration: 0.02 
                                });
                            }
                            // 最后恢复正常状态
                            flashTl.set(categoryItem, { boxShadow: "none" });
                        }
                    });
                }, [], "+=0.2"); // 延迟0.2秒后开始分类项动画（减少延迟时间）
            }
        }
    }, []);

    return (
        <div className="devices-list-page-container w-full h-full flex flex-col">
            {/* 新的组合容器：这里是中文注释，说明这个新容器的作用 */}
            <div className="top-header-and-controls w-full h-[25px] flex items-stretch flex-shrink-0 mt-[5px] relative">
                {/* 左侧标题部分：这部分只包含标题文字和数量 - 移除select-none允许文字选择 */}
                <div ref={titleContainerRef} className="title-text-area overflow-hidden relative">
                    <div className="flex items-center mb-[5px] ml-[28px]"> {/* 调整左边距与拖拽手柄对齐 */}
                        <span ref={titleTextRef} className="!text-white text-[14px] font-['DingTalkJinBuTi'] font-bold" style={{color: '#ffffff !important'}}>监测点列表</span>
                        <span ref={deviceCountRef} className="text-[rgb(73,255,255)] text-[12px] font-['ChakraPetch-Light'] ml-[5px]">
                            [<NumberFlow 
                                value={deviceCount} 
                                format={{ minimumIntegerDigits: 2, useGrouping: false }}
                                // transformTiming={{ duration: 1500, easing: 'ease-out' }}
                                // spinTiming={{ duration: 1500, easing: 'ease-out' }}
                                className="inline-block"
                                style={{ '--number-flow-char-height': '0.85em' }}
                            />]
                        </span>
                    </div>
                </div>
                
                {/* 视图按钮呼吸灯动画样式 */}
                <style dangerouslySetInnerHTML={{
                    __html: `
                    @keyframes viewButtonBreathing {
                        0%, 100% { 
                            border-bottom-color: rgba(35, 168, 97, 0.3); 
                        }
                        50% { 
                            border-bottom-color: rgba(35, 168, 97, 1); 
                        }
                    }
                    .view-button-breathing {
                        animation: viewButtonBreathing 1.2s ease-in-out infinite;
                    }
                    `
                }} />

                {/* 右侧视图切换按钮组：按钮现在和标题区域同级 */}
                <div className="view-switch-buttons flex items-center mr-[10px] space-x-2 gap-[10px] ml-[10px]"> {/* ml-[10px] 用于与标题区域的间隔, mb-[5px] 用于垂直对齐 */}
                    {/* 块状视图按钮 */}
                    <div
                        ref={blockViewButtonRef}
                        title="块状视图"
                        role="button"
                        tabIndex="0"
                        className={`w-[50px] h-[25px] flex justify-center items-center focus:outline-none focus:ring-0 cursor-pointer border-b-[3px] ${
                            viewType === 'block' 
                                ? 'border-[#23A861] view-button-breathing' 
                                : 'border-[#666D6D]'
                        }`}
                        onClick={(e) => {
                            // 如果点击的是当前已选中的视图或正在切换中，则不执行任何操作
                            if (viewType === 'block' || isViewSwitching) return;
                            
                            // 开始视图切换动画
                            handleViewSwitch('block');
                            
                            gsap.to(e.currentTarget, {
                                filter: 'brightness(0.8)', 
                                duration: 0.05, 
                                yoyo: true, 
                                repeat: 3,
                                ease: 'power1.inOut' 
                            });
                        }}
                    >
                        {/* 使用 div 和 mask 来动态改变图标颜色：这里是中文注释 */}
                        <div 
                            className={`w-[15px] h-[15px] mb-[3px] ${viewType === 'block' ? 'bg-[#23A861]' : 'bg-[#666D6D]'}`}
                            style={{
                                WebkitMask: 'url(/block.svg) no-repeat center / contain',
                                mask: 'url(/block.svg) no-repeat center / contain'
                            }}
                        ></div>
                    </div>

                                        {/* 列表视图按钮 */}
                    <div
                        ref={listViewButtonRef}
                        title="列表视图"
                        role="button"
                        tabIndex="0"
                        className={`w-[50px] h-[25px] flex justify-center items-center focus:outline-none focus:ring-0 cursor-pointer border-b-[3px] ${
                            viewType === 'list' 
                                ? 'border-[#23A861] view-button-breathing' 
                                : 'border-[#666D6D]'
                        }`}
                        onClick={(e) => {
                            // 如果点击的是当前已选中的视图或正在切换中，则不执行任何操作
                            if (viewType === 'list' || isViewSwitching) return;
                            
                            // 开始视图切换动画
                            handleViewSwitch('list');
                            
                            gsap.to(e.currentTarget, {
                                filter: 'brightness(0.8)', 
                                duration: 0.05, 
                                yoyo: true, 
                                repeat: 3,
                                ease: 'power1.inOut' 
                            });
                        }}
                    >
                        {/* 使用 div 和 mask 来动态改变图标颜色：这里是中文注释 */}
                        <div 
                            className={`w-[15px] h-[15px] mb-[3px] ${viewType === 'list' ? 'bg-[#23A861]' : 'bg-[#666D6D]'}`}
                            style={{
                                WebkitMask: 'url(/list.svg) no-repeat center / contain',
                                mask: 'url(/list.svg) no-repeat center / contain'
                            }}
                        ></div>
                    </div>


                </div>
                        

                {/* 标题区域底部线 - 移到整个容器的底部 */}
                <div ref={titleBottomLineRef} className="absolute bottom-[0px] left-[0px] right-[130px] h-[1px] bg-[#858582]"></div>
                
                {/* 新增的装饰线：从左侧延伸至一半，2px宽，特定颜色。添加 top-auto 确保其不会被意外拉到顶部。 */}
                <div ref={decorativeLineRef} className="absolute top-[23.5px] left-[0] w-[150px] h-[2px] bg-[#BFBEC4] rounded-full"></div>
            </div>

            {/* 底部内容区域 - 修改为左右分栏布局 */}
            <div className="content-area-wrapper flex-grow overflow-hidden w-full relative mt-[10px] flex">
                {/* 左侧分类导航栏 - 从BlockView移动过来 */}
                <div className="left-category-panel w-[155px] flex flex-col flex-shrink-0 font-['ChakraPetch-Light'] relative pb-[10px]">
                    {/* 分类列表显示区域 - 隐藏滚动条样式 */}
                    <style>{`
                        .category-list-area::-webkit-scrollbar {
                            display: none; /* Webkit browsers */
                        }
                        .category-list-area {
                            -ms-overflow-style: none;  /* IE and Edge */
                            scrollbar-width: none;  /* Firefox */
                            background-image: repeating-linear-gradient(
                                -45deg,
                                rgba(32, 46, 45, 0.5),
                                rgba(32, 46, 45, 0.5) 10px,
                                rgba(26, 39, 38, 0.5) 10px,
                                rgba(26, 39, 38, 0.5) 20px
                            );
                            clip-path: polygon(
                                20px 0,
                                100% 0,
                                100% calc(100% - 20px),
                                calc(100% - 20px) 100%,
                                0 100%,
                                0 20px
                            );
                            opacity: 0; /* 初始状态不可见 */
                        }
                        .category-item-clipped {
                            clip-path: polygon(8px 0, 100% 0, 100% calc(100% - 8px), calc(100% - 8px) 100%, 0 100%, 0 8px);
                        }
                    `}</style>
                    <div ref={categoryListAreaRef} className="category-list-area flex-grow overflow-y-auto text-[#A9A6A9] flex flex-col items-start gap-[10px] p-[10px]">
                        {allCategories.map((category, index) => {
                            const isSelected = selectedCategory === category;
                            
                            // 根据分类类型确定小方块颜色
                            let squareColor = '#BCB6B4'; // 默认灰色
                            if (category === '流量计') {
                                squareColor = '#23C26D'; // 流量计绿色
                            } else if (category === '压力计') {
                                squareColor = '#2BA4B9'; // 压力计蓝色
                            } else if (category === '全部') {
                                squareColor = '#3EAE66'; // 全部分类使用原来的绿色
                            }
                            
                            return (
                                <div 
                                    key={category} 
                                    ref={el => categoryItemsRef.current[index] = el} // 添加ref引用
                                    className={`category-item-clipped bg-[#262629] w-[135px] box-border px-[15px] py-[2px] cursor-pointer flex items-center`}
                                    onClick={(e) => {
                                        handleCategoryChange(category); // 调用新的处理函数
                                        gsap.to(e.currentTarget, {
                                            filter: 'brightness(0.8)', 
                                            duration: 0.05, 
                                            yoyo: true, 
                                            repeat: 3,
                                            ease: 'power1.inOut' 
                                        });
                                    }}
                                    onMouseEnter={(e) => gsap.to(e.currentTarget, { x: 5, duration: 0.2, ease: 'power1.out' })}
                                    onMouseLeave={(e) => gsap.to(e.currentTarget, { x: 0, duration: 0.2, ease: 'power1.in' })}
                                >
                                    {/* 左侧小方块 - 根据分类显示对应颜色 */}
                                    <div 
                                        className="w-[5px] h-[5px] rounded-[1px] mr-[10px]"
                                        style={{ backgroundColor: squareColor }}
                                    ></div>
                                    {/* 中间分类名称 */}
                                    <span 
                                        className={`text-[13px] font-['DingTalkJinBuTi',sans-serif] flex-grow text-left ${isSelected ? 'text-[#3EAE66]' : 'text-[#A9A6A9]'}`}
                                    >
                                        {category}
                                    </span>
                                    {/* 右侧指示器 */}
                                    <div className="ml-[10px] flex items-center gap-[2px]">
                                        {isSelected ? (
                                            // 选中状态：3个三角形依次呼吸效果
                                            <>
                                                <svg className="triangle-indicator triangle-1" width="6" height="8" viewBox="0 0 6 8" fill="#3EAE66">
                                                    <path d="M1 0l4 4-4 4z" />
                                                </svg>
                                                <svg className="triangle-indicator triangle-2" width="6" height="8" viewBox="0 0 6 8" fill="#3EAE66">
                                                    <path d="M1 0l4 4-4 4z" />
                                                </svg>
                                                <svg className="triangle-indicator triangle-3" width="6" height="8" viewBox="0 0 6 8" fill="#3EAE66">
                                                    <path d="M1 0l4 4-4 4z" />
                                                </svg>
                                                <style dangerouslySetInnerHTML={{
                                                    __html: `
                                                    @keyframes triangleBreathing {
                                                        0%, 100% { opacity: 0.3; }
                                                        50% { opacity: 1; }
                                                    }
                                                    .triangle-indicator {
                                                        animation: triangleBreathing 1.5s infinite;
                                                    }
                                                    .triangle-1 {
                                                        animation-delay: 0s;
                                                    }
                                                    .triangle-2 {
                                                        animation-delay: 0.3s;
                                                    }
                                                    .triangle-3 {
                                                        animation-delay: 0.6s;
                                                    }
                                                    `
                                                }} />
                                            </>
                                        ) : (
                                            // 未选中状态：单个三角形
                                            <svg width="8" height="12" viewBox="0 0 8 12" fill="#A9A6A9">
                                                <path d="M2 0l6 6-6 6z" />
                                            </svg>
                                        )}
                                    </div>
                                </div>
                            );
                        })}
                        
                        {/* 装饰容器 - 在分类按钮底部，作为占位符使用，根据滚动情况显示/隐藏 */}
                        {showDecorativeContainer && (
                            <div className="decorative-placeholder flex-grow w-full flex justify-center mt-[10px]">
                            <div className="w-[135px] h-full relative">
                                {/* 绘制边框线条 - 使用1px宽度，颜色#4E5856 */}
                                {/* 顶边 */}
                                <div className="absolute top-[0px] left-[0px] w-full h-[1px] bg-[rgba(54,58,62,0.5)]"></div>
                                {/* 左边 */}
                                <div className="absolute top-[0px] left-[0px] w-[1px] h-full bg-[rgba(54,58,62,0.5)]"></div>
                                {/* 右边 - 到斜切角位置 */}
                                <div 
                                    className="absolute top-[0px] right-[0px] w-[1px] bg-[rgba(54,58,62,0.5)]"
                                    style={{
                                        height: 'calc(100% - 15px)'
                                    }}
                                ></div>
                                {/* 底边 - 到斜切角位置 */}
                                <div 
                                    className="absolute bottom-[0px] left-[0px] bg-[rgba(54,58,62,0.5)] h-[1px]"
                                    style={{
                                        width: 'calc(100% - 15px)'
                                    }}
                                ></div>
                                {/* 斜切角边 - 从右边底部到底边右端的连接线 */}
                                <div 
                                    className="absolute w-[1px] bg-[rgba(54,58,62,0.5)] h-[21.21px] bottom-[1px] right-[15px]"
                                    style={{
                                        transform: 'rotate(45deg)',
                                        transformOrigin: 'bottom left'
                                    }}
                                ></div>
                            </div>
                        </div>
                        )}
                    </div>
                    
                    {/* 分类容器底部线 - 移到分类列表容器外部但仍在left-category-panel内 */}
                    <div ref={categoryBottomLineRef} className="absolute bottom-[0px] left-0 w-[155px] h-[2px] bg-[#3EAE66]"></div>
                </div>

                {/* 右侧内容区域 (用于显示 BlockView 或 ListView) */}
                <div ref={contentAreaRef} className="right-content-area flex-grow overflow-hidden relative">
                    {viewType === 'block' ? (
                        <BlockView 
                            selectedCategory={selectedCategory}
                            onDeviceCountChange={setDeviceCount}
                        />
                    ) : viewType === 'list' ? (
                        <ListView 
                            selectedCategory={selectedCategory}
                            onDeviceCountChange={setDeviceCount}
                        />
                    ) : null}
                </div>
            </div>
        </div>
    );
};

export default DevicesList;
