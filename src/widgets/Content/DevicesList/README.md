# 设备组件架构使用指南

## 🎯 架构概述

这是一个高度可扩展的设备监控组件架构，支持**完全自定义的UI布局**，同时保持**数据管理逻辑的复用**。

## 🏗️ 架构层次

### 1. 基础层 - BaseDevice
- **数据管理**: `useDeviceData` Hook
- **颜色管理**: `useDeviceColors` Hook  
- **默认组件**: 提供可复用的UI组件
- **渲染抽象**: 支持自定义渲染函数

### 2. 组件层 - 具体设备组件
- **FlowMeterDevice**: 流量计（默认布局）
- **PressureGaugeDevice**: 压力计（默认布局）
- **ValveControllerDevice**: 阀门控制器（圆形布局）
- **LevelSensorDevice**: 液位计（垂直布局）

### 3. 工厂层 - DeviceFactory
- **统一管理**: 所有设备类型的元数据
- **批量创建**: 设备数据生成
- **类型映射**: 分类到设备类型的映射

## 🚀 使用方式

### 方式一：使用默认布局（简单）

```javascript
import BaseDevice from './BaseDevice';

const MyDevice = ({ data }) => (
    <BaseDevice 
        data={data}
        unit="单位"
        colorSchemes={MY_COLOR_SCHEMES}
        config={myConfig} // 可选
    />
);
```

### 方式二：自定义UI布局（灵活）

```javascript
import BaseDevice, { 
    useDeviceData, 
    useDeviceColors,
    DefaultDeviceName 
} from './BaseDevice';

const CustomDevice = ({ data }) => {
    // 自定义内容渲染
    const renderContent = ({ deviceData, unit, deviceConfig }) => (
        <div className="my-custom-layout">
            {/* 你的自定义UI */}
        </div>
    );
    
    // 自定义边框渲染
    const renderBorder = ({ borderColor, borderLineClass }) => (
        <div className="my-custom-border">
            {/* 你的自定义边框 */}
        </div>
    );
    
    // 自定义进度条渲染
    const renderProgressBar = ({ deviceData, progressColor }) => (
        <div className="my-custom-progress">
            {/* 你的自定义进度条 */}
        </div>
    );

    return (
        <BaseDevice 
            data={data}
            unit="单位"
            colorSchemes={MY_COLOR_SCHEMES}
            renderContent={renderContent}
            renderBorder={renderBorder}
            renderProgressBar={renderProgressBar}
            containerClassName="my-custom-container"
        />
    );
};
```

### 方式三：完全自定义（最灵活）

```javascript
import { useDeviceData, useDeviceColors } from './BaseDevice';

const FullyCustomDevice = ({ data }) => {
    const { deviceData, deviceConfig } = useDeviceData(data, myConfig);
    const { borderColor, progressColor, borderLineClass } = useDeviceColors(deviceData, colorSchemes);
    
    return (
        <div className="completely-custom-device">
            {/* 完全自定义的UI结构 */}
        </div>
    );
};
```

## 📋 现有设备类型示例

### 1. 默认布局设备
- **FlowMeterDevice**: 矩形边框 + 水平进度条
- **PressureGaugeDevice**: 矩形边框 + 水平进度条

### 2. 圆形布局设备
- **ValveControllerDevice**: 圆形边框 + 圆形进度条 + 中心百分比显示

### 3. 垂直布局设备
- **LevelSensorDevice**: 矩形边框 + 垂直进度条 + 状态指示

## 🔧 配置选项

```javascript
const deviceConfig = {
    updateProbability: 0.10,        // 更新概率 (0-1)
    checkInterval: 2000,            // 检查间隔（毫秒）
    minUpdateDelay: 8000,           // 最小更新延迟（毫秒）
    maxUpdateDelay: 25000,          // 最大更新延迟（毫秒）
    valueChangeRange: 41,           // 数值变化范围
    progressMinChange: 15,          // 进度条最小变化幅度
    progressChangeRange: 40,        // 进度条变化范围
    animationDuration: 500,         // 数字动画时长（毫秒）
};
```

## 🎨 颜色方案

```javascript
const COLOR_SCHEMES = [
    {
        name: 'primary',
        borderColor: '#26764D',     // 边框颜色
        progressColor: '#23C26D'    // 进度条颜色
    }
];
```

## 🆕 添加新设备类型

### 步骤1: 创建设备组件

```javascript
// MyNewDevice.jsx
import BaseDevice from './BaseDevice';

const MyNewDevice = ({ data }) => (
    <BaseDevice 
        data={data}
        unit="新单位"
        colorSchemes={NEW_COLOR_SCHEMES}
        config={newConfig}
        // 可选：自定义渲染函数
    />
);

export { NEW_COLOR_SCHEMES };
export default MyNewDevice;
```

### 步骤2: 在工厂中注册

```javascript
// DeviceFactory.js
import MyNewDevice, { NEW_COLOR_SCHEMES } from '../components/MyNewDevice';

export const DEVICE_TYPES = {
    // ... 现有类型
    MY_NEW_DEVICE: 'myNewDevice'
};

export const DEVICE_TYPE_METADATA = {
    // ... 现有元数据
    [DEVICE_TYPES.MY_NEW_DEVICE]: {
        name: '新设备',
        component: MyNewDevice,
        colorSchemes: NEW_COLOR_SCHEMES,
        unit: '新单位',
        defaultConfig: { /* 配置 */ }
    }
};
```

### 步骤3: 添加分类映射

```javascript
// DeviceFactory.js
export const getDeviceTypesByCategory = (categoryName) => {
    switch (categoryName) {
        // ... 现有映射
        case '新分类':
            return [DEVICE_TYPES.MY_NEW_DEVICE];
        default:
            return [];
    }
};
```

## 🔌 Socket集成准备

架构已为Socket集成做好准备：

```javascript
// 在设备组件中
const MyDevice = ({ data }) => {
    const { deviceData, setDeviceData } = useDeviceData(data, config);
    
    // Socket监听
    useEffect(() => {
        socket.on(`device-${data.id}`, (newData) => {
            setDeviceData(newData);
        });
        
        return () => socket.off(`device-${data.id}`);
    }, [data.id]);
    
    // ... 渲染逻辑
};
```

## 🎯 架构优势

1. **🔧 高度可配置**: 每个设备独立配置更新频率、动画等
2. **🎨 UI完全自定义**: 支持任意布局设计
3. **🔄 逻辑复用**: 数据管理、颜色管理逻辑复用
4. **📈 易于扩展**: 新设备类型只需几行代码
5. **🛠️ 易于维护**: 修改共性功能只需改基础组件
6. **🔌 Socket友好**: 每个设备独立管理数据
7. **🧪 便于测试**: 各层可独立测试

## 📊 性能优化

- **Hook复用**: 数据管理逻辑通过Hook复用
- **按需渲染**: 只有数据变化时才重新渲染
- **动画优化**: 使用CSS过渡和NumberFlow优化动画
- **内存管理**: 组件卸载时自动清理定时器

这个架构完美平衡了**代码复用**和**UI灵活性**，让你既能快速开发标准设备，又能完全自定义特殊设备的UI布局！🎉 