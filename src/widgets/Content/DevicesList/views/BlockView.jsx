import React, { useState, useEffect, useRef, useCallback } from 'react';
import { gsap } from 'gsap';
import { Responsive, WidthProvider } from 'react-grid-layout';
import NumberFlow from '@number-flow/react';

// 引入设备组件和颜色方案
import FlowMeterDevice, { FLOW_METER_COLOR_SCHEMES } from '../components/FlowMeterDevice';
import PressureGaugeDevice, { PRESSURE_GAUGE_COLOR_SCHEMES } from '../components/PressureGaugeDevice';
import DraggableDevice from '../components/DraggableDevice';

// 定义颜色常量
const BG_COLOR = '#262629';
const PROGRESS_BG_COLOR = '#363A3E';

// 创建响应式网格布局组件
const ResponsiveGridLayout = WidthProvider(Responsive);

/**
 * 自定义Hook - 定时器
 * @param {Function} callback - 回调函数
 * @param {number} delay - 延迟时间（毫秒）
 */
function useInterval(callback, delay) {
    const savedCallback = useRef();
  
    // 记住最新的回调函数
    useEffect(() => {
        savedCallback.current = callback;
    }, [callback]);
  
    // 设置定时器
    useEffect(() => {
        function tick() {
            savedCallback.current();
        }
        if (delay !== null) {
            const id = setInterval(tick, delay);
            return () => clearInterval(id);
        }
    }, [delay]);
}

/**
 * 块状视图组件 - 显示设备网格
 * @param {Object} props - 组件属性
 * @param {Array} props.devices - 设备数据数组
 * @param {string} props.selectedCategory - 当前选中的设备分类
 * @param {Function} props.onDeviceCountChange - 设备数量变化回调函数
 * @returns {JSX.Element} 块状视图组件
 */
const BlockView = ({ devices, selectedCategory, onDeviceCountChange }) => {
    // 生成随机设备数据用于展示
    const [deviceItems, setDeviceItems] = useState([]);
    // 设备动画状态管理 - 存储设备ID和对应的动画配置
    const [animatedDevices, setAnimatedDevices] = useState(new Map());
    // 关闭动画状态管理
    const [isClosingAnimation, setIsClosingAnimation] = useState(false);
    // 网格布局引用
    const gridRef = useRef(null);
    // 设备更新定时器ID引用
    const deviceTimersRef = useRef([]);
    // 设备动画定时器引用
    const animationTimersRef = useRef([]);
    // 关闭动画定时器引用
    const closingTimersRef = useRef([]);
    // 存储上一次的selectedCategory，用于检测变化
    const prevSelectedCategoryRef = useRef(selectedCategory);
    
    // 当选中分类变化时，先播放关闭动画，然后重新生成设备数据
    useEffect(() => {
        // 检查是否是真正的分类变化（而不是初始渲染）
        const isRealCategoryChange = prevSelectedCategoryRef.current && 
                                     prevSelectedCategoryRef.current !== selectedCategory && 
                                     deviceItems.length > 0;
        
        // 更新ref为当前值
        prevSelectedCategoryRef.current = selectedCategory;
        
        if (isRealCategoryChange) {
            // 分类发生变化且当前有设备，先播放关闭动画
            setIsClosingAnimation(true);
            
            // 清除旧的关闭动画定时器
            closingTimersRef.current.forEach(timerId => clearTimeout(timerId));
            closingTimersRef.current = [];
            
                         // 为每个设备设置关闭动画
             const closingAnimationMap = new Map();
             deviceItems.forEach((item, index) => {
                 // 生成0-150ms的随机延迟，让设备依次关闭
                 const delay = Math.random() * 150;
                 
                 const timerId = setTimeout(() => {
                     closingAnimationMap.set(item.id, { 
                         isClosing: true,
                         duration: 0.15 // 关闭动画持续150ms
                     });
                     setAnimatedDevices(prev => new Map([...prev, ...closingAnimationMap]));
                 }, delay);
                 
                 closingTimersRef.current.push(timerId);
             });
             
             // 等待所有关闭动画完成后（300ms后），执行分类切换逻辑
             const switchTimerId = setTimeout(() => {
                 executeCategory切换Logic(selectedCategory);
             }, 300);
            
            closingTimersRef.current.push(switchTimerId);
        } else {
            // 初始渲染或其他情况，直接执行分类切换逻辑
            executeCategory切换Logic(selectedCategory);
        }
    }, [selectedCategory]);
    
    // 分类切换的实际逻辑（从原useEffect中提取出来）
    const executeCategory切换Logic = (category) => {
        if (category) {
            // 先清空设备数据，显示加载状态
            setDeviceItems([]);
            setIsClosingAnimation(false);
            
            // 通知父组件设备数量为0
            if (onDeviceCountChange) {
                onDeviceCountChange(0);
            }
            
            // 检查是否为允许显示设备的分类
            const isValidCategory = category === '全部' || 
                                   category === '流量计' || 
                                   category === '压力计';
            
            if (!isValidCategory) {
                // 如果不是有效分类，保持空数组状态
                return;
            }
            
            // 模拟API加载延迟（500-1000ms）
            const loadingDelay = 500 + Math.random() * 500;
            
            setTimeout(() => {
                const items = [];
                
                // 判断设备类型
                const isFlowMeter = category === '流量计';
                const isPressureGauge = category === '压力计';
                const isAll = category === '全部';
                
                if (isAll) {
                    // "全部"分类：生成12个流量计 + 12个压力计 = 总共24个
                    for (let i = 0; i < 12; i++) {
                        // 流量计设备数据
                        const flowMeterDevice = {
                            id: `flowMeter-${i}`,
                            value: Math.floor(Math.random() * 1000) + parseFloat((Math.random()).toFixed(2)),
                            progress: 30 + Math.random() * 40,
                            nextUpdateTime: Math.floor(Math.random() * 8000),
                            alarmLowerLimit: Math.max(10, Math.floor(Math.random() * 30)),
                            alarmUpperLimit: Math.min(90, 70 + Math.floor(Math.random() * 20)),
                            type: 'flowMeter',
                            name: `流量计 ${i+1}`,
                            colorScheme: FLOW_METER_COLOR_SCHEMES[0]
                        };
                        items.push(flowMeterDevice);
                        
                        // 压力计设备数据
                        const pressureGaugeDevice = {
                            id: `pressureGauge-${i}`,
                            value: Math.floor(Math.random() * 1000) + parseFloat((Math.random()).toFixed(2)),
                            progress: 30 + Math.random() * 40,
                            nextUpdateTime: Math.floor(Math.random() * 8000),
                            alarmLowerLimit: Math.max(10, Math.floor(Math.random() * 30)),
                            alarmUpperLimit: Math.min(90, 70 + Math.floor(Math.random() * 20)),
                            type: 'pressureGauge',
                            name: `压力计 ${i+1}`,
                            colorScheme: PRESSURE_GAUGE_COLOR_SCHEMES[0]
                        };
                        items.push(pressureGaugeDevice);
                    }
                } else if (isFlowMeter) {
                    // 只显示流量计：生成12个流量计
                    for (let i = 0; i < 12; i++) {
                        const flowMeterDevice = {
                            id: `flowMeter-${i}`,
                            value: Math.floor(Math.random() * 1000) + parseFloat((Math.random()).toFixed(2)),
                            progress: 30 + Math.random() * 40,
                            nextUpdateTime: Math.floor(Math.random() * 8000),
                            alarmLowerLimit: Math.max(10, Math.floor(Math.random() * 30)),
                            alarmUpperLimit: Math.min(90, 70 + Math.floor(Math.random() * 20)),
                            type: 'flowMeter',
                            name: `流量计 ${i+1}`,
                            colorScheme: FLOW_METER_COLOR_SCHEMES[0]
                        };
                        items.push(flowMeterDevice);
                    }
                } else if (isPressureGauge) {
                    // 只显示压力计：生成12个压力计
                    for (let i = 0; i < 12; i++) {
                        const pressureGaugeDevice = {
                            id: `pressureGauge-${i}`,
                            value: Math.floor(Math.random() * 1000) + parseFloat((Math.random()).toFixed(2)),
                            progress: 30 + Math.random() * 40,
                            nextUpdateTime: Math.floor(Math.random() * 8000),
                            alarmLowerLimit: Math.max(10, Math.floor(Math.random() * 30)),
                            alarmUpperLimit: Math.min(90, 70 + Math.floor(Math.random() * 20)),
                            type: 'pressureGauge',
                            name: `压力计 ${i+1}`,
                            colorScheme: PRESSURE_GAUGE_COLOR_SCHEMES[0]
                        };
                        items.push(pressureGaugeDevice);
                    }
                }
            
                setDeviceItems(items);
                
                // 通知父组件更新设备数量
                if (onDeviceCountChange) {
                    onDeviceCountChange(items.length);
                }
                
                // 清除旧的定时器
                deviceTimersRef.current.forEach(timerId => clearTimeout(timerId));
                deviceTimersRef.current = [];
                
                // 清除旧的动画定时器
                animationTimersRef.current.forEach(timerId => clearTimeout(timerId));
                animationTimersRef.current = [];
                
                // 重置动画状态
                setAnimatedDevices(new Map());
                
                // 为每个设备创建随机延迟的进场动画
                items.forEach((item, index) => {
                    // 生成1-2秒的随机延迟
                    const delay = 1000 + Math.random() * 1000;
                    // 生成0.2-0.5秒的随机动画持续时间
                    const duration = 0.2 + Math.random() * 0.3;
                    
                    // 开始容器闪烁动画
                    const containerTimerId = setTimeout(() => {
                        setAnimatedDevices(prev => new Map([...prev, [item.id, { 
                            duration, 
                            containerAnimated: true,
                            numberAnimated: false,
                            originalValue: item.value
                        }]]));
                    }, delay);
                    
                    // 在容器闪烁动画结束时开始数字动画
                    const numberTimerId = setTimeout(() => {
                        setAnimatedDevices(prev => {
                            const newMap = new Map(prev);
                            const config = newMap.get(item.id);
                            if (config) {
                                newMap.set(item.id, { ...config, numberAnimated: true });
                            }
                            return newMap;
                        });
                    }, delay + duration * 1000);
                    
                    animationTimersRef.current.push(containerTimerId, numberTimerId);
                });
            }, loadingDelay);
        }
    };
    
    // 清理组件卸载时的定时器
    useEffect(() => {
        return () => {
            deviceTimersRef.current.forEach(timerId => clearTimeout(timerId));
            animationTimersRef.current.forEach(timerId => clearTimeout(timerId));
            closingTimersRef.current.forEach(timerId => clearTimeout(timerId));
        };
    }, []);

    // 监听来自DevicesList的关闭动画触发事件
    useEffect(() => {
        const handleCloseAnimation = (event) => {
            const { callback } = event.detail;
            
            // 只有在有设备的情况下才执行关闭动画
            if (deviceItems.length === 0) {
                // 如果没有设备，直接执行回调
                if (callback) callback();
                return;
            }
            
            // 执行关闭动画逻辑（复用分类切换的关闭动画逻辑）
            setIsClosingAnimation(true);
            
            // 清除旧的关闭动画定时器
            closingTimersRef.current.forEach(timerId => clearTimeout(timerId));
            closingTimersRef.current = [];
            
            // 为每个设备设置关闭动画
            const closingAnimationMap = new Map();
            deviceItems.forEach((item, index) => {
                // 生成0-150ms的随机延迟，让设备依次关闭
                const delay = Math.random() * 150;
                
                const timerId = setTimeout(() => {
                    closingAnimationMap.set(item.id, { 
                        isClosing: true,
                        duration: 0.15 // 关闭动画持续150ms
                    });
                    setAnimatedDevices(prev => new Map([...prev, ...closingAnimationMap]));
                }, delay);
                
                closingTimersRef.current.push(timerId);
            });
            
            // 等待所有关闭动画完成后（300ms后），执行回调
            const callbackTimerId = setTimeout(() => {
                if (callback) callback();
            }, 300);
            
            closingTimersRef.current.push(callbackTimerId);
        };

        const container = gridRef.current;
        if (container) {
            container.addEventListener('triggerCloseAnimation', handleCloseAnimation);
            return () => {
                container.removeEventListener('triggerCloseAnimation', handleCloseAnimation);
            };
        }
    }, [deviceItems]);
    
    // 更新进度条的函数 - 确保产生明显变化
    const getProgressChange = (currentProgress) => {
        // 大幅度变化进度条（-20%到+20%）
        const minChange = 15; // 至少变化15%
        let progressDelta = (Math.random() * 40) - 20;
        
        // 确保变化幅度足够大
        if (Math.abs(progressDelta) < minChange) {
            progressDelta = progressDelta >= 0 ? minChange : -minChange;
        }
        
        // 确保进度条值在合理范围内
        return Math.min(90, Math.max(10, currentProgress + progressDelta));
    };
    
    // 定时检查设备更新
    useInterval(() => {
        if (deviceItems.length > 0) {
            const now = Date.now();
            
            // 检查是否有设备需要更新
            setDeviceItems(prevItems => {
                // 首先检查是否需要更新，避免不必要的映射操作
                const needsUpdate = prevItems.some(item => now >= item.nextUpdateTime);
                
                // 如果没有设备需要更新，直接返回原始数组，避免不必要的渲染
                if (!needsUpdate) return prevItems;
                
                let hasUpdates = false;
                const newItems = prevItems.map(item => {
                    // 检查是否到达更新时间
                    if (now >= item.nextUpdateTime) {
                        // 45%的概率更新设备数值和进度条（提高概率）
                        if (Math.random() < 0.45) {
                            // 随机变化数值（-20到+20）
                            const valueDelta = Math.floor(Math.random() * 41) - 20;
                            // 生成新的小数部分
                            const newDecimal = parseFloat((Math.random()).toFixed(2));
                            // 新的值是整数部分变化加上新的小数部分
                            const newValue = Math.max(0, item.value - (item.value % 1) + valueDelta) + newDecimal;
                            
                            // 当且仅当数值变化时，同时更新进度条
                            const newProgress = getProgressChange(item.progress);
                            
                            // 设置下一次更新时间（3-8秒后）
                            const nextUpdateDelay = 3000 + Math.floor(Math.random() * 5000);
                            
                            hasUpdates = true;
                            return {
                                ...item,
                                value: newValue,
                                progress: newProgress, // 只在数值变化时更新进度条
                                nextUpdateTime: now + nextUpdateDelay
                            };
                        }
                        
                        // 即使不更新数值，也保持对象引用不变，只更新nextUpdateTime属性
                        const nextUpdateDelay = 3000 + Math.floor(Math.random() * 5000);
                        
                        // 直接修改原对象的nextUpdateTime而不创建新对象
                        item.nextUpdateTime = now + nextUpdateDelay;
                        return item;
                    }
                    return item;
                });
                
                // 仅在有实际更新时返回新数组
                return hasUpdates ? newItems : prevItems;
            });
        }
    }, 1800); // 每1.8秒检查一次是否有设备需要更新（提高频率）
    
    // 定义断点和列数配置
    const breakpoints = { xxl: 1600, xl: 1400, lg: 1200, md: 996, sm: 768, xs: 480, xxs: 320, xxxs: 240 };
    const cols = { xxl: 10, xl: 8, lg: 6, md: 5, sm: 4, xs: 3, xxs: 2, xxxs: 1 };
    
    // 生成网格布局配置 - 根据断点动态调整列数
    const generateLayout = useCallback((breakpoint) => {
        // 使用传入的断点对应的列数
        const colsForBreakpoint = cols[breakpoint] || 6;
        
        return deviceItems.map((item, index) => ({
            i: item.id,
            x: index % colsForBreakpoint, // 根据断点动态调整每行设备数
            y: Math.floor(index / colsForBreakpoint), // 自动换行
            w: 1,
            h: 1,
            static: true // 设置为静态，不可拖动和调整大小
        }));
    }, [deviceItems]);

    // 根据设备类型渲染对应的组件
    const renderDeviceByType = (item, animationConfig) => {
        // 创建带动画状态的设备数据
        const deviceDataWithAnimation = {
            ...item,
            // 如果数字动画还没开始，显示0值
            value: animationConfig?.numberAnimated ? item.value : 0,
            // 如果进度条动画还没开始，显示0进度
            progress: animationConfig?.numberAnimated ? item.progress : 0,
            // 传递动画配置
            _animationConfig: animationConfig
        };
        
        switch (item.type) {
            case 'pressureGauge':
                return <PressureGaugeDevice data={deviceDataWithAnimation} />;
            case 'flowMeter':
            default:
                return <FlowMeterDevice data={deviceDataWithAnimation} />;
        }
    };

    return (
        <div className="block-view-container w-full h-full font-['ChakraPetch-Light']" ref={gridRef}>
            {/* 添加呼吸灯动画和设备容器进场动画样式 */}
            <style dangerouslySetInnerHTML={{
                __html: `
                @keyframes borderBreathingHigh {
                    0%, 100% { background-color: rgba(184, 63, 49, 0.4); }
                    50% { background-color: rgba(184, 63, 49, 1); }
                }
                @keyframes borderBreathingLow {
                    0%, 100% { background-color: rgba(227, 157, 37, 0.4); }
                    50% { background-color: rgba(227, 157, 37, 1); }
                }
                .border-line-high {
                    animation: borderBreathingHigh 0.8s infinite;
                }
                .border-line-low {
                    animation: borderBreathingLow 0.8s infinite;
                }
                
                /* 设备容器进场动画 - 急促闪烁效果 */
                @keyframes deviceEntrance {
                    0% { opacity: 0; }
                    15% { opacity: 0.8; }
                    30% { opacity: 0.2; }
                    45% { opacity: 0.7; }
                    60% { opacity: 0.1; }
                    75% { opacity: 1; }
                    90% { opacity: 0.2; }
                    100% { opacity: 1; }
                }
                
                /* 设备容器关闭动画 - 急促闪烁关闭效果（进场动画的倒放） */
                @keyframes deviceExit {
                    0% { opacity: 1; }
                    10% { opacity: 0.2; }
                    25% { opacity: 1; }
                    40% { opacity: 0.1; }
                    55% { opacity: 0.7; }
                    70% { opacity: 0.2; }
                    85% { opacity: 0.8; }
                    100% { opacity: 0; }
                }
                
                .device-item {
                    opacity: 0; /* 初始状态不可见 */
                }
                
                .device-item-animated {
                    animation: deviceEntrance var(--animation-duration, 0.3s) ease-in-out forwards;
                }
                
                .device-item-closing {
                    animation: deviceExit var(--closing-duration, 0.15s) ease-in-out forwards;
                }
                
                /* 拖拽相关样式 */
                .draggable-device {
                    position: relative;
                    height: 100%;
                    width: 100%;
                }
                
                .draggable-device.dragging {
                    z-index: 1000;
                }
                
                .draggable-device:hover {
                    transform: scale(1.02);
                }
                
                .draggable-device:active {
                    transform: scale(0.98);
                }
                `
            }} />
            
            {/* 设备网格区域 */}
            <div className="devices-grid w-full h-full p-[0px] overflow-y-auto">
                {/* 根据选中的分类显示相应的设备内容 */}
                {selectedCategory ? (
                    <div className="selected-category-content">
                        {deviceItems.length > 0 ? (
                            <ResponsiveGridLayout
                                className="layout"
                                layouts={{
                                    xxl: generateLayout('xxl'),
                                    xl: generateLayout('xl'),
                                    lg: generateLayout('lg'),
                                    md: generateLayout('md'),
                                    sm: generateLayout('sm'),
                                    xs: generateLayout('xs'),
                                    xxs: generateLayout('xxs'),
                                    xxxs: generateLayout('xxxs') // 添加xxxs布局
                                }}
                                breakpoints={breakpoints}
                                cols={cols} // 使用定义的列数配置
                                rowHeight={80}
                                margin={[15, 20]} // 设置间距
                                containerPadding={[5, 5]}
                                isDraggable={false}
                                isResizable={false}
                                compactType="horizontal" // 使用水平方向的紧凑类型
                                style={{ maxWidth: '100%' }} // 确保不超出父容器
                            >
                                {deviceItems.map((item) => {
                                    const animationConfig = animatedDevices.get(item.id);
                                    const isAnimated = animationConfig?.containerAnimated;
                                    const isClosing = animationConfig?.isClosing;
                                    
                                    // 确定动画类名
                                    let animationClass = '';
                                    if (isClosing) {
                                        animationClass = 'device-item-closing';
                                    } else if (isAnimated) {
                                        animationClass = 'device-item-animated';
                                    }
                                    
                                    return (
                                        <div 
                                            key={item.id} 
                                            className={`device-item rounded flex flex-col items-center justify-start p-0 overflow-visible relative ${animationClass}`}
                                            style={{
                                                maxHeight: '100%',
                                                minHeight: '50px',
                                                minWidth: '100px', // 减小最小宽度
                                                width: '100%', // 确保填充可用空间
                                                '--animation-duration': isAnimated ? `${animationConfig.duration}s` : '0.4s',
                                                '--closing-duration': isClosing ? `${animationConfig.duration}s` : '0.15s'
                                            }}
                                        >
                                            {/* 使用可拖拽的设备组件包装器 */}
                                            <DraggableDevice
                                                deviceData={item}
                                                animationConfig={animationConfig}
                                                renderDevice={renderDeviceByType}
                                            />
                                        </div>
                                    );
                                })}
                            </ResponsiveGridLayout>
                        ) : (
                            // 去掉加载文字，直接显示空白
                            <div></div>
                        )}
                    </div>
                ) : (
                    <div className="no-category-selected text-[#A9A6A9] text-[14px] flex items-center justify-center h-full">
                        请从左侧选择一个分类查看设备
                    </div>
                )}
            </div>
        </div>
    );
};

export default BlockView;