import React, { useState, useEffect, useRef } from 'react';
import FlowMeterListItem from '../components/FlowMeterListItem';
import PressureGaugeListItem from '../components/PressureGaugeListItem';
import DraggableDevice from '../components/DraggableDevice';

// 列表视图组件 - 现在支持根据选中分类显示内容
const ListView = ({ selectedCategory, onDeviceCountChange }) => {
    // 设备数据状态管理
    const [deviceItems, setDeviceItems] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    // 关闭动画状态管理 - 现在使用Map来单独控制每个设备
    const [closingItems, setClosingItems] = useState(new Map());
    // 容器引用
    const listViewRef = useRef(null);
    // 关闭动画定时器引用
    const closingTimersRef = useRef([]);
    // 存储上一次的selectedCategory，用于检测变化
    const prevSelectedCategoryRef = useRef(selectedCategory);

    // 检查是否为允许显示设备的分类
    const isValidCategory = selectedCategory === '全部' || 
                           selectedCategory === '流量计' || 
                           selectedCategory === '压力计';

    // 触发关闭动画的函数
    const triggerClosingAnimation = (callback) => {
        if (deviceItems.length === 0) {
            if (callback) callback();
            return;
        }

        closingTimersRef.current.forEach(timerId => clearTimeout(timerId));
        closingTimersRef.current = [];

        const newClosingItems = new Map();
        deviceItems.forEach((item, index) => {
            const delay = Math.random() * 150; // 0-150ms的随机延迟
            const timerId = setTimeout(() => {
                setClosingItems(prev => new Map(prev).set(item.id, true));
            }, delay);
            closingTimersRef.current.push(timerId);
        });
        
        // 等待所有动画完成后执行回调
        const callbackTimerId = setTimeout(() => {
            if (callback) callback();
        }, 300); // 150ms (最大延迟) + 150ms (动画时长)
        closingTimersRef.current.push(callbackTimerId);
    };

    // 当选中分类变化时，先播放关闭动画
    useEffect(() => {
        const isRealCategoryChange = prevSelectedCategoryRef.current && 
                                     prevSelectedCategoryRef.current !== selectedCategory && 
                                     deviceItems.length > 0;

        prevSelectedCategoryRef.current = selectedCategory;

        if (isRealCategoryChange) {
            triggerClosingAnimation(() => {
                executeCategoryChangeLogic(selectedCategory);
            });
        } else {
            executeCategoryChangeLogic(selectedCategory);
        }
    }, [selectedCategory]);

    // 监听来自DevicesList的关闭动画触发事件
    useEffect(() => {
        const handleCloseAnimation = (event) => {
            triggerClosingAnimation(event.detail.callback);
        };

        const container = listViewRef.current;
        if (container) {
            container.addEventListener('triggerCloseAnimation', handleCloseAnimation);
            return () => {
                container.removeEventListener('triggerCloseAnimation', handleCloseAnimation);
            };
        }
    }, [deviceItems]); // 依赖deviceItems确保回调能访问到最新的列表

    // 分类切换的实际逻辑
    const executeCategoryChangeLogic = (category) => {
        if (category && isValidCategory) {
            setIsLoading(true);
            setDeviceItems([]);
            setClosingItems(new Map()); // 重置关闭动画状态

            if (onDeviceCountChange) {
                onDeviceCountChange(0);
            }
            
            const loadingDelay = 300 + Math.random() * 200;
            
            setTimeout(() => {
                const items = [];
                const isFlowMeter = category === '流量计';
                const isPressureGauge = category === '压力计';
                const isAll = category === '全部';
                
                if (isAll) {
                    for (let i = 0; i < 12; i++) {
                        items.push({
                            id: `flowMeter-${i}`,
                            value: Math.floor(Math.random() * 1000) + parseFloat((Math.random()).toFixed(2)),
                            progress: 30 + Math.random() * 40,
                            alarmLowerLimit: Math.max(10, Math.floor(Math.random() * 30)),
                            alarmUpperLimit: Math.min(90, 70 + Math.floor(Math.random() * 20)),
                            type: 'flowMeter',
                            name: `流量计 ${i+1}`
                        });
                        items.push({
                            id: `pressureGauge-${i}`,
                            value: Math.floor(Math.random() * 1000) + parseFloat((Math.random()).toFixed(2)),
                            progress: 30 + Math.random() * 40,
                            alarmLowerLimit: Math.max(10, Math.floor(Math.random() * 30)),
                            alarmUpperLimit: Math.min(90, 70 + Math.floor(Math.random() * 20)),
                            type: 'pressureGauge',
                            name: `压力计 ${i+1}`
                        });
                    }
                } else if (isFlowMeter) {
                    for (let i = 0; i < 12; i++) {
                        items.push({
                            id: `flowMeter-${i}`,
                            value: Math.floor(Math.random() * 1000) + parseFloat((Math.random()).toFixed(2)),
                            progress: 30 + Math.random() * 40,
                            alarmLowerLimit: Math.max(10, Math.floor(Math.random() * 30)),
                            alarmUpperLimit: Math.min(90, 70 + Math.floor(Math.random() * 20)),
                            type: 'flowMeter',
                            name: `流量计 ${i+1}`
                        });
                    }
                } else if (isPressureGauge) {
                    for (let i = 0; i < 12; i++) {
                        items.push({
                            id: `pressureGauge-${i}`,
                            value: Math.floor(Math.random() * 1000) + parseFloat((Math.random()).toFixed(2)),
                            progress: 30 + Math.random() * 40,
                            alarmLowerLimit: Math.max(10, Math.floor(Math.random() * 30)),
                            alarmUpperLimit: Math.min(90, 70 + Math.floor(Math.random() * 20)),
                            type: 'pressureGauge',
                            name: `压力计 ${i+1}`
                        });
                    }
                }
                
                setDeviceItems(items);
                setIsLoading(false);
                
                if (onDeviceCountChange) {
                    onDeviceCountChange(items.length);
                }
            }, loadingDelay);
        } else {
            setDeviceItems([]);
            if (onDeviceCountChange) {
                onDeviceCountChange(0);
            }
        }
    };

    // 清理定时器
    useEffect(() => {
        return () => {
            closingTimersRef.current.forEach(timerId => clearTimeout(timerId));
        };
    }, []);

    // 根据设备类型渲染对应的列表项组件
    const renderDeviceListItem = (device) => {
        const isClosing = closingItems.get(device.id) || false;
        
        // 渲染原始的列表项组件
        const renderListItem = (deviceData) => {
            switch (deviceData.type) {
                case 'flowMeter':
                    return <FlowMeterListItem data={deviceData} isClosing={isClosing} />;
                case 'pressureGauge':
                    return <PressureGaugeListItem data={deviceData} isClosing={isClosing} />;
                default:
                    return null;
            }
        };
        
        return (
            <DraggableDevice
                key={device.id}
                deviceData={device}
                renderDevice={renderListItem}
            />
        );
    };
    
    return (
        <div className="list-view-container w-full h-full font-['ChakraPetch-Light']" ref={listViewRef}>
            {/* 添加拖拽相关样式 */}
            <style dangerouslySetInnerHTML={{
                __html: `
                /* 拖拽相关样式 */
                .draggable-device {
                    position: relative;
                    width: 100%;
                }
                
                .draggable-device.dragging {
                    z-index: 1000;
                }
                
                .draggable-device:hover {
                    transform: scale(1.01);
                }
                
                .draggable-device:active {
                    transform: scale(0.99);
                }
                `
            }} />
            
            <div className="devices-list w-full h-full p-[20px] overflow-y-auto">
                {selectedCategory ? (
                    <div className="selected-category-list">
                        {isValidCategory ? (
                            isLoading ? (
                                <div className="text-[#A9A6A9] text-[14px] flex items-center justify-center h-32">
                                    {/* 加载提示文字已移除，但保留容器 */}
                                </div>
                            ) : deviceItems.length > 0 ? (
                                <div className="devices-container">
                                    {deviceItems.map(device => renderDeviceListItem(device))}
                                </div>
                            ) : (
                                <div className="text-[#A9A6A9] text-[14px] flex items-center justify-center h-32">
                                    {/* “无设备数据”提示文字已移除 */}
                                </div>
                            )
                        ) : (
                            <div className="text-[#A9A6A9] text-[14px] flex items-center justify-center h-32">
                                {/* “无设备数据”提示文字已移除 */}
                            </div>
                        )}
                    </div>
                ) : (
                    <div className="no-category-selected text-[#A9A6A9] text-[14px] flex items-center justify-center h-full">
                        请从左侧选择一个分类查看设备列表
                    </div>
                )}
            </div>
        </div>
    );
};

export default ListView; 