// 设备工厂 - 统一管理所有设备类型
import FlowMeterDevice, { FLOW_METER_COLOR_SCHEMES } from '../components/FlowMeterDevice';
import PressureGaugeDevice, { PRESSURE_GAUGE_COLOR_SCHEMES } from '../components/PressureGaugeDevice';
import ValveControllerDevice, { VALVE_CONTROLLER_COLOR_SCHEMES } from '../components/ValveControllerDevice';
import LevelSensorDevice, { LEVEL_SENSOR_COLOR_SCHEMES } from '../components/LevelSensorDevice';

/**
 * 设备类型配置
 */
export const DEVICE_TYPES = {
    FLOW_METER: 'flowMeter',
    PRESSURE_GAUGE: 'pressureGauge', 
    VALVE_CONTROLLER: 'valveController',
    LEVEL_SENSOR: 'levelSensor'
};

/**
 * 设备类型元数据
 */
export const DEVICE_TYPE_METADATA = {
    [DEVICE_TYPES.FLOW_METER]: {
        name: '流量计',
        component: FlowMeterDevice,
        colorSchemes: FLOW_METER_COLOR_SCHEMES,
        unit: 'm³/h',
        defaultConfig: {
            updateProbability: 0.10,
            checkInterval: 2000,
            minUpdateDelay: 8000,
            maxUpdateDelay: 25000,
            valueChangeRange: 41,
            progressMinChange: 15,
            progressChangeRange: 40,
            animationDuration: 500
        }
    },
    [DEVICE_TYPES.PRESSURE_GAUGE]: {
        name: '压力计',
        component: PressureGaugeDevice,
        colorSchemes: PRESSURE_GAUGE_COLOR_SCHEMES,
        unit: 'MPa',
        defaultConfig: {
            updateProbability: 0.10,
            checkInterval: 2000,
            minUpdateDelay: 8000,
            maxUpdateDelay: 25000,
            valueChangeRange: 41,
            progressMinChange: 15,
            progressChangeRange: 40,
            animationDuration: 500
        }
    },
    [DEVICE_TYPES.VALVE_CONTROLLER]: {
        name: '阀门控制器',
        component: ValveControllerDevice,
        colorSchemes: VALVE_CONTROLLER_COLOR_SCHEMES,
        unit: '%',
        defaultConfig: {
            updateProbability: 0.05,
            checkInterval: 3000,
            minUpdateDelay: 15000,
            maxUpdateDelay: 45000,
            valueChangeRange: 11,
            progressMinChange: 5,
            progressChangeRange: 20,
            animationDuration: 1000
        }
    },
    [DEVICE_TYPES.LEVEL_SENSOR]: {
        name: '液位计',
        component: LevelSensorDevice,
        colorSchemes: LEVEL_SENSOR_COLOR_SCHEMES,
        unit: 'm',
        defaultConfig: {
            updateProbability: 0.08,
            checkInterval: 2500,
            minUpdateDelay: 10000,
            maxUpdateDelay: 30000,
            valueChangeRange: 31,
            progressMinChange: 8,
            progressChangeRange: 25,
            animationDuration: 600
        }
    }
};

/**
 * 创建设备数据
 * @param {string} deviceType - 设备类型
 * @param {number} index - 设备索引
 * @param {Object} overrides - 覆盖配置
 * @returns {Object} 设备数据对象
 */
export const createDeviceData = (deviceType, index, overrides = {}) => {
    const metadata = DEVICE_TYPE_METADATA[deviceType];
    if (!metadata) {
        throw new Error(`未知的设备类型: ${deviceType}`);
    }

    return {
        id: `${deviceType}-${index}`,
        value: Math.floor(Math.random() * 1000) + parseFloat((Math.random()).toFixed(2)),
        progress: 30 + Math.random() * 40,
        nextUpdateTime: Math.floor(Math.random() * 8000),
        alarmLowerLimit: Math.max(10, Math.floor(Math.random() * 30)),
        alarmUpperLimit: Math.min(90, 70 + Math.floor(Math.random() * 20)),
        type: deviceType,
        name: `${metadata.name} ${index + 1}`,
        colorScheme: metadata.colorSchemes[0],
        unit: metadata.unit,
        ...overrides
    };
};

/**
 * 批量创建设备数据
 * @param {string} deviceType - 设备类型
 * @param {number} count - 创建数量
 * @param {Object} overrides - 覆盖配置
 * @returns {Array} 设备数据数组
 */
export const createDevicesData = (deviceType, count, overrides = {}) => {
    const devices = [];
    for (let i = 0; i < count; i++) {
        devices.push(createDeviceData(deviceType, i, overrides));
    }
    return devices;
};

/**
 * 根据设备类型获取对应的React组件
 * @param {string} deviceType - 设备类型
 * @returns {React.Component} 设备组件
 */
export const getDeviceComponent = (deviceType) => {
    const metadata = DEVICE_TYPE_METADATA[deviceType];
    return metadata ? metadata.component : null;
};

/**
 * 获取所有支持的设备类型
 * @returns {Array} 设备类型数组
 */
export const getAllDeviceTypes = () => {
    return Object.values(DEVICE_TYPES);
};

/**
 * 获取设备类型的显示名称
 * @param {string} deviceType - 设备类型
 * @returns {string} 显示名称
 */
export const getDeviceTypeName = (deviceType) => {
    const metadata = DEVICE_TYPE_METADATA[deviceType];
    return metadata ? metadata.name : deviceType;
};

/**
 * 根据分类名称获取对应的设备类型
 * @param {string} categoryName - 分类名称
 * @returns {Array} 设备类型数组
 */
export const getDeviceTypesByCategory = (categoryName) => {
    switch (categoryName) {
        case '全部':
            return [DEVICE_TYPES.FLOW_METER, DEVICE_TYPES.PRESSURE_GAUGE];
        case '流量计':
            return [DEVICE_TYPES.FLOW_METER];
        case '压力计':
            return [DEVICE_TYPES.PRESSURE_GAUGE];

        case '阀门':
            return [DEVICE_TYPES.VALVE_CONTROLLER];
        case '液位计':
            return [DEVICE_TYPES.LEVEL_SENSOR];
        default:
            return [];
    }
}; 