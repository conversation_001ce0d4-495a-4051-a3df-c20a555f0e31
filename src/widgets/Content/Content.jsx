import React from 'react';
import ResponsiveGridExample from './GridLayout/ResponsiveGridExample.jsx';

/**
 * @description Content 内容区域组件，用于管理和展示主要内容
 * @returns {JSX.Element} Content UI 界面
 * @constructor
 */
const Content = () => {
    return (
        <div className="flex-1 overflow-auto">
            {/* 响应式网格布局区域 */}
            <ResponsiveGridExample />
            
            {/* 这里可以添加其他内容组件 */}
            {/* 比如: */}
            {/* <DataVisualization /> */}
            {/* <Charts /> */}
            {/* <Tables /> */}
        </div>
    );
};

export default React.memo(Content);
