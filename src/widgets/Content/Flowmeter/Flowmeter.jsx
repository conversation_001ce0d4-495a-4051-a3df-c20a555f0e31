import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import AnimationUtils from '../../../utils/animations.js';
import { useOptimizedResize } from '../../../hooks/useOptimizedEventListener';
import NumberFlow from '@number-flow/react';
import * as echarts from 'echarts';
import { gsap } from 'gsap';
import * as THREE from 'three';
// 导入页面组件
import FlowPage from './components/FlowPage.jsx';
import DataPage from './components/DataPage.jsx';
import AlarmPage from './components/AlarmPage.jsx';
import DevicePage from './components/DevicePage.jsx';

// 🎯 性能优化：提取常量避免重复创建
const ANIMATION_CONFIG = {
    randomDelayMin: 1500,
    randomDelayMax: 2500,
    flashDuration: 0.8,
    closingDuration: 0.35,
    statusCheckInterval: 10000
};

const STATUS_COLORS = {
    '正常': '#23C76D',
    '异常': '#E39D25', 
    '离线': '#B83F31'
};

const STATUS_OPTIONS = ['正常', '异常', '离线'];

// 标题选项配置
const TITLE_OPTIONS = ['流量', '数据', '报警', '设备'];

// 流量计设备组件 - 使用React.memo优化重新渲染
const Flowmeter = React.memo(({ onClose }) => {
    // 设备状态管理
    const [deviceStatus, setDeviceStatus] = useState('正常');
    const [lastUpdate, setLastUpdate] = useState(new Date());
    
    // 标题切换状态
    const [currentTitleIndex, setCurrentTitleIndex] = useState(0);
    const [titleChanging, setTitleChanging] = useState(false);
    
    // 生成随机数据点的函数
    const randomDataPoint = useCallback((baseTime, baseValue) => {
        const newTime = new Date(baseTime);
        const newValue = baseValue + Math.random() * 21 - 10; // ±10的波动
        return [newTime, Math.max(80, Math.min(180, newValue))]; // 限制在80-180范围
    }, []);
    
    // 数据维度状态管理 - 用于"数据"视图
    const [dimensionData, setDimensionData] = useState({
        todayUsage: 156.8,      // 今日用量
        monthUsage: 4521.3,     // 本月用量
        avgFlow: 98.5,          // 平均流量
        peakFlow: 178.9         // 峰值流量
    });
    
    // 维度数据闪烁状态
    const [dimensionFlashing, setDimensionFlashing] = useState({
        todayUsage: false,
        monthUsage: false,
        avgFlow: false,
        peakFlow: false
    });
    
    // 报警数据状态管理
    const [alarmData, setAlarmData] = useState({
        yesterdayAlarms: 2,     // 昨日报警次数
        todayAlarms: 0          // 今日报警次数
    });
    
    // 报警列表自动滚动相关refs和状态
    const alarmListRef = useRef(null);
    const alarmScrollContainerRef = useRef(null);
    const scrollAnimationRef = useRef(null);
    const [alarmScrollPaused, setAlarmScrollPaused] = useState(false);
    const [manualScrollPosition, setManualScrollPosition] = useState(0);
    
    // 时间序列数据状态 - 初始化50个数据点确保图表稳定显示
    const [timeSeriesData, setTimeSeriesData] = useState(() => {
        const data = [];
        let now = new Date();
        let value = 125; // 基础值
        
        // 生成过去50个数据点
        for (let i = 49; i >= 0; i--) {
            const time = new Date(now.getTime() - i * 1000);
            value = value + Math.random() * 21 - 10;
            value = Math.max(80, Math.min(180, value)); // 限制范围
            data.push([time, value]);
        }
        return data;
    });
    
    // NumberFlow显示状态管理 - 先显示0.00，2秒后显示真实数据
    const [showRealFlow, setShowRealFlow] = useState(false);
    
    // 🎯 性能优化：从图表数据中派生出当前流量值，确保与图表同步
    const currentFlowRate = useMemo(() => {
        if (!showRealFlow) {
            return 0.00; // 前2秒显示0.00
        }
        if (timeSeriesData.length === 0) {
            return 125.60; // 在数据为空时提供一个默认值
        }
        // 返回最后一个数据点的值
        return timeSeriesData[timeSeriesData.length - 1][1];
    }, [timeSeriesData, showRealFlow]);
    
    // 简化动画状态管理 - 使用单个对象减少状态数量
    const [animationState, setAnimationState] = useState({
        showTopLines: false,
        showTitle: false,
        showBottomLine: false,
        showTitleBar: false,
        showStatusButton: false,
        showContentArea: false, // 新增：主要内容区域动画状态
        showBottomInfoBar: false, // 新增：底部信息栏动画状态
        isClosing: false
    });
    
    // 创建refs用于GSAP动画和ECharts
    const containerRef = useRef(null);
    const leftLineRef = useRef(null);
    const rightLineRef = useRef(null);
    const titleRef = useRef(null);
    const titleBarRef = useRef(null);
    const statusButtonRef = useRef(null);
    const bottomLineRef = useRef(null);
    const contentAreaRef = useRef(null); // 新增：主要内容区域ref
    const bottomInfoBarRef = useRef(null); // 新增：底部信息栏ref
    // 注意：chartRef和chartInstanceRef现在由FlowPage组件管理
    const cumulativeFlowRef = useRef(null); // 底部累计流量显示ref
    
    // Three.js 3D模型相关refs
    const threeDContainerRef = useRef(null); // 3D模型容器
    const threeSceneRef = useRef(null); // Three.js场景
    const threeRendererRef = useRef(null); // Three.js渲染器
    const threeCameraRef = useRef(null); // Three.js相机
    const flowmeterModelRef = useRef(null); // 流量计3D模型引用
    const threeAnimationIdRef = useRef(null); // 动画帧ID
    
    // 鼠标交互相关状态（升级版本 - 支持更流畅的3D拖拽）
    const [mouseInteraction, setMouseInteraction] = useState({
        isMouseDown: false,
        isDragging: false, // 是否正在拖拽
        previousMousePosition: { x: 0, y: 0 },
        startMousePosition: { x: 0, y: 0 } // 开始拖拽的位置
    });
    
    // 数据维度容器refs - 用于闪烁动画
    const dimensionRefs = useRef({
        todayUsage: null,
        monthUsage: null,
        avgFlow: null,
        peakFlow: null
    });
    
    // 🎯 性能优化：存储动画时间线引用，便于清理
    const animationTimelines = useRef([]);
    
    // 🧹 资源清理：存储所有需要清理的资源引用
    const resourceRefs = useRef({
        intervals: [], // 存储setInterval的ID
        timeouts: [], // 存储setTimeout的ID
        resizeObserver: null, // ResizeObserver实例
        mutationObserver: null, // MutationObserver实例
        resizeHandler: null, // resize事件处理函数
        gsapCallbacks: [], // GSAP延迟调用
    });

    // 生成随机延迟时间 - 使用常量配置
    const getRandomDelay = useCallback(() => 
        Math.random() * (ANIMATION_CONFIG.randomDelayMax - ANIMATION_CONFIG.randomDelayMin) + ANIMATION_CONFIG.randomDelayMin
    , []);

    // 获取状态颜色 - 使用useMemo优化
    const statusColor = useMemo(() => STATUS_COLORS[deviceStatus] || '#909999', [deviceStatus]);

    // 🎯 计算数据平均值的函数 - 传统平均值线
    const calculateAverageValue = useCallback((data) => {
        if (!data || data.length === 0) return 130; // 默认值
        
        const values = data.map(item => item[1]);
        const sum = values.reduce((a, b) => a + b, 0);
        const average = sum / values.length;
        
        return average;
    }, []);

    // 触发维度数据闪烁效果 - 明显的透明度闪烁
    const triggerDimensionFlash = useCallback((dimensionKey) => {
        setDimensionFlashing(prev => ({ ...prev, [dimensionKey]: true }));
        
        // 获取对应的DOM元素
        const element = dimensionRefs.current[dimensionKey];
        if (element) {

            
            // 创建基于透明度的急促频闪效果 - 更明显
            const tl = gsap.timeline();
            
            // 急促的三次快闪（像汽车双闪灯）
            for (let i = 0; i < 3; i++) {
                tl
                    // 瞬间几乎消失
                    .to(element, {
                        opacity: 0.1,
                        duration: 0.05,
                        ease: 'none'
                    }, i * 0.2)
                    // 瞬间完全显现
                    .to(element, {
                        opacity: 1,
                        duration: 0.05,
                        ease: 'none'
                    }, i * 0.2 + 0.08)
                    // 稍微停顿保持显示
                    .to(element, {
                        opacity: 1,
                        duration: 0.07,
                        ease: 'none'
                    }, i * 0.2 + 0.13);
            }
            
            // 最后确保完全显示
            tl.to(element, {
                opacity: 1,
                duration: 0.1,
                ease: 'power2.out'
            });
            
            // 存储动画引用
            animationTimelines.current.push(tl);
        }
        
        // 0.8秒后重置闪烁状态
        const resetTimer = setTimeout(() => {
            setDimensionFlashing(prev => ({ ...prev, [dimensionKey]: false }));
        }, 800);
        
        resourceRefs.current.timeouts.push(resetTimer);
    }, []);

    // 🎯 性能优化：使用useCallback优化动画函数
    const updateAnimationState = useCallback((updates) => {
        setAnimationState(prev => ({ ...prev, ...updates }));
    }, []);

    // 注意：3D流量计模型现在由DevicePage组件负责创建

    // 清理3D资源
    const cleanup3DResources = useCallback(() => {
        // 停止动画循环
        if (threeAnimationIdRef.current) {
            cancelAnimationFrame(threeAnimationIdRef.current);
            threeAnimationIdRef.current = null;
        }

        // 清理渲染器
        if (threeRendererRef.current) {
            // 清理WebGL上下文
            threeRendererRef.current.dispose();
            threeRendererRef.current.forceContextLoss();
            
            // 移除DOM元素
            if (threeDContainerRef.current && threeRendererRef.current.domElement) {
                try {
                    threeDContainerRef.current.removeChild(threeRendererRef.current.domElement);
                } catch (e) {
                    // console.warn('移除3D渲染器DOM元素时出错:', e);
                }
            }
            threeRendererRef.current = null;
        }

        // 清理场景中的所有对象
        if (threeSceneRef.current) {
            // 递归清理场景中的所有对象
            const dispose = (obj) => {
                if (obj.geometry) {
                    obj.geometry.dispose();
                }
                if (obj.material) {
                    if (Array.isArray(obj.material)) {
                        obj.material.forEach(material => material.dispose());
                    } else {
                        obj.material.dispose();
                    }
                }
                if (obj.children) {
                    obj.children.forEach(child => dispose(child));
                }
            };

            threeSceneRef.current.traverse(dispose);
            threeSceneRef.current.clear();
            threeSceneRef.current = null;
        }

        // 清理其他引用
        threeCameraRef.current = null;
        flowmeterModelRef.current = null;

        // console.log('🧹 3D资源清理完成');
    }, []);

    // 鼠标事件处理函数
    const handleMouseDown = useCallback((event) => {
        if (!threeDContainerRef.current) return;
        
        // 立即停止自动旋转
        rotationStateRef.current.autoRotate = false;
        
        // 获取鼠标在容器中的相对位置
        const rect = threeDContainerRef.current.getBoundingClientRect();
        const mouseX = event.clientX - rect.left;
        const mouseY = event.clientY - rect.top;
        
        setMouseInteraction(prev => ({
            ...prev,
            isMouseDown: true,
            isDragging: false, // 刚开始按下时还没有拖拽
            mouseButton: event.button, // 记录按下的鼠标按键：0=左键，2=右键
            previousMousePosition: {
                x: mouseX,
                y: mouseY
            },
            startMousePosition: {
                x: mouseX,
                y: mouseY
            }
        }));
        
        // 阻止拖拽选择文本和默认行为
        event.preventDefault();
        event.stopPropagation();
    }, []);

    const handleMouseMove = useCallback((event) => {
        if (!mouseInteraction.isMouseDown) return;
        
        // 获取鼠标在容器中的相对位置
        const rect = threeDContainerRef.current?.getBoundingClientRect();
        if (!rect) return;
        
        const mouseX = event.clientX - rect.left;
        const mouseY = event.clientY - rect.top;
        
        // 计算鼠标移动距离
        const deltaX = mouseX - mouseInteraction.previousMousePosition.x;
        const deltaY = mouseY - mouseInteraction.previousMousePosition.y;
        
        // 如果移动距离超过阈值，标记为拖拽状态
        if (!mouseInteraction.isDragging) {
            const totalDistance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
            if (totalDistance > 3) { // 3像素的拖拽阈值
                setMouseInteraction(prev => ({
                    ...prev,
                    isDragging: true
                }));
            }
        }
        
        // 根据鼠标按键类型执行不同操作
        if (mouseInteraction.mouseButton === 0) {
            // 🖱️ 左键：模型旋转
            const rotationSensitivity = 0.008; // 提高灵敏度
            
            // 水平拖拽控制Y轴旋转（左右旋转）
            const deltaRotationY = deltaX * rotationSensitivity;
            
            // 垂直拖拽控制X轴旋转（上下旋转）- 限制角度避免倾斜
            const deltaRotationX = deltaY * rotationSensitivity;
            
            // 应用旋转变化
            rotationStateRef.current.rotationY += deltaRotationY;
            rotationStateRef.current.rotationX += deltaRotationX;
            
            // 限制X轴旋转角度，允许更大的旋转范围，可以看到底部
            const maxRotationX = Math.PI / 1.8; // 扩大到100度范围，允许看到底部
            rotationStateRef.current.rotationX = Math.max(-maxRotationX, Math.min(maxRotationX, rotationStateRef.current.rotationX));
            
            // Y轴旋转保持连续，允许无限旋转
            rotationStateRef.current.rotationY = rotationStateRef.current.rotationY % (Math.PI * 2);
        } else if (mouseInteraction.mouseButton === 2) {
            // 🖱️ 右键：模型平移
            const panSensitivity = 0.003; // 平移灵敏度
            
            // 计算平移变化量
            const deltaPanX = deltaX * panSensitivity;
            const deltaPanY = -deltaY * panSensitivity; // Y轴反向，符合直觉
            
            // 应用平移变化
            rotationStateRef.current.panX += deltaPanX;
            rotationStateRef.current.panY += deltaPanY;
            
            // 限制平移范围，防止模型移出视野
            const maxPan = 2.0;
            rotationStateRef.current.panX = Math.max(-maxPan, Math.min(maxPan, rotationStateRef.current.panX));
            rotationStateRef.current.panY = Math.max(-maxPan, Math.min(maxPan, rotationStateRef.current.panY));
        }
        
        setMouseInteraction(prev => ({
            ...prev,
            previousMousePosition: {
                x: mouseX,
                y: mouseY
            }
        }));
        
        // 阻止默认行为
        event.preventDefault();
        event.stopPropagation();
    }, [mouseInteraction.isMouseDown, mouseInteraction.previousMousePosition, mouseInteraction.isDragging]);

    const handleMouseUp = useCallback((event) => {
        if (!mouseInteraction.isMouseDown) return;
        
        setMouseInteraction(prev => ({
            ...prev,
            isMouseDown: false,
            isDragging: false
        }));
        
        // 只有当用户启用了自动旋转时，才在鼠标释放后恢复旋转
        // 这样可以实现：激活时拖动暂停、松开恢复；未激活时始终不旋转
        const resumeAutoRotateTimer = setTimeout(() => {
            // 检查用户是否启用了自动旋转
            if (rotationStateRef.current.userAutoRotateEnabled) {
                rotationStateRef.current.autoRotate = true;
            }
            // 如果用户未启用自动旋转，则保持关闭状态
        }, 100); // 缩短延迟时间
        
        resourceRefs.current.timeouts.push(resumeAutoRotateTimer);
        
        event.preventDefault();
        event.stopPropagation();
    }, [mouseInteraction.isMouseDown]);

    // 鼠标滚轮缩放事件处理
    const handleMouseWheel = useCallback((event) => {
        if (!threeDContainerRef.current) return;
        
        // 阻止页面滚动
        event.preventDefault();
        event.stopPropagation();
        
        // 计算缩放变化量
        const zoomSensitivity = 0.1;
        const deltaZoom = event.deltaY > 0 ? -zoomSensitivity : zoomSensitivity;
        
        // 应用缩放变化，限制在最小和最大缩放范围内
        const newZoom = Math.max(
            rotationStateRef.current.minZoom,
            Math.min(
                rotationStateRef.current.maxZoom,
                rotationStateRef.current.zoom + deltaZoom
            )
        );
        
        // 使用GSAP实现平滑缩放动画
        if (Math.abs(newZoom - rotationStateRef.current.zoom) > 0.01) {
            gsap.to(rotationStateRef.current, {
                zoom: newZoom,
                duration: 0.2,
                ease: "power2.out"
            });
        }
    }, []);

    // 鼠标离开容器时停止拖拽并恢复自动旋转
    const handleMouseLeave = useCallback(() => {
        if (mouseInteraction.isMouseDown) {
            setMouseInteraction(prev => ({
                ...prev,
                isMouseDown: false,
                isDragging: false
            }));
            
            // 鼠标离开时立即恢复自动旋转
            rotationStateRef.current.autoRotate = true;
        }
    }, [mouseInteraction.isMouseDown]);

    // 使用ref来存储旋转状态，避免频繁重新创建动画函数
    const rotationStateRef = useRef({
        rotationY: 0,    // Y轴旋转（左右）
        rotationX: 0,    // X轴旋转（上下）
        rotationZ: 0,    // Z轴旋转（倾斜）- 始终保持为0
        autoRotate: true,
        userAutoRotateEnabled: true, // 记录用户的自动旋转选择
        zoom: 1.0,       // 缩放比例
        minZoom: 0.5,    // 最小缩放
        maxZoom: 3.0,    // 最大缩放
        panX: 0,         // 模型X轴平移
        panY: 0          // 模型Y轴平移
    });

    // 注意：3D模型动画循环现在由DevicePage组件负责

    // 🧹 资源清理函数 - 统一清理所有资源，防止内存泄漏
    const cleanupAllResources = useCallback(() => {
        // 1. 清理所有定时器
        resourceRefs.current.intervals.forEach(id => {
            if (id) {
                clearInterval(id);
            }
        });
        resourceRefs.current.intervals = [];
        
        resourceRefs.current.timeouts.forEach(id => {
            if (id) {
                clearTimeout(id);
            }
        });
        resourceRefs.current.timeouts = [];
        
        // 2. 清理事件监听器
        if (resourceRefs.current.resizeHandler) {
            window.removeEventListener('resize', resourceRefs.current.resizeHandler);
            resourceRefs.current.resizeHandler = null;
        }
        
        // 3. 清理观察者
        if (resourceRefs.current.resizeObserver) {
            resourceRefs.current.resizeObserver.disconnect();
            resourceRefs.current.resizeObserver = null;
        }
        
        if (resourceRefs.current.mutationObserver) {
            resourceRefs.current.mutationObserver.disconnect();
            resourceRefs.current.mutationObserver = null;
        }
        
        // 4. 清理GSAP动画和延迟调用
        animationTimelines.current.forEach(tl => {
            if (tl && tl.kill) {
                tl.kill();
            }
        });
        animationTimelines.current = [];
        
        resourceRefs.current.gsapCallbacks.forEach(callback => {
            if (callback && callback.kill) {
                callback.kill();
            }
        });
        resourceRefs.current.gsapCallbacks = [];
        
        // 5. 注意：ECharts实例现在由FlowPage组件负责清理

        // 6. 清理3D资源
        cleanup3DResources();
    }, [cleanup3DResources]);

    // 开场动画函数 - 优化版本
    const playOpeningAnimation = useCallback(() => {
        // 清理之前的动画
        animationTimelines.current.forEach(tl => tl && tl.kill());
        animationTimelines.current = [];

        // 顶部线条扩散动画
        const topLinesTimer = setTimeout(() => {
            updateAnimationState({ showTopLines: true });
            
            // 延迟启动频闪效果，与CSS扩散动画同步
            setTimeout(() => {
                const leftFlash = AnimationUtils.createFlashEffect(leftLineRef, {
                    duration: ANIMATION_CONFIG.flashDuration,
                    delay: 0,
                    mode: 'opacity',
                    flashCount: 15,
                    flashDuration: 0.025,
                    minOpacity: 0.4,
                    maxOpacity: 1.0
                });
                
                const rightFlash = AnimationUtils.createFlashEffect(rightLineRef, {
                    duration: ANIMATION_CONFIG.flashDuration,
                    delay: 0,
                    mode: 'opacity',
                    flashCount: 15,
                    flashDuration: 0.025,
                    minOpacity: 0.4,
                    maxOpacity: 1.0
                });
                
                // 存储动画引用便于清理
                if (leftFlash) animationTimelines.current.push(leftFlash);
                if (rightFlash) animationTimelines.current.push(rightFlash);
            }, 100);
        }, getRandomDelay());

        // 标题动画
        const titleTimer = setTimeout(() => {
            updateAnimationState({ showTitle: true });
            
            const titleFlash = AnimationUtils.createFlashEffect(titleRef, {
                duration: ANIMATION_CONFIG.flashDuration,
                delay: 0,
                mode: 'brightness',
                flashCount: 15,
                flashDuration: 0.025,
                minBrightness: 0.6,
                maxBrightness: 2.0
            });
            
            if (titleFlash) animationTimelines.current.push(titleFlash);
        }, getRandomDelay());

        // 标题栏动画
        const titleBarTimer = setTimeout(() => {
            updateAnimationState({ showTitleBar: true });
            
            const titleBarFlash = AnimationUtils.createFlashEffect(titleBarRef, {
                duration: ANIMATION_CONFIG.flashDuration,
                delay: 0,
                mode: 'brightness',
                flashCount: 12,
                flashDuration: 0.03,
                minBrightness: 0.5,
                maxBrightness: 1.8
            });
            
            if (titleBarFlash) animationTimelines.current.push(titleBarFlash);
        }, getRandomDelay());

        // 底部信息栏动画 - 比标题栏延迟多0.5秒
        const bottomInfoBarTimer = setTimeout(() => {
            updateAnimationState({ showBottomInfoBar: true });
            
            const bottomInfoBarFlash = AnimationUtils.createFlashEffect(bottomInfoBarRef, {
                duration: ANIMATION_CONFIG.flashDuration,
                delay: 0,
                mode: 'brightness',
                flashCount: 12,
                flashDuration: 0.03,
                minBrightness: 0.5,
                maxBrightness: 1.8
            });
            
            if (bottomInfoBarFlash) animationTimelines.current.push(bottomInfoBarFlash);
        }, getRandomDelay() + 500); // 比标题栏多延迟0.5秒

        // 状态按钮动画
        const statusButtonTimer = setTimeout(() => {
            updateAnimationState({ showStatusButton: true });
            
            const statusFlash = AnimationUtils.createFlashEffect(statusButtonRef, {
                duration: 1.0,
                delay: 0,
                mode: 'brightness',
                flashCount: 18,
                flashDuration: 0.025,
                minBrightness: 0.4,
                maxBrightness: 2.2
            });
            
            if (statusFlash) animationTimelines.current.push(statusFlash);
        }, getRandomDelay());

        // 底部线条动画
        const bottomLineTimer = setTimeout(() => {
            updateAnimationState({ showBottomLine: true });
            
            setTimeout(() => {
                const bottomFlash = AnimationUtils.createFlashEffect(bottomLineRef, {
                    duration: ANIMATION_CONFIG.flashDuration,
                    delay: 0,
                    mode: 'opacity',
                    flashCount: 15,
                    flashDuration: 0.025,
                    minOpacity: 0.3,
                    maxOpacity: 1.0
                });
                
                if (bottomFlash) animationTimelines.current.push(bottomFlash);
            }, 100);
        }, getRandomDelay());

        // 主要内容区域动画 - 延迟2.5秒后播放，只保留闪烁效果
        const contentAreaTimer = setTimeout(() => {
            updateAnimationState({ showContentArea: true });
            
            // 立即启动内容区域闪烁效果
            const contentFlash = AnimationUtils.createFlashEffect(contentAreaRef, {
                duration: 1.2,
                delay: 0,
                mode: 'brightness',
                flashCount: 20,
                flashDuration: 0.03,
                minBrightness: 0.3,
                maxBrightness: 1.8
            });
            
            if (contentFlash) animationTimelines.current.push(contentFlash);
        }, 2500); // 延迟2.5秒

        // NumberFlow数据显示 - 添加随机延迟，显得更自然
        const flowDataDelay = 2500 + Math.random() * 1000; // 2.5-3.5秒随机延迟
        const flowDataTimer = setTimeout(() => {
            setShowRealFlow(true);
        }, flowDataDelay);

        return () => {
            clearTimeout(topLinesTimer);
            clearTimeout(titleTimer);
            clearTimeout(titleBarTimer);
            clearTimeout(bottomInfoBarTimer);
            clearTimeout(statusButtonTimer);
            clearTimeout(bottomLineTimer);
            clearTimeout(contentAreaTimer);
            clearTimeout(flowDataTimer);
        };
    }, [getRandomDelay, updateAnimationState]);

    // 关闭动画函数 - 优化版本
    const playClosingAnimation = useCallback(() => {
        return new Promise((resolve) => {
            updateAnimationState({ isClosing: true });
            
            // 清理之前的动画
            animationTimelines.current.forEach(tl => tl && tl.kill());
            animationTimelines.current = [];
            
            // 为整个容器添加整体闪烁效果
            if (containerRef.current) {
                const containerFlash = AnimationUtils.createFlashEffect(containerRef, {
                    duration: ANIMATION_CONFIG.closingDuration,
                    delay: 0,
                    mode: 'brightness',
                    flashCount: 10,
                    flashDuration: 0.02,
                    minBrightness: 0.3,
                    maxBrightness: 1.8
                });
                
                if (containerFlash) animationTimelines.current.push(containerFlash);
            }
            
            // 同时播放所有关闭动画
            const animationPromises = [];
            
            // 状态按钮关闭动画
            if (statusButtonRef.current) {
                const statusCloseFlash = AnimationUtils.createFlashEffect(statusButtonRef, {
                    duration: 0.25,
                    delay: 0,
                    mode: 'brightness',
                    flashCount: 8,
                    flashDuration: 0.015,
                    minBrightness: 0.2,
                    maxBrightness: 2.0
                });
                if (statusCloseFlash) {
                    animationTimelines.current.push(statusCloseFlash);
                    animationPromises.push(statusCloseFlash);
                }
            }
            
            // 标题关闭动画
            if (titleRef.current) {
                const titleCloseFlash = AnimationUtils.createFlashEffect(titleRef, {
                    duration: 0.25,
                    delay: 0.05,
                    mode: 'brightness',
                    flashCount: 8,
                    flashDuration: 0.015,
                    minBrightness: 0.2,
                    maxBrightness: 2.0
                });
                if (titleCloseFlash) {
                    animationTimelines.current.push(titleCloseFlash);
                    animationPromises.push(titleCloseFlash);
                }
            }
            
            // 标题栏关闭动画
            if (titleBarRef.current) {
                const titleBarCloseFlash = AnimationUtils.createFlashEffect(titleBarRef, {
                    duration: 0.25,
                    delay: 0.1,
                    mode: 'brightness',
                    flashCount: 7,
                    flashDuration: 0.02,
                    minBrightness: 0.1,
                    maxBrightness: 1.8
                });
                if (titleBarCloseFlash) {
                    animationTimelines.current.push(titleBarCloseFlash);
                    animationPromises.push(titleBarCloseFlash);
                }
            }
            
            // 顶部线条关闭动画
            if (leftLineRef.current && rightLineRef.current) {
                const leftCloseFlash = AnimationUtils.createFlashEffect(leftLineRef, {
                    duration: 0.25,
                    delay: 0.15,
                    mode: 'opacity',
                    flashCount: 8,
                    flashDuration: 0.015,
                    minOpacity: 0.1,
                    maxOpacity: 1.0
                });
                
                const rightCloseFlash = AnimationUtils.createFlashEffect(rightLineRef, {
                    duration: 0.25,
                    delay: 0.15,
                    mode: 'opacity',
                    flashCount: 8,
                    flashDuration: 0.015,
                    minOpacity: 0.1,
                    maxOpacity: 1.0
                });
                
                if (leftCloseFlash) {
                    animationTimelines.current.push(leftCloseFlash);
                    animationPromises.push(leftCloseFlash);
                }
                if (rightCloseFlash) {
                    animationTimelines.current.push(rightCloseFlash);
                    animationPromises.push(rightCloseFlash);
                }
            }
            
            // 主要内容区域关闭动画
            if (contentAreaRef.current) {
                const contentCloseFlash = AnimationUtils.createFlashEffect(contentAreaRef, {
                    duration: 0.3,
                    delay: 0.1,
                    mode: 'brightness',
                    flashCount: 10,
                    flashDuration: 0.015,
                    minBrightness: 0.1,
                    maxBrightness: 1.5
                });
                if (contentCloseFlash) {
                    animationTimelines.current.push(contentCloseFlash);
                    animationPromises.push(contentCloseFlash);
                }
            }

            // 底部状态栏关闭动画 - 快速闪烁消失
            if (bottomInfoBarRef.current) {
                const bottomInfoCloseFlash = AnimationUtils.createFlashEffect(bottomInfoBarRef, {
                    duration: 0.3,
                    delay: 0.15,
                    mode: 'brightness',
                    flashCount: 12,
                    flashDuration: 0.012,
                    minBrightness: 0.1,
                    maxBrightness: 2.2
                });
                if (bottomInfoCloseFlash) {
                    animationTimelines.current.push(bottomInfoCloseFlash);
                    animationPromises.push(bottomInfoCloseFlash);
                }
            }

            // 底部线条关闭动画
            if (bottomLineRef.current) {
                const bottomCloseFlash = AnimationUtils.createFlashEffect(bottomLineRef, {
                    duration: 0.25,
                    delay: 0.2,
                    mode: 'opacity',
                    flashCount: 8,
                    flashDuration: 0.015,
                    minOpacity: 0.05,
                    maxOpacity: 1.0
                });
                if (bottomCloseFlash) {
                    animationTimelines.current.push(bottomCloseFlash);
                    animationPromises.push(bottomCloseFlash);
                }
            }

            // 同时进行收缩动画
            setTimeout(() => updateAnimationState({ showStatusButton: false }), 50);
            setTimeout(() => updateAnimationState({ showTitle: false }), 100);
            setTimeout(() => updateAnimationState({ showTitleBar: false }), 150);
            setTimeout(() => updateAnimationState({ showContentArea: false }), 180); // 新增：内容区域收缩
            setTimeout(() => updateAnimationState({ showTopLines: false }), 200);
            setTimeout(() => updateAnimationState({ showBottomInfoBar: false }), 230); // 新增：底部状态栏收缩
            setTimeout(() => {
                updateAnimationState({ showBottomLine: false });
                // 重置NumberFlow显示状态
                setShowRealFlow(false);
                setTimeout(resolve, 100);
            }, 250);
        });
    }, [updateAnimationState]);

    // 关闭窗口 - 优化版本，增加资源清理
    const handleClose = useCallback(async () => {
        // 1. 先清理所有资源，防止内存泄漏
        cleanupAllResources();
        
        // 2. 播放关闭动画
        await playClosingAnimation();
        
        // 3. 调用父组件的关闭回调
        if (onClose) {
            onClose();
        }
    }, [playClosingAnimation, onClose, cleanupAllResources]);

    // 模拟状态变化和流量数据更新 - 参考用户提供的ECharts代码
    useEffect(() => {
        const statusInterval = setInterval(() => {
            if (Math.random() > 0.1) {
                setDeviceStatus('正常');
            } else {
                const randomStatus = STATUS_OPTIONS[Math.floor(Math.random() * STATUS_OPTIONS.length)];
                setDeviceStatus(randomStatus);
            }
        }, 8000 + Math.random() * 7000); // 随机8-15秒状态检查间隔

        // 实时流量数据更新 - 只更新图表数据
        const flowInterval = setInterval(() => {
            const now = new Date();
            
            setTimeSeriesData(prevData => {
                const newData = [...prevData];
                
                // 移除最老的数据点，添加新的数据点
                if (newData.length >= 50) {
                    newData.shift(); // 移除最老的数据
                }
                
                // 获取基准值并生成新数据
                const baseValue = newData.length > 0 ? newData[newData.length - 1][1] : 125;
                const newValue = baseValue + Math.random() * 31 - 15;
                const clampedValue = Math.max(80, Math.min(180, newValue));
                
                newData.push([now, clampedValue]);
                
                return newData;
            });
            
            // 更新最后更新时间
            setLastUpdate(new Date());
        }, 2500 + Math.random() * 3000); // 随机2.5-5.5秒更新频率

        // 维度数据随机更新 - 每个维度独立更新，间隔不同
        const dimensionUpdateInterval = setInterval(() => {
            const dimensionKeys = ['todayUsage', 'monthUsage', 'avgFlow', 'peakFlow'];
            
            // 随机选择1-2个维度进行更新
            const updateCount = Math.random() > 0.6 ? 2 : 1;
            const shuffledKeys = dimensionKeys.sort(() => Math.random() - 0.5);
            const keysToUpdate = shuffledKeys.slice(0, updateCount);
            
            keysToUpdate.forEach(key => {
                setDimensionData(prevData => {
                    const newData = { ...prevData };
                    
                    switch (key) {
                        case 'todayUsage':
                            // 今日用量：在基础值±20%范围内波动
                            newData.todayUsage = Math.max(100, 
                                prevData.todayUsage + (Math.random() - 0.5) * 50
                            );
                            break;
                        case 'monthUsage':
                            // 本月用量：缓慢增长，偶尔大幅增长
                            const monthIncrease = Math.random() > 0.8 ? 
                                Math.random() * 50 + 10 : Math.random() * 10 + 2;
                            newData.monthUsage = prevData.monthUsage + monthIncrease;
                            break;
                        case 'avgFlow':
                            // 平均流量：在90-120范围内波动
                            newData.avgFlow = Math.max(85, Math.min(125, 
                                prevData.avgFlow + (Math.random() - 0.5) * 15
                            ));
                            break;
                        case 'peakFlow':
                            // 峰值流量：在150-200范围内波动
                            newData.peakFlow = Math.max(150, Math.min(200, 
                                prevData.peakFlow + (Math.random() - 0.5) * 20
                            ));
                            break;
                    }
                    
                    return newData;
                });
                
                // 触发闪烁效果
                setTimeout(() => {
                    triggerDimensionFlash(key);
                }, Math.random() * 200); // 随机延迟0-200ms
            });
        }, 4000 + Math.random() * 3000); // 4-7秒随机间隔

        // 🧹 存储interval引用以便清理
        resourceRefs.current.intervals.push(statusInterval, flowInterval, dimensionUpdateInterval);

        return () => {
            clearInterval(statusInterval);
            clearInterval(flowInterval);
            clearInterval(dimensionUpdateInterval);
        };
    }, [triggerDimensionFlash]);

    // 开场动画触发
    useEffect(() => {
        const cleanup = playOpeningAnimation();
        return cleanup;
    }, [playOpeningAnimation]);

    // 🎯 性能优化：组件卸载时清理所有动画
    useEffect(() => {
        return () => {
            animationTimelines.current.forEach(tl => tl && tl.kill());
            animationTimelines.current = [];
        };
    }, []);

    // 标题切换函数 - 添加内容区域闪烁效果
    const handleTitlePrev = useCallback(() => {
        if (titleChanging) return; // 防止重复点击
        
        setTitleChanging(true);
        
        // 1. 先让旧内容闪烁消失
        if (contentAreaRef.current) {
            const oldContentFlash = AnimationUtils.createFlashEffect(contentAreaRef, {
                duration: 0.2,
                delay: 0,
                mode: 'brightness',
                flashCount: 6,
                flashDuration: 0.015,
                minBrightness: 0.2,
                maxBrightness: 1.0
            });
            if (oldContentFlash) animationTimelines.current.push(oldContentFlash);
        }
        
        const timer1 = setTimeout(() => {
            setCurrentTitleIndex(prev => 
                prev === 0 ? TITLE_OPTIONS.length - 1 : prev - 1
            );
            
            // 2. 切换内容后，让新内容闪烁进入
            const timer2 = setTimeout(() => {
                setTitleChanging(false);
                
                if (contentAreaRef.current) {
                    const newContentFlash = AnimationUtils.createFlashEffect(contentAreaRef, {
                        duration: 0.3,
                        delay: 0,
                        mode: 'brightness',
                        flashCount: 8,
                        flashDuration: 0.02,
                        minBrightness: 0.4,
                        maxBrightness: 1.8
                    });
                    if (newContentFlash) animationTimelines.current.push(newContentFlash);
                }
            }, 50);
            
            // 🧹 存储timeout引用
            resourceRefs.current.timeouts.push(timer2);
        }, 200); // 稍微增加延迟，让闪烁完成
        
        // 🧹 存储timeout引用
        resourceRefs.current.timeouts.push(timer1);
    }, [titleChanging]);
    
    const handleTitleNext = useCallback(() => {
        if (titleChanging) return; // 防止重复点击
        
        setTitleChanging(true);
        
        // 1. 先让旧内容闪烁消失
        if (contentAreaRef.current) {
            const oldContentFlash = AnimationUtils.createFlashEffect(contentAreaRef, {
                duration: 0.2,
                delay: 0,
                mode: 'brightness',
                flashCount: 6,
                flashDuration: 0.015,
                minBrightness: 0.2,
                maxBrightness: 1.0
            });
            if (oldContentFlash) animationTimelines.current.push(oldContentFlash);
        }
        
        const timer1 = setTimeout(() => {
            setCurrentTitleIndex(prev => 
                prev === TITLE_OPTIONS.length - 1 ? 0 : prev + 1
            );
            
            // 2. 切换内容后，让新内容闪烁进入
            const timer2 = setTimeout(() => {
                setTitleChanging(false);
                
                if (contentAreaRef.current) {
                    const newContentFlash = AnimationUtils.createFlashEffect(contentAreaRef, {
                        duration: 0.3,
                        delay: 0,
                        mode: 'brightness',
                        flashCount: 8,
                        flashDuration: 0.02,
                        minBrightness: 0.4,
                        maxBrightness: 1.8
                    });
                    if (newContentFlash) animationTimelines.current.push(newContentFlash);
                }
            }, 50);
            
            // 🧹 存储timeout引用
            resourceRefs.current.timeouts.push(timer2);
        }, 200); // 稍微增加延迟，让闪烁完成
        
        // 🧹 存储timeout引用
        resourceRefs.current.timeouts.push(timer1);
    }, [titleChanging]);
    
    // 获取当前标题
    const currentTitle = useMemo(() => TITLE_OPTIONS[currentTitleIndex], [currentTitleIndex]);

    // 注意：ECharts初始化现在由FlowPage组件负责，避免重复初始化

    // 注意：图表数据更新和resize处理现在由FlowPage组件负责

        // 🚀 处理3D模型容器尺寸变化 - 性能优化版本
        const handle3DResize = useCallback(() => {
            if (threeRendererRef.current && threeDContainerRef.current) {
                const rect = threeDContainerRef.current.getBoundingClientRect();
                if (rect.width > 0 && rect.height > 0) {
                    // 更新渲染器尺寸
                    threeRendererRef.current.setSize(rect.width, rect.height);
                    
                    // 更新相机宽高比
                    if (threeCameraRef.current) {
                        threeCameraRef.current.aspect = rect.width / rect.height;
                        threeCameraRef.current.updateProjectionMatrix();
                    }
                    
                    console.log('📐 3D渲染器尺寸更新:', rect.width, 'x', rect.height);
                }
            }
        }, []);

        // 使用优化的resize监听器
        useOptimizedResize(handle3DResize, 150);

        // 添加全局鼠标事件监听，支持拖拽到容器外的情况
        useEffect(() => {
            const handleGlobalMouseMove = (event) => {
                if (mouseInteraction.isMouseDown) {
                    handleMouseMove(event);
                }
            };

            const handleGlobalMouseUp = (event) => {
                if (mouseInteraction.isMouseDown) {
                    handleMouseUp(event);
                }
            };

            // 只有在拖拽状态时才添加全局监听器
            if (mouseInteraction.isMouseDown) {
                document.addEventListener('mousemove', handleGlobalMouseMove);
                document.addEventListener('mouseup', handleGlobalMouseUp);
                
                return () => {
                    document.removeEventListener('mousemove', handleGlobalMouseMove);
                    document.removeEventListener('mouseup', handleGlobalMouseUp);
                };
            }
        }, [mouseInteraction.isMouseDown, handleMouseMove, handleMouseUp]);

    // 监听视图切换，强制重新初始化图表
    useEffect(() => {
        // 当切换到流量视图时，FlowPage组件会自动处理图表初始化
        if (currentTitleIndex === 0) {
            console.log('🔄 切换到流量视图 - 图表由FlowPage组件管理');
        }
        
        // 当切换到报警视图时，启动自动滚动
        if (currentTitleIndex === 2) {
            
            const startAutoScroll = () => {
                if (alarmScrollContainerRef.current && alarmListRef.current) {
                    // 获取容器高度来计算滚动距离
                    const containerHeight = alarmListRef.current.clientHeight;
                    const contentHeight = alarmScrollContainerRef.current.scrollHeight;
                    
                    // 计算真正的滚动距离：如果内容高度小于容器高度，则不滚动
                    const scrollDistance = contentHeight > containerHeight * 2 ? 
                        -(contentHeight / 2) : // 滚动一半的距离
                        -containerHeight; // 如果内容不够高，滚动一个容器的高度
                    
                    // 创建无限滚动动画
                    const scrollAnimation = gsap.to(alarmScrollContainerRef.current, {
                        y: scrollDistance,
                        duration: 16, // 更加缓慢的滚动：16秒一轮
                        ease: 'none', // 线性动画，保持匀速
                        repeat: -1, // 无限重复
                        onRepeat: () => {
                            // 滚动完成后重置位置，实现无缝循环
                            gsap.set(alarmScrollContainerRef.current, { y: 0 });
                        }
                    });
                    
                    // 存储动画引用用于清理和控制
                    scrollAnimationRef.current = scrollAnimation;
                    animationTimelines.current.push(scrollAnimation);
                    console.log('✅ 报警列表自动滚动已启动，滚动距离:', scrollDistance);
                }
            };
            
            // 延迟启动，确保DOM渲染完成
            const scrollTimer = setTimeout(startAutoScroll, 500);
            resourceRefs.current.timeouts.push(scrollTimer);
        } 
        // 当切换到设备视图时，3D模型由DevicePage组件自动初始化
        else if (currentTitleIndex === 3) {
            // DevicePage组件会自动处理3D模型的创建和动画
            console.log('🔄 切换到设备视图 - 3D模型由DevicePage组件管理');
        } 
        else {
            // 切换到其他视图时，清理相关资源
            if (scrollAnimationRef.current) {
                scrollAnimationRef.current.kill();
                scrollAnimationRef.current = null;
            }
            setAlarmScrollPaused(false);
            setManualScrollPosition(0);
            
            // 清理3D资源
            cleanup3DResources();
        }
    }, [currentTitleIndex, cleanup3DResources]); // 修改依赖数组，移除已删除的函数

    // 🎯 动态更新底部累计流量显示
    useEffect(() => {
        if (cumulativeFlowRef.current && cumulativeFlowRef.current.dataset.typed === 'true') {
            const newText = `累计: ${dimensionData.monthUsage.toLocaleString('zh-CN', { minimumFractionDigits: 1, maximumFractionDigits: 1 })} m³`;
            
            // 平滑更新文本内容
            gsap.to(cumulativeFlowRef.current, {
                opacity: 0.3,
                duration: 0.2,
                onComplete: () => {
                    cumulativeFlowRef.current.innerHTML = newText;
                    gsap.to(cumulativeFlowRef.current, {
                        opacity: 0.7,
                        duration: 0.3
                    });
                }
            });
            
            console.log(`📊 底部累计流量更新: ${dimensionData.monthUsage.toFixed(1)} m³`);
        }
    }, [dimensionData.monthUsage]);

    // 组件卸载时清理所有资源 - 最后的保障
    useEffect(() => {
        return () => {
            cleanupAllResources();
        };
    }, [cleanupAllResources]);

    // 🎯 性能优化：渲染当前页面内容，使用独立的页面组件
    const renderPageContent = useMemo(() => {
        switch (currentTitleIndex) {
            case 0: // 流量
                return (
                    <FlowPage
                        currentFlowRate={currentFlowRate}
                        timeSeriesData={timeSeriesData}
                        showRealFlow={showRealFlow}
                        calculateAverageValue={calculateAverageValue}
                    />
                );
            
            case 1: // 数据
                return (
                    <DataPage
                        dimensionData={dimensionData}
                        dimensionFlashing={dimensionFlashing}
                        dimensionRefs={dimensionRefs}
                    />
                );
                
            case 2: // 报警
                return (
                    <AlarmPage
                        alarmData={alarmData}
                        alarmListRef={alarmListRef}
                        alarmScrollContainerRef={alarmScrollContainerRef}
                        scrollAnimationRef={scrollAnimationRef}
                        alarmScrollPaused={alarmScrollPaused}
                        setAlarmScrollPaused={setAlarmScrollPaused}
                        setManualScrollPosition={setManualScrollPosition}
                        animationTimelines={animationTimelines}
                        resourceRefs={resourceRefs}
                    />
                );
                
            case 3: // 设备 - 3D模型展示
                return (
                    <DevicePage
                        threeDContainerRef={threeDContainerRef}
                        threeSceneRef={threeSceneRef}
                        threeRendererRef={threeRendererRef}
                        threeCameraRef={threeCameraRef}
                        flowmeterModelRef={flowmeterModelRef}
                        threeAnimationIdRef={threeAnimationIdRef}
                        mouseInteraction={mouseInteraction}
                        setMouseInteraction={setMouseInteraction}
                        handleMouseDown={handleMouseDown}
                        handleMouseMove={handleMouseMove}
                        handleMouseUp={handleMouseUp}
                        handleMouseLeave={handleMouseLeave}
                        handleMouseWheel={handleMouseWheel}
                        rotationStateRef={rotationStateRef}
                        resourceRefs={resourceRefs}
                    />
                );
                
            default:
                return null;
        }
    }, [
        currentTitleIndex, 
        currentFlowRate, 
        timeSeriesData, 
        showRealFlow, 
        calculateAverageValue,
        dimensionData,
        dimensionFlashing,
        alarmData,
        alarmScrollPaused,
        mouseInteraction
    ]);

    return (
        <div ref={containerRef} className="flowmeter-container w-full h-full bg-transparent flex flex-col">
            {/* 顶部标题栏 */}
            <div className="title-header h-[28.5px] flex items-center justify-between pl-[10px] flex-shrink-0">
                {/* 左侧标题切换区域 */}
                <div 
                    ref={titleRef}
                    className="title-switcher flex items-center ml-[10px] overflow-hidden"
                    style={{
                        transform: animationState.showTitle ? 'translateY(0) scale(1)' : 'translateY(10px) scale(0.8)',
                        opacity: animationState.showTitle ? 1 : 0,
                        transition: animationState.showTitle ? 'transform 0.6s ease-out, opacity 0.6s ease-out' : 'all 0.6s ease-out'
                    }}
                >
                    {/* 左箭头按钮 */}
                    <div
                        onClick={handleTitlePrev}
                        className="prev-btn w-[20px] h-[24px] flex items-center justify-center transition-colors duration-200 cursor-pointer"
                        title="上一个"
                        style={{ pointerEvents: animationState.isClosing ? 'none' : 'auto' }}
                    >
                        <div 
                            className="w-[0] h-[0] border-t-[5px] border-t-transparent border-b-[5px] border-b-transparent border-r-[7px] border-r-white"
                        ></div>
                    </div>
                    
                    {/* 标题文字 */}
                    <div 
                        className="bg-[#246F49] rounded-[3px] title-text text-[12px] font-['DingTalkJinBuTi',sans-serif] font-medium text-white px-[8px] py-[0] min-w-[40px] text-center transition-all duration-300"
                        style={{
                            opacity: titleChanging ? 0.3 : 1,
                            transform: titleChanging ? 'scale(0.9)' : 'scale(1)'
                        }}
                    >
                        {currentTitle}
                    </div>
                    
                    {/* 右箭头按钮 */}
                    <div
                        onClick={handleTitleNext}
                        className="next-btn w-[20px] h-[24px] flex items-center justify-center transition-colors duration-200 cursor-pointer"
                        title="下一个"
                        style={{ pointerEvents: animationState.isClosing ? 'none' : 'auto' }}
                    >
                        <div 
                            className="w-[0] h-[0] border-t-[5px] border-t-transparent border-b-[5px] border-b-transparent border-l-[7px] border-l-white"
                        ></div>
                    </div>
                </div>
                
                {/* 右侧状态和操作区 */}
                <div 
                    ref={statusButtonRef}
                    className="status-actions flex items-center"
                    style={{
                        opacity: animationState.showStatusButton ? 1 : 0,
                        transform: animationState.showStatusButton ? 'scale(1)' : 'scale(0.8)',
                        transition: animationState.showStatusButton ? 'transform 0.3s ease-out, opacity 0.3s ease-out' : 'all 0.3s ease-out'
                    }}
                >
                    {/* 设备状态指示器 */}
                    <div className="status-indicator flex items-center gap-[4px] mr-[8px]">
                        <div 
                            className="status-dot w-[6px] h-[6px] rounded-full" 
                            style={{ backgroundColor: statusColor }}
                        ></div>
                        <span 
                            className="status-text text-[10px] font-['DingTalkJinBuTi',sans-serif]"
                            style={{ color: statusColor }}
                        >
                            {deviceStatus}
                        </span>
                    </div>
                    
                    {/* 分隔符 */}
                    <div className="separator w-[1px] h-[12px] bg-[#444447] mr-[8px]"></div>
                    
                    {/* 关闭按钮 */}
                    <div
                        onClick={handleClose}
                        className="close-btn w-[16px] h-[16px] flex items-center justify-center hover:opacity-70 transition-opacity duration-200 cursor-pointer bg-[#333135]"
                        title="关闭流量计"
                        style={{ pointerEvents: animationState.isClosing ? 'none' : 'auto' }}
                    >
                        <svg 
                            xmlns="http://www.w3.org/2000/svg" 
                            width="12" 
                            height="12" 
                            viewBox="0 0 24 24" 
                            fill="none" 
                            stroke="#B3B2B6"
                            strokeWidth="2.5" 
                            strokeLinecap="round" 
                            strokeLinejoin="round"
                        >
                            <path d="M18 6L6 18"/>
                            <path d="M6 6l12 12"/>
                        </svg>
                    </div>
                </div>
            </div>
            
            {/* 分色线条 - 单独绘制带开场动画 */}
            <div className="divider-line w-full h-[1px] flex flex-shrink-0 relative overflow-hidden">
                {/* 左半部分 - 绿色线条 */}
                <div 
                    ref={leftLineRef}
                    className="left-line absolute top-[0] left-[0] w-[50%] h-[1px] bg-[#23C76D]"
                    style={{
                        transform: animationState.showTopLines ? 'scaleX(1)' : 'scaleX(0)',
                        transformOrigin: 'left',
                        opacity: animationState.showTopLines ? 1 : 0,
                        transition: animationState.showTopLines ? 'transform 0.8s ease-out' : 'all 0.8s ease-out'
                    }}
                ></div>
                {/* 右半部分 - 深绿色线条 */}
                <div 
                    ref={rightLineRef}
                    className="right-line absolute top-[0] right-[0] w-[50%] h-[1px] bg-[#22764B]"
                    style={{
                        transform: animationState.showTopLines ? 'scaleX(1)' : 'scaleX(0)',
                        transformOrigin: 'right',
                        opacity: animationState.showTopLines ? 1 : 0,
                        transition: animationState.showTopLines ? 'transform 0.8s ease-out' : 'all 0.8s ease-out'
                    }}
                ></div>
            </div>
            
            {/* 2px间距 */}
            <div className="spacing h-[1px] w-full flex-shrink-0"></div>
            
            {/* 设备名称容器 */}
            <div 
                ref={titleBarRef}
                className="device-name-container w-full bg-[#333236] px-[5px] flex-shrink-0"
                style={{
                    transform: animationState.showTitleBar ? 'scaleX(1)' : 'scaleX(0)',
                    transformOrigin: 'center',
                    opacity: animationState.showTitleBar ? 1 : 0,
                    transition: animationState.showTitleBar ? 'transform 0.6s ease-out, opacity 0.6s ease-out' : 'all 0.6s ease-out'
                }}
            >
                <div className="device-name text-[12px] font-['DingTalkJinBuTi',sans-serif] text-white text-left">
                    市人民医院1号流量计
                </div>
            </div>
            
            {/* 主要内容区域 */}
            <div 
                ref={contentAreaRef}
                className="content-area flex-grow min-h-0 overflow-y-hidden"
                style={{
                    opacity: animationState.showContentArea ? 1 : 0,
                    transition: animationState.showContentArea ? 'opacity 0.8s ease-out' : 'opacity 0.6s ease-out'
                }}
            >
                <div className="w-full h-full font-['DingTalkJinBuTi',sans-serif]">
                    {renderPageContent}
                </div>
            </div>
            
            {/* 底部信息栏 - 使用lmh.jsx样式和动画 */}
            <div 
                ref={el => {
                    bottomInfoBarRef.current = el;
                    if (el && !el.dataset.animated) {
                        // 添加标记避免重复执行
                        el.dataset.animated = 'true';
                        
                        // 设置初始状态：使用scaleY实现从底部向上扩散
                        gsap.set(el, {
                            scaleY: 0,
                            transformOrigin: 'bottom',
                            opacity: 0
                        });
                        
                        // 延迟1.5秒后开始底部栏向上扩散动画
                        const decorationTL = gsap.timeline({ 
                            delay: 1.5,
                            onComplete: () => {
                                el.style.opacity = '1';
                                el.style.transform = 'scaleY(1)';
                            }
                        });
                        
                        // 从底部向上扩散
                        decorationTL.to(el, {
                            scaleY: 1,
                            opacity: 1,
                            duration: 0.4,
                            ease: 'power2.out'
                        })
                        // 频闪效果 - 与扩散同时进行
                        .fromTo(el, 
                            { opacity: 1 },
                            { 
                                opacity: 0.3, 
                                duration: 0.07,
                                ease: 'steps(1)',
                                repeat: 5, // 6次闪烁
                                yoyo: true
                            }, '<') // 与扩散同时开始
                        // 最后确保停在稳定状态
                        .set(el, {
                            opacity: 1,
                            scaleY: 1
                        });
                    }
                }}
                className="bottom-info-bar w-full h-[29px] bg-gradient-to-r from-[#1a1a1a]/50 via-[#2894B7]/5 to-[#1a1a1a]/50 backdrop-blur-sm border-t border-[#2894B7]/15 flex items-center justify-between flex-shrink-0"
                style={{
                    opacity: animationState.showBottomInfoBar ? 1 : 0,
                    transform: animationState.showBottomInfoBar ? 'scaleY(1)' : 'scaleY(0)',
                    transformOrigin: 'bottom',
                    transition: animationState.showBottomInfoBar ? 'opacity 0.3s ease-out, transform 0.3s ease-out' : 'opacity 0.2s ease-in, transform 0.2s ease-in'
                }}
            >
                <div className="left-info">
                    <span 
                        ref={el => {
                            cumulativeFlowRef.current = el; // 保存ref以便动态更新
                            if (el && !el.dataset.typed) {
                                el.dataset.typed = 'true';
                                
                                let text = `累计: ${dimensionData.monthUsage.toLocaleString('zh-CN', { minimumFractionDigits: 1, maximumFractionDigits: 1 })} m³`;
                                el.innerHTML = text;
                                const finalWidth = el.offsetWidth;
                                el.style.width = (finalWidth + 10) + 'px';
                                el.style.minWidth = (finalWidth + 10) + 'px';
                                el.style.whiteSpace = 'nowrap';
                                gsap.set(el, { opacity: 0 });
                                
                                // 延迟2.1秒后开始打字机效果
                                const delayedCall = gsap.delayedCall(2.1, () => {
                                    el.innerHTML = "";
                                    gsap.set(el, { opacity: 1 });
                                    
                                    const tl = gsap.timeline();
                                    for (let i = 0; i < text.length; i++) {
                                        tl.call(() => {
                                            el.innerHTML = text.substring(0, i + 1);
                                        }, null, i * 0.02);
                                    }
                                });
                                
                                // 🧹 存储GSAP延迟调用引用
                                resourceRefs.current.gsapCallbacks.push(delayedCall);
                            }
                        }}
                        className="text-[12px] text-[#23C26D] opacity-70 font-['DingTalkJinBuTi'] ml-[5px]"
                    ></span>
                </div>
                
                <div className="right-info">
                    <span 
                        ref={el => {
                            if (el && !el.dataset.typed) {
                                el.dataset.typed = 'true';
                                
                                let text = `${lastUpdate.toLocaleTimeString('zh-CN', { 
                                    hour: '2-digit', 
                                    minute: '2-digit', 
                                    second: '2-digit' 
                                })}`;
                                el.innerHTML = text;
                                const finalWidth = el.offsetWidth;
                                el.style.width = (finalWidth + 10) + 'px';
                                el.style.minWidth = (finalWidth + 10) + 'px';
                                el.style.fontSize = '12px';
                                el.style.whiteSpace = 'nowrap';
                                gsap.set(el, { opacity: 0 });
                                
                                const delayedCall2 = gsap.delayedCall(2.1, () => {
                                    el.innerHTML = "";
                                    gsap.set(el, { opacity: 1 });
                                    
                                    const tl = gsap.timeline();
                                    for (let i = 0; i < text.length; i++) {
                                        tl.call(() => {
                                            el.innerHTML = text.substring(0, i + 1);
                                        }, null, i * 0.015);
                                    }
                                });
                                
                                // 🧹 存储GSAP延迟调用引用
                                resourceRefs.current.gsapCallbacks.push(delayedCall2);
                            }
                        }}
                        className="text-[12px] text-[#2894B7] opacity-50 font-['DingTalkJinBuTi'] mr-[5px]"
                    ></span>
                </div>
            </div>
            
            {/* 底部绿线 - 单独绘制，右侧留20px间距，确保始终可见 */}
            <div className="bottom-line-container w-full h-[1px] flex-shrink-0 relative">
                <div 
                    ref={bottomLineRef}
                    className="bottom-line absolute top-[0] left-[0] right-[10px] h-[1px] bg-[#22764B] z-[10]"
                    style={{
                        transform: animationState.showBottomLine ? 'scaleX(1)' : 'scaleX(0)',
                        transformOrigin: 'center',
                        opacity: animationState.showBottomLine ? 1 : 0,
                        transition: animationState.showBottomLine ? 'transform 0.8s ease-out' : 'all 0.8s ease-out'
                    }}
                ></div>
            </div>
        </div>
    );
});

// 🎯 性能优化：添加显示名称便于调试
Flowmeter.displayName = 'Flowmeter';

export default Flowmeter;
