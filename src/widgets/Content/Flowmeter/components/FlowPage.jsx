import React, { useEffect, useRef, useMemo, useCallback } from 'react';
import NumberFlow from '@number-flow/react';
import * as echarts from 'echarts';

/**
 * 流量页面组件
 * 显示实时流量数据和时序图表
 */
const FlowPage = ({ 
    currentFlowRate, 
    timeSeriesData, 
    showRealFlow,
    calculateAverageValue 
}) => {
    const chartRef = useRef(null);
    const chartInstanceRef = useRef(null);
    
    // 🧹 资源清理：存储所有需要清理的资源引用
    const resourceRefs = useRef({
        timeouts: [], // 存储setTimeout的ID
        resizeObserver: null, // ResizeObserver实例
        resizeHandler: null, // resize事件处理函数
        isResizing: false, // 防抖标志
    });

    // 🚀 优化的resize处理函数 - 使用防抖机制
    const optimizedResize = useCallback(() => {
        if (resourceRefs.current.isResizing) return; // 防抖：如果正在resize，直接返回
        
        resourceRefs.current.isResizing = true;
        
        // 使用requestAnimationFrame确保在下一帧执行，优化性能
        const animationFrame = requestAnimationFrame(() => {
            if (chartInstanceRef.current && chartRef.current) {
                const rect = chartRef.current.getBoundingClientRect();
                if (rect.width > 0 && rect.height > 0) {
                    chartInstanceRef.current.resize();
                    // console.log('✅ 图表尺寸优化调整完成:', rect.width, 'x', rect.height);
                }
            }
            
            // 重置防抖标志
            const resetTimeout = setTimeout(() => {
                resourceRefs.current.isResizing = false;
            }, 100); // 100ms防抖间隔
            
            resourceRefs.current.timeouts.push(resetTimeout);
        });
        
        // 存储animationFrame ID（虽然无法直接取消，但作为引用）
        resourceRefs.current.timeouts.push(animationFrame);
    }, []);

    // 🔄 分离ECharts初始化和ResizeObserver设置 - 修复动态调整问题
    
    // 1️⃣ 仅初始化ECharts实例（只在组件挂载时执行一次）
    useEffect(() => {
        if (chartRef.current && !chartInstanceRef.current) {
            chartInstanceRef.current = echarts.init(chartRef.current);
            
            // 设置初始图表配置
            const option = {
                backgroundColor: 'transparent',
                animation: true,
                animationDurationUpdate: 300,
                grid: {
                    left: '2%',
                    right: '2%',
                    top: '5%',
                    bottom: '5%',
                    containLabel: false
                },
                xAxis: {
                    type: 'time',
                    splitLine: { show: false },
                    axisLine: { show: false },
                    axisTick: { show: false },
                    axisLabel: { show: false }
                },
                yAxis: {
                    type: 'value',
                    min: 60,
                    max: 200,
                    splitLine: { show: false },
                    axisLine: { show: false },
                    axisTick: { show: false },
                    axisLabel: { show: false }
                },
                series: [{
                    name: '瞬时流量',
                    type: 'line',
                    smooth: true,
                    symbol: 'none',
                    lineStyle: {
                        color: '#25C270',
                        width: 2
                    },
                    data: [], // 初始为空，由后续useEffect更新
                    markLine: {
                        symbol: 'none',
                        data: [{
                            yAxis: 125, // 初始平均值
                            name: '平均值线'
                        }],
                        label: {
                            formatter: () => '平均: 125.0',
                            position: 'insideEndBottom',
                            color: '#E39D25',
                            fontSize: 10
                        },
                        lineStyle: {
                            type: 'dashed',
                            color: '#E39D25',
                            width: 1,
                            // 🔧 修复虚线偶尔变粗的问题 - 强制样式锁定
                            opacity: 1, // 确保透明度一致
                            cap: 'round', // 线条端点样式
                            join: 'round' // 线条连接样式
                        }
                    }
                }],
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    borderColor: '#25C270',
                    textStyle: {
                        color: '#fff',
                        fontSize: 12
                    },
                    formatter: function(params) {
                        if (params && params.length > 0) {
                            const value = params[0].value[1];
                            const time = new Date(params[0].value[0]).toLocaleTimeString();
                            return `时间: ${time}<br/>流量: ${Number(value).toFixed(2)} m³/h`;
                        }
                        return '';
                    },
                    axisPointer: {
                        animation: false
                    }
                }
            };
            
            chartInstanceRef.current.setOption(option);
            // console.log('✅ ECharts实例初始化完成');
        }
    }, []); // 空依赖数组，只在组件挂载时执行一次

    // 2️⃣ 优化的动态调整功能 - 大幅简化和性能提升
    useEffect(() => {
        if (!chartInstanceRef.current || !chartRef.current) return;
        
        // console.log('🎯 开始设置优化的ECharts动态调整功能');
        
        // 🚀 优化的窗口resize处理 - 使用节流
        const handleWindowResize = () => {
            optimizedResize();
        };
        
        // ⚡ 使用passive事件监听器优化性能
        window.addEventListener('resize', handleWindowResize, { passive: true });
        resourceRefs.current.resizeHandler = handleWindowResize;
        
        // 🎯 精简的ResizeObserver - 只监听必要的容器
        const setupOptimizedResizeObserver = () => {
            if (!window.ResizeObserver || !chartRef.current) return null;
            
            const resizeObserver = new ResizeObserver((entries) => {
                if (chartInstanceRef.current && entries.length > 0) {
                    // 🚀 只在实际尺寸变化时才resize
                    let shouldResize = false;
                    entries.forEach(entry => {
                        const { width, height } = entry.contentRect;
                        // 检查是否有实质性的尺寸变化（避免微小变化触发）
                        if (width > 10 && height > 10) { // 最小尺寸阈值
                            shouldResize = true;
                        }
                    });
                    
                    if (shouldResize) {
                        optimizedResize();
                    }
                }
            });
            
            // 🎯 只监听最关键的容器 - 大幅减少监听数量
            const containersToObserve = [];
            
            // 1. 图表容器本身
            containersToObserve.push(chartRef.current);
            
            // 2. 只监听直接父容器
            if (chartRef.current.parentElement) {
                containersToObserve.push(chartRef.current.parentElement);
            }
            
            // 3. 只监听react-grid-item容器（如果存在）
            let ancestor = chartRef.current.parentElement;
            let gridItemFound = false;
            while (ancestor && ancestor !== document.body && !gridItemFound) {
                if (ancestor.classList.contains('react-grid-item')) {
                    containersToObserve.push(ancestor);
                    gridItemFound = true; // 找到后就停止，不再继续向上查找
                }
                ancestor = ancestor.parentElement;
            }
            
            // 开始监听 - 最多只监听3个容器
            containersToObserve.forEach((container) => {
                if (container) {
                    resizeObserver.observe(container);
                }
            });
            
            // console.log(`🎯 优化监听：总共监听了 ${containersToObserve.length} 个关键容器`);
            resourceRefs.current.resizeObserver = resizeObserver;
            return resizeObserver;
        };
        
        // 延迟设置ResizeObserver
        const setupTimer = setTimeout(() => {
            setupOptimizedResizeObserver();
            // console.log('✅ 优化的ResizeObserver设置完成');
        }, 200); // 减少延迟时间
        
        resourceRefs.current.timeouts.push(setupTimer);
        
        // ❌ 移除不必要的MutationObserver和定期检查 - 性能提升关键点
        
        return () => {
            // 清理资源
            window.removeEventListener('resize', handleWindowResize);
            
            if (resourceRefs.current.resizeObserver) {
                resourceRefs.current.resizeObserver.disconnect();
                resourceRefs.current.resizeObserver = null;
            }
            
            // console.log('🧹 优化的ECharts资源清理完成');
        };
    }, [optimizedResize]); // 依赖优化的resize函数

    // 3️⃣ 优化的图表数据更新 - 添加防抖机制
    const updateChartDataDebounced = useCallback((data, avgValue) => {
        if (!chartInstanceRef.current) return;
        
        // 使用requestAnimationFrame确保在最佳时机更新
        requestAnimationFrame(() => {
            if (chartInstanceRef.current) {
                chartInstanceRef.current.setOption({
                    series: [{
                        data: data,
                        markLine: {
                            symbol: 'none',
                            data: [{
                                yAxis: avgValue,
                                name: '平均值线'
                            }],
                            label: {
                                formatter: () => `平均: ${avgValue.toFixed(1)}`,
                                position: 'insideEndBottom',
                                color: '#E39D25',
                                fontSize: 10
                            },
                            lineStyle: {
                                type: 'dashed',
                                color: '#E39D25',
                                width: 1,
                                opacity: 1,
                                cap: 'round',
                                join: 'round'
                            }
                        }
                    }]
                }, false); // 强制完全替换
                
                // console.log('📊 图表数据优化更新完成，数据点:', data.length);
            }
        });
    }, []);

    useEffect(() => {
        if (timeSeriesData.length > 0) {
            const averageValue = calculateAverageValue(timeSeriesData);
            updateChartDataDebounced(timeSeriesData, averageValue);
        }
    }, [timeSeriesData, calculateAverageValue, updateChartDataDebounced]);

    // 4️⃣ 简化的初始resize
    useEffect(() => {
        const timer = setTimeout(() => {
            optimizedResize();
        }, 300); // 减少延迟
        
        resourceRefs.current.timeouts.push(timer);
        return () => clearTimeout(timer);
    }, [optimizedResize]);

    // 组件卸载时清理所有资源
    useEffect(() => {
        return () => {
            // 清理所有定时器和AnimationFrame
            resourceRefs.current.timeouts.forEach(id => {
                if (id) {
                    clearTimeout(id);
                    // AnimationFrame ID也会被尝试清理（虽然可能无效，但不会报错）
                }
            });
            resourceRefs.current.timeouts = [];
            
            // 清理事件监听器
            if (resourceRefs.current.resizeHandler) {
                window.removeEventListener('resize', resourceRefs.current.resizeHandler);
                resourceRefs.current.resizeHandler = null;
            }
            
            // 清理观察者
            if (resourceRefs.current.resizeObserver) {
                resourceRefs.current.resizeObserver.disconnect();
                resourceRefs.current.resizeObserver = null;
            }
            
            // 清理ECharts实例
            if (chartInstanceRef.current) {
                chartInstanceRef.current.dispose();
                chartInstanceRef.current = null;
            }
            
            // console.log('🧹 FlowPage优化资源清理完成');
        };
    }, []);

    // 计算当前显示的流量值
    const displayFlowRate = useMemo(() => {
        if (!showRealFlow) {
            return 0.00; // 前2秒显示0.00
        }
        return currentFlowRate;
    }, [currentFlowRate, showRealFlow]);

    return (
        <div className="flow-page w-full h-full flex gap-[10px]">
            {/* 左侧：数字显示区域 */}
            <div className="left-section flex-shrink-0 flex flex-col justify-center w-[80px]">
                <div className="main-flow text-center">
                    <NumberFlow
                        value={parseFloat(displayFlowRate.toFixed(2))}
                        precision={2}
                        className="text-[24px] text-[#23C76D] font-light"
                        style={{ fontFamily: 'ChakraPetch-Light, monospace' }}
                        duration={800}
                    />
                </div>
                <div className="secondary-data text-center mt-[-5px]">
                    <span className="text-[#D2D1DA] text-[12px] font-light rounded-[999px] px-[10px] bg-[#17474C]/60">
                        m³/h
                    </span>
                </div>
            </div>
            
            {/* 右侧：图表区域 */}
            <div className="right-section flex-1 flex flex-col min-w-[0]">
                <div className="chart-container flex-1 w-full h-full overflow-hidden">
                    {timeSeriesData.length > 0 ? (
                        <div 
                            ref={chartRef}
                            className='w-full h-full bg-[#1a1a1d]/30'
                            style={{
                                // 🚀 GPU加速优化
                                willChange: 'auto', // 默认不启用will-change，避免过度GPU占用
                                transform: 'translateZ(0)', // 强制硬件加速
                                backfaceVisibility: 'hidden' // 优化渲染性能
                            }}
                        />
                    ) : (
                        <div className="w-full h-full flex items-center justify-center text-[#909999] text-[12px]">
                            数据准备中...
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default FlowPage;