import React from 'react';
import FlowMeterDevice from '../DevicesList/components/FlowMeterDevice';
import PressureGaugeDevice from '../DevicesList/components/PressureGaugeDevice';
import Flowmeter from '../Flowmeter/Flowmeter';

/**
 * 独立设备容器组件 - 用于ResponsiveGridLayout内部
 * @param {Object} props
 * @param {Object} props.deviceData - 设备数据
 * @param {Function} props.onRemove - 移除设备的回调函数
 * @param {boolean} props.itemsVisible - 控制开场动画的可见性状态
 * @returns {JSX.Element}
 */
const IndependentDeviceContainer = ({ deviceData, onRemove, itemsVisible = false }) => {
    // 检查是否为预览设备或实时拖拽设备
    const isPreview = deviceData.id.startsWith('preview-');
    const isLiveDrag = deviceData.id.startsWith('live-');
    const isBeingDragged = deviceData.gridLayoutItem?.isBeingDragged;
    
    // 根据设备类型渲染对应的组件
    const renderDeviceByType = (item) => {
        // 统一渲染为流量计组件，传递关闭回调
        return <Flowmeter onClose={onRemove} />;
    };



    return (
        <div 
            className={`independent-device-container bg-white rounded-lg p-[4px] shadow-sm relative group hover:shadow-lg transition-shadow duration-200 grid-item-animate ${itemsVisible ? 'grid-item-visible' : ''} flex flex-col h-full w-full`}
        >
            {/* 拖动手柄 - 与默认容器一致 */}
            <div className="drag-handle absolute top-[8px] left-[5px] z-10 p-[1px]">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="rgba(90,170,180,0.5)" className="hover:fill-[rgba(90,170,180,0.8)] transition-colors duration-200">
                    <circle cx="4" cy="4" r="1.5" />
                    <circle cx="8" cy="4" r="1.5" />
                    <circle cx="12" cy="4" r="1.5" />
                    <circle cx="4" cy="8" r="1.5" />
                    <circle cx="8" cy="8" r="1.5" />
                    <circle cx="12" cy="8" r="1.5" />
                    <circle cx="4" cy="12" r="1.5" />
                    <circle cx="8" cy="12" r="1.5" />
                    <circle cx="12" cy="12" r="1.5" />
                </svg>
            </div>

            {/* 设备内容区域 - 控制宽度不溢出 */}
            <div 
                className="flex-1 w-full h-full min-h-0 overflow-hidden" 
            >
                {renderDeviceByType(deviceData)}
            </div>



        </div>
    );
};

export default IndependentDeviceContainer; 