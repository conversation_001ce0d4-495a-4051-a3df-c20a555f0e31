import React from 'react';
import { useDrop } from 'react-dnd';
import { useDragContext, DRAG_TYPES } from '../../../hooks/useDragContext.jsx';

/**
 * 放置目标组件 - 用于整个ResponsiveGridExample页面
 * @param {Object} props
 * @param {React.ReactNode} props.children - 子组件
 * @param {string} props.className - CSS类名
 * @returns {JSX.Element}
 */
const DropTarget = ({ children, className = '' }) => {
    const { 
        addDroppedDevice, 
        setPreviewDeviceData,
        startLiveGridDrag,
        finalizeLiveGridDrag,
        cancelLiveGridDrag,
        liveGridDevice
    } = useDragContext();

    const [{ isOver, canDrop }, drop] = useDrop({
        accept: DRAG_TYPES.DEVICE,
        hover: (item, monitor) => {
            // 检查是否在目标区域内悬停
            if (monitor.isOver({ shallow: true })) {
                // 如果还没有开始实时网格拖拽，则启动
                if (!liveGridDevice) {
                    startLiveGridDrag(item.deviceData, monitor);
                }
            }
        },
        drop: (item, monitor) => {
            // 清除预览设备
            setPreviewDeviceData(null);
            
            let droppedDevice;
            
            // 检查是否有实时网格拖拽设备
            if (liveGridDevice) {
                // 使用实时网格拖拽的最终确认
                droppedDevice = finalizeLiveGridDrag();
                
                // console.log(`✅ 设备 ${item.deviceData.name} 已通过实时网格拖拽成功放置`); // 已注释掉，减少控制台输出
                
                // 返回实时网格拖拽结果
                return {
                    success: true,
                    deviceId: droppedDevice.id,
                    targetType: 'GridLayout',
                    isIndependent: true,
                    isLiveGridDrop: true // 标识为实时网格拖拽
                };
            } else {
                // 传统拖拽方式（兼容性）
                const deviceData = item.deviceData;
                droppedDevice = addDroppedDevice(deviceData);
                
                // console.log(`✅ 设备 ${deviceData.name} 已成功放置到网格布局页面作为独立容器`); // 已注释掉，减少控制台输出
                
                // 返回传统放置结果
                return {
                    success: true,
                    deviceId: droppedDevice.id,
                    targetType: 'GridLayout',
                    isIndependent: true,
                    isLiveGridDrop: false
                };
            }
        },
        collect: (monitor) => ({
            isOver: monitor.isOver(),
            canDrop: monitor.canDrop(),
        }),
    });

    // 放置区域的样式
    const dropStyle = {
        position: 'relative',
        minHeight: '100%',
        transition: 'all 0.3s ease-in-out',
    };

    // 拖拽悬停时的样式 - 更强烈的视觉反馈
    const hoverStyle = isOver && canDrop ? {
        backgroundColor: 'rgba(90, 170, 180, 0.15)',
        borderColor: 'rgba(90, 170, 180, 0.8)',
        borderWidth: '2px',
        borderStyle: 'dashed',
        borderRadius: '8px',
    } : {};

    return (
        <div
            ref={drop}
            className={`drop-target ${className}`}
            style={{ ...dropStyle, ...hoverStyle }}
        >
            {/* 拖拽悬停时的增强视觉反馈 */}
            {isOver && canDrop && (
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-green-500/10 rounded-lg pointer-events-none transition-all duration-200">
                    <div className="absolute inset-2 border-2 border-dashed border-cyan-400/60 rounded-lg animate-pulse"></div>
                    <div className="absolute top-[50%] left-[50%] transform -translate-x-[50%] -translate-y-[50%] bg-cyan-500/20 text-cyan-700 text-[14px] font-medium px-[8px] py-[2px] rounded-full backdrop-blur-sm">
                        拖拽到此处放置
                    </div>
                </div>
            )}
            
            {children}
        </div>
    );
};

export default DropTarget; 