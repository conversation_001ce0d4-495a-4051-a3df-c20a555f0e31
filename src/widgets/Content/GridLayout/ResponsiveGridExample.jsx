import React, { useState, useEffect, useCallback, useMemo, memo, useRef } from 'react';
import { Responsive, WidthProvider } from 'react-grid-layout';
// import { usePerformanceMonitor, useRenderTimeMonitor } from '../../../hooks/usePerformanceMonitor';
// 使用 vite-plugin-svgr 导入 SVG 作为 React 组件
// import Demo1SvgComponent from '../../../assets/imgs/demo1.svg?react'; - 由子组件导入
// 导入GSAP库
// import { gsap } from 'gsap'; - 由子组件导入
// 导入SocketConsole组件
import SocketConsole from '../SocketConsole/SocketConsole';
// 导入DevicesList组件
import DevicesList from '../DevicesList/DevicesList';
// 导入DeviceInfo组件
import DeviceInfo from '../DeviceInfo/DeviceInfo';
// 导入Flowmeter组件 - 恢复到原始版本
import Flowmeter from '../Flowmeter/Flowmeter';
// 导入封装的SVG组件
import Demo1Svg from '../Flowchart/Demo1Svg';
// 老马河
import Lmh from '../Flowchart/lmh';
// 老马河 PixiJS 版本
import Lmh2 from '../Flowchart/lmh2';
// 导入拖拽相关组件
import DropTarget from './DropTarget';
import IndependentDeviceContainer from './IndependentDeviceContainer';
import { useDragContext, DRAG_TYPES } from '../../../hooks/useDragContext.jsx';
import Konva from 'konva';

// 创建响应式网格布局组件
const ResponsiveGridLayout = WidthProvider(Responsive);

// 自定义样式 - 提取为常量避免重复创建
const CUSTOM_GRID_STYLES = `
  /* React Grid Layout 基础样式 - 修复拖放容器宽度不一致问题 */
  .react-grid-layout {
    position: relative;
  }

  .react-grid-placeholder {
    background: #1E6273 !important;  /* 1D2836 */
    opacity: 0.1;
    transition-duration: 100ms;
    z-index: 2;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    -o-user-select: none;
    user-select: none;
  }
  
  .react-resizable-handle {
    bottom: 0px !important;
    right: 0px !important;
  }
  
  .react-resizable-handle::after {
    border-right: 1.5px solid rgba(90,170,180,0.8) !important;
    border-bottom: 1.5px solid rgba(90,170,180,0.8) !important;
  }
  
  .react-resizable-handle:hover::after {
    border-right: 1.5px solid rgba(90,170,180) !important;
    border-bottom: 1.5px solid rgba(90,170,180) !important;
  }

  /* 针对拖拽进来的容器，调整右下角手柄的位置 */
  .dropped-grid-item .react-resizable-handle::after {
    content: '' !important;
    position: absolute !important;
    right: -5px !important;
    bottom: -5px !important;
  }

  /* 添加闪烁动画的CSS */
  @keyframes fadeInAndFlash {
    0% { opacity: 0; }
    50% { opacity: 1; }
    75% { opacity: 0.5; }
    100% { opacity: 1; }
  }

  .grid-item-animate {
    opacity: 0; /* 初始透明 */
    animation-fill-mode: forwards; /* 动画结束后保持最后一帧的状态 */
  }

  .grid-item-visible {
    animation-name: fadeInAndFlash;
    animation-duration: 0.8s; /* 动画持续时间，可以调整 */
    animation-timing-function: ease-in-out;
  }

  /* 隐藏拖放容器的resize手柄直到动画开始 */
  .react-grid-item:has(.independent-device-container.grid-item-animate:not(.grid-item-visible)) .react-resizable-handle {
    display: none !important;
  }

  /* 隐藏所有处于动画初始状态的grid项的resize手柄 */
  .react-grid-item:has(.grid-item-animate:not(.grid-item-visible)) .react-resizable-handle {
    display: none !important;
  }
  
  /* 移除按钮焦点边框 */
  button:focus {
    outline: none !important;
    box-shadow: none !important;
    border-color: transparent !important;
  }
  
  /* 移除所有webkit默认样式 */
  button:focus,
  button:active:focus,
  button.active:focus,
  button.focus,
  button:active.focus,
  button.active.focus {
    outline: none !important;
    outline-offset: 0 !important;
    -webkit-box-shadow: none !important;
    box-shadow: none !important;
  }

  /* 拖动手柄样式 */
  .drag-handle {
    cursor: grab;
  }
  
  .drag-handle:active {
    cursor: grabbing !important;
  }
  
  /* 确保拖动手柄在拖动时保持抓取状态 */
  .react-draggable-dragging .drag-handle {
    cursor: grabbing !important;
  }
`;

// 样式注入 - 使用IIFE立即执行，避免重复执行
const injectStyles = (() => {
    let injected = false;
    return () => {
        if (typeof document !== 'undefined' && !injected && !document.getElementById('custom-grid-layout-styles')) {
            const styleElement = document.createElement('style');
            styleElement.id = 'custom-grid-layout-styles';
            styleElement.textContent = CUSTOM_GRID_STYLES;
            document.head.appendChild(styleElement);
            injected = true;
        }
    };
})();

// 默认布局配置 - 提取为常量避免重复创建
const DEFAULT_LAYOUTS = {
    lg: [
        { i: 'svgDemo', x: 0, y: 0, w: 6, h: 4 },  // Demo1 SVG展示区域
        { i: 'devicesList', x: 0, y: 4, w: 6, h: 3 }, // DevicesList区域
        { i: 'emptyContainer1', x: 6, y: 4, w: 6, h: 3 }, // 空容器1 (DeviceInfo)
        { i: 'lmhSvg', x: 0, y: 7, w: 4, h: 3 }, // Lmh Svg in former emptyContainer2
        { i: 'lmh2Svg', x: 6, y: 0, w: 6, h: 4 }, // PixiJS 版本泵房工况图
        { i: 'emptyContainer3', x: 4, y: 7, w: 4, h: 3 }, // 空容器3
        { i: 'socketConsole', x: 8, y: 7, w: 4, h: 3 }, // SocketConsole区域
    ],
    md: [
        { i: 'svgDemo', x: 0, y: 0, w: 5, h: 4 },  // Demo1 SVG展示区域
        { i: 'devicesList', x: 0, y: 4, w: 5, h: 3 }, // DevicesList区域
        { i: 'emptyContainer1', x: 5, y: 4, w: 5, h: 3 }, // 空容器1
        { i: 'lmhSvg', x: 0, y: 7, w: 5, h: 3 }, // Lmh Svg in former emptyContainer2
        { i: 'lmh2Svg', x: 5, y: 0, w: 5, h: 4 }, // PixiJS 版本泵房工况图
        { i: 'emptyContainer3', x: 5, y: 7, w: 2, h: 3 }, // 空容器3
        { i: 'socketConsole', x: 7, y: 7, w: 3, h: 3 }, // SocketConsole区域
    ],
    sm: [
        { i: 'svgDemo', x: 0, y: 0, w: 6, h: 4 },  // Demo1 SVG展示区域
        { i: 'devicesList', x: 0, y: 8, w: 6, h: 3 }, // DevicesList区域
        { i: 'emptyContainer1', x: 0, y: 11, w: 2, h: 3 }, // 空容器1
        { i: 'lmhSvg', x: 2, y: 11, w: 2, h: 3 }, // Lmh Svg in former emptyContainer2
        { i: 'lmh2Svg', x: 0, y: 4, w: 6, h: 4 }, // PixiJS 版本泵房工况图
        { i: 'emptyContainer3', x: 4, y: 11, w: 2, h: 3 }, // 空容器3
        { i: 'socketConsole', x: 0, y: 14, w: 6, h: 3 }, // SocketConsole区域
    ],
    xs: [
        { i: 'svgDemo', x: 0, y: 0, w: 4, h: 4 },  // Demo1 SVG展示区域
        { i: 'devicesList', x: 0, y: 8, w: 4, h: 3 }, // DevicesList区域
        { i: 'emptyContainer1', x: 0, y: 11, w: 4, h: 2 }, // 空容器1
        { i: 'lmhSvg', x: 0, y: 13, w: 4, h: 2 }, // Lmh Svg in former emptyContainer2
        { i: 'lmh2Svg', x: 0, y: 4, w: 4, h: 4 }, // PixiJS 版本泵房工况图
        { i: 'emptyContainer3', x: 0, y: 15, w: 4, h: 2 }, // 空容器3
        { i: 'socketConsole', x: 0, y: 17, w: 4, h: 3 }, // SocketConsole区域
    ],
};

// 网格配置 - 提取为常量避免重复创建
const GRID_CONFIG = {
    breakpoints: { lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 },
    cols: { lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 },
    rowHeight: 60,
    margin: [16, 16],
    draggableHandle: '.drag-handle', // 添加拖动手柄类
    isDraggable: true,
    isResizable: true,
    compactType: null, // vertical
    allowOverlap: true,
    // preventCollision: false,
    // isBounded: true,
};

// 拖动手柄组件
const DragHandle = () => (
    <div className="drag-handle absolute top-[8px] left-[5px] z-10 p-[1px]">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="rgba(90,170,180,0.5)" className="hover:fill-[rgba(90,170,180,0.8)] transition-colors duration-200">
            <circle cx="4" cy="4" r="1.5" />
            <circle cx="8" cy="4" r="1.5" />
            <circle cx="12" cy="4" r="1.5" />
            <circle cx="4" cy="8" r="1.5" />
            <circle cx="8" cy="8" r="1.5" />
            <circle cx="12" cy="8" r="1.5" />
            <circle cx="4" cy="12" r="1.5" />
            <circle cx="8" cy="12" r="1.5" />
            <circle cx="12" cy="12" r="1.5" />
        </svg>
    </div>
);

const ResponsiveGridExample = () => {
    // 🚀 性能监控 - 暂时禁用以确保拖拽功能正常
    // usePerformanceMonitor({
    //     enabled: false,
    //     interval: 5000,
    //     logToConsole: false,
    // });
    
    // 🚀 渲染时间监控 - 暂时禁用
    // useRenderTimeMonitor('ResponsiveGridExample');

    // 注入样式
    injectStyles();

    // 创建一个ref来引用DevicesList容器
    const devicesListRef = useRef(null);

    // 新增：流量计容器显示状态
    const [showFlowmeter, setShowFlowmeter] = useState(true);

    // 拖拽上下文
    const {
        getIndependentDevices,
        removeDroppedDevice,
        addDroppedDevice,
        updateIndependentDeviceLayout,
        gridLayoutRef,
        draggedDevice, // 获取正在被拖拽的设备
    } = useDragContext();

    // 从localStorage加载保存的布局 - 使用useCallback优化
    const loadSavedLayouts = useCallback(() => {
        try {
            const saved = localStorage.getItem('grid-layouts');
            return saved ? JSON.parse(saved) : DEFAULT_LAYOUTS;
        } catch (error) {
            console.error('加载布局失败:', error);
            return DEFAULT_LAYOUTS;
        }
    }, []);

    // 布局状态 - 使用懒初始化优化
    const [layouts, setLayouts] = useState(() => loadSavedLayouts());
    const [itemsVisible, setItemsVisible] = useState(false); // 新增状态控制可见性

    // 获取独立设备
    const independentDevices = getIndependentDevices();

    // 生成包含独立设备的动态布局
    const generateDynamicLayouts = useCallback((staticLayouts) => {
        const dynamicLayouts = { ...staticLayouts };

        // 为每个断点生成布局
        Object.keys(dynamicLayouts).forEach(breakpoint => {
            const staticItems = [...dynamicLayouts[breakpoint]];
            const deviceItems = independentDevices.map(device => device.gridLayoutItem);

            // 直接合并静态和动态布局项
            dynamicLayouts[breakpoint] = [...staticItems, ...deviceItems];
        });

        return dynamicLayouts;
    }, [independentDevices]);

    // 保存布局到localStorage - 使用useCallback优化
    const saveLayoutsToStorage = useCallback((newLayouts) => {
        try {
            localStorage.setItem('grid-layouts', JSON.stringify(newLayouts));
            // console.log('✅ 布局已保存到缓存'); // 已注释掉，减少控制台输出
        } catch (error) {
            console.error('保存布局失败:', error);
        }
    }, []);

    // 布局变化回调 - 使用useCallback优化，添加防抖
    const onLayoutChange = useCallback((layout, allLayouts) => {
        // console.log('🔄 布局已变化:', allLayouts); // 已注释掉，减少控制台输出

        // 更新独立设备的布局信息
        const deviceLayouts = layout.filter(item => item.i.startsWith('dropped-'));
        if (deviceLayouts.length > 0) {
            updateIndependentDeviceLayout(deviceLayouts);
        }

        // 分离静态布局用于保存
        const staticLayouts = {};
        Object.keys(allLayouts).forEach(breakpoint => {
            staticLayouts[breakpoint] = allLayouts[breakpoint].filter(
                item => !item.i.startsWith('dropped-') && !item.i.startsWith('live-')
            );
        });

        // 更新静态布局状态
        setLayouts(staticLayouts);

        // 只保存静态布局到 localStorage
        const timeoutId = setTimeout(() => {
            saveLayoutsToStorage(staticLayouts);
        }, 300);

        return () => clearTimeout(timeoutId);
    }, [saveLayoutsToStorage, updateIndependentDeviceLayout]);

    // 重置布局 - 使用useCallback优化
    const resetLayouts = useCallback(() => {
        // console.log('🔧 重置布局到默认状态'); // 已注释掉，减少控制台输出
        const newLayouts = { ...DEFAULT_LAYOUTS };
        setLayouts(newLayouts);
        saveLayoutsToStorage(newLayouts);
    }, [saveLayoutsToStorage]);

    // 断点变化回调 - 使用useCallback优化
    const onBreakpointChange = useCallback((breakpoint, cols) => {
        // console.log('📱 断点变化:', breakpoint, '列数:', cols); // 已注释掉，减少控制台输出
    }, []);

    // 从拖拽事件中添加新设备的回调
    const onDrop = useCallback((layout, item, e) => {
        // 检查拖放事件是否发生在DevicesList容器内部
        if (devicesListRef.current && devicesListRef.current.contains(e.target)) {
            // console.log('📦 拖放回源区域，已取消添加。'); // 已注释掉，减少控制台输出
            return; // 如果是，则不执行任何操作
        }

        e.preventDefault();
        e.stopPropagation();
        try {
            const dataStr = e.dataTransfer.getData(DRAG_TYPES.DEVICE);
            if (!dataStr) return;

            const deviceData = JSON.parse(dataStr);
            // item 包含了由 react-grid-layout 计算出的 x, y, w, h
            addDroppedDevice(deviceData, item);
        } catch (err) {
            console.error("拖放处理失败:", err);
        }
    }, [addDroppedDevice]);

    // 监听重置事件 - 优化依赖数组
    useEffect(() => {
        const handleResetEvent = () => {
            resetLayouts();
        };

        window.addEventListener('resetGridLayout', handleResetEvent);

        return () => {
            window.removeEventListener('resetGridLayout', handleResetEvent);
        };
    }, [resetLayouts]);

    // 新增useEffect，用于延迟显示卡片并触发动画
    useEffect(() => {
        const timer = setTimeout(() => {
            setItemsVisible(true);
        }, 300); // 延迟300毫秒显示，可以调整

        return () => clearTimeout(timer);
    }, []);

    // 动态过滤布局，移除隐藏的流量计容器
    const filteredLayouts = useMemo(() => {
        const layouts_copy = { ...layouts };
        if (!showFlowmeter) {
            // 如果流量计隐藏，则从布局中移除 emptyContainer3
            Object.keys(layouts_copy).forEach(breakpoint => {
                layouts_copy[breakpoint] = layouts_copy[breakpoint].filter(
                    item => item.i !== 'emptyContainer3'
                );
            });
        }
        return layouts_copy;
    }, [layouts, showFlowmeter]);

    // 使用动态布局
    const currentLayouts = generateDynamicLayouts(filteredLayouts);

    // 动态计算droppingItem，根据屏幕尺寸设置默认值
    const getDefaultSize = () => {
        const screenWidth = window.innerWidth;
        if (screenWidth >= 1200) { // lg断点
            return { w: 1, h: 2 }; // 进一步减小宽度到最小
        } else if (screenWidth >= 996) { // md断点
            return { w: 1, h: 2 }; // 进一步减小宽度到最小
        } else if (screenWidth >= 768) { // sm断点
            return { w: 1, h: 2 }; // 进一步减小宽度到最小
        } else { // xs断点
            return { w: 1, h: 2 }; // 进一步减小宽度到最小
        }
    };

    const defaultSize = getDefaultSize();
    const droppingItem = {
        i: '___dropping-elem___',
        w: draggedDevice ? draggedDevice.w : defaultSize.w,
        h: draggedDevice ? draggedDevice.h : defaultSize.h,
    };

    return (
        <div className="h-full">
            <ResponsiveGridLayout
                ref={gridLayoutRef}
                className="layout"
                layouts={currentLayouts}
                onLayoutChange={onLayoutChange}
                onBreakpointChange={onBreakpointChange}
                isDroppable={true}
                onDrop={onDrop}
                onDragOver={(e) => e.preventDefault()} // 允许拖放
                droppingItem={droppingItem} // 使用动态的占位符
                {...GRID_CONFIG}
            >

                {/* Demo1 SVG展示区域 */}
                <div
                    key="svgDemo"
                    className={`bg-white rounded-lg p-[4px] shadow-sm relative group hover:shadow-lg transition-shadow duration-200 grid-item-animate ${itemsVisible ? 'grid-item-visible' : ''} flex flex-col`}
                    style={{ border: '1px solid rgba(90,170,180,0.5)' }}
                >
                    <DragHandle />
                    <div className="flex-1 w-full h-full min-h-0 overflow-hidden">
                        <Demo1Svg />
                    </div>
                </div>

                {/* DevicesList组件 */}
                <div
                    key="devicesList"
                    ref={devicesListRef} // 将ref附加到容器上
                    className={`bg-white rounded-lg p-[4px] shadow-sm relative group hover:shadow-lg transition-shadow duration-200 grid-item-animate ${itemsVisible ? 'grid-item-visible' : ''} flex flex-col`}
                    style={{ /* 移除了边框样式 */ }}
                >
                    <DragHandle />
                    <div
                        className="flex-1 w-full h-full min-h-0 overflow-auto no-scrollbar"
                        style={{
                            scrollbarWidth: 'none', /* Firefox */
                            msOverflowStyle: 'none', /* IE 和 Edge */
                        }}
                    >
                        <style>
                            {`
                .no-scrollbar::-webkit-scrollbar {
                  display: none;
                }
              `}
                        </style>
                        <DevicesList />
                    </div>
                </div>

                {/* 设备详情页面容器 */}
                <div
                    key="emptyContainer1"
                    className={`bg-white rounded-lg p-[4px] shadow-sm relative group hover:shadow-lg transition-shadow duration-200 grid-item-animate ${itemsVisible ? 'grid-item-visible' : ''} flex flex-col`}
                >
                    <DragHandle />
                    <div
                        className="flex-1 w-full h-full min-h-0 overflow-auto no-scrollbar"
                        style={{
                            scrollbarWidth: 'none', /* Firefox */
                            msOverflowStyle: 'none', /* IE 和 Edge */
                        }}
                    >
                        <style>
                            {`
                .no-scrollbar::-webkit-scrollbar {
                  display: none;
                }
              `}
                        </style>
                        <DeviceInfo />
                    </div>
                </div>

                {/* LmhSvg组件，取代了空容器2 */}
                <div
                    key="lmhSvg"
                    className={`bg-white rounded-lg p-[4px] shadow-sm relative group hover:shadow-lg transition-shadow duration-200 grid-item-animate ${itemsVisible ? 'grid-item-visible' : ''} flex flex-col`}
                >
                    <DragHandle />
                    <div className="flex-1 w-full h-full min-h-0 overflow-hidden">
                        <Lmh />
                    </div>
                </div>

                {/* PixiJS 版本泵房工况图 */}
                <div
                    key="lmh2Svg"
                    className={`bg-white rounded-lg p-[4px] shadow-sm relative group hover:shadow-lg transition-shadow duration-200 grid-item-animate ${itemsVisible ? 'grid-item-visible' : ''} flex flex-col`}
                >
                    <DragHandle />
                    <div className="flex-1 w-full h-full min-h-0 overflow-hidden">
                        <Lmh2 />
                    </div>
                </div>

                {/* 流量计组件 - 根据状态决定是否渲染 */}
                {showFlowmeter && (
                    <div
                        key="emptyContainer3"
                        className={`bg-white rounded-lg p-[4px] shadow-sm relative group hover:shadow-lg transition-shadow duration-200 grid-item-animate ${itemsVisible ? 'grid-item-visible' : ''} flex flex-col`}
                    >
                        <DragHandle />
                        <div
                            className="flex-1 w-full h-full min-h-0 overflow-hidden no-scrollbar"
                            style={{
                                scrollbarWidth: 'none', /* Firefox */
                                msOverflowStyle: 'none', /* IE 和 Edge */
                            }}
                        >
                            <style>
                                {`
                  .no-scrollbar::-webkit-scrollbar {
                    display: none;
                  }
                `}
                            </style>
                            <Flowmeter onClose={() => setShowFlowmeter(false)} />
                        </div>
                    </div>
                )}

                {/* SocketConsole组件 */}
                <div
                    key="socketConsole"
                    className={`bg-white rounded-lg p-[4px] shadow-sm relative group hover:shadow-lg transition-shadow duration-200 grid-item-animate ${itemsVisible ? 'grid-item-visible' : ''} flex flex-col`}
                    style={{ /* 移除了边框样式 */ }}
                >
                    <DragHandle />
                    <div
                        className="flex-1 w-full h-full min-h-0 overflow-auto no-scrollbar"
                        style={{
                            scrollbarWidth: 'none', /* Firefox */
                            msOverflowStyle: 'none', /* IE 和 Edge */
                        }}
                    >
                        <style>
                            {`
                .no-scrollbar::-webkit-scrollbar {
                  display: none;
                }
              `}
                        </style>
                        <SocketConsole />
                    </div>
                </div>

                        {/* 渲染独立设备容器 */}
        {independentDevices.map((device) => (
          <div
            key={device.gridLayoutItem.i}
            className="dropped-grid-item"
          >
            <IndependentDeviceContainer
              deviceData={device}
              onRemove={() => removeDroppedDevice(device.id)}
              itemsVisible={itemsVisible}
            />
          </div>
        ))}

            </ResponsiveGridLayout>
        </div>
    );
};

export default ResponsiveGridExample;