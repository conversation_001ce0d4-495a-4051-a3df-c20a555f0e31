import React, { useState, useEffect } from 'react';
import FlowMeterDevice from '../DevicesList/components/FlowMeterDevice';
import PressureGaugeDevice from '../DevicesList/components/PressureGaugeDevice';

/**
 * 放置的设备容器组件
 * @param {Object} props
 * @param {Object} props.deviceData - 设备数据
 * @param {Function} props.onRemove - 移除设备的回调函数
 * @returns {JSX.Element}
 */
const DroppedDeviceContainer = ({ deviceData, onRemove }) => {
    const [isVisible, setIsVisible] = useState(false);

    // 组件挂载时添加进场动画
    useEffect(() => {
        const timer = setTimeout(() => {
            setIsVisible(true);
        }, 100);
        return () => clearTimeout(timer);
    }, []);

    // 根据设备类型渲染对应的组件
    const renderDeviceByType = (item) => {
        switch (item.type) {
            case 'pressureGauge':
                return <PressureGaugeDevice data={item} />;
            case 'flowMeter':
            default:
                return <FlowMeterDevice data={item} />;
        }
    };

    const handleRemove = () => {
        // 添加移除动画
        setIsVisible(false);
        setTimeout(() => {
            if (onRemove) {
                onRemove(deviceData.id);
            }
        }, 300);
    };

    return (
        <div 
            className={`dropped-device-container relative bg-white rounded-lg p-2 shadow-lg border-2 transition-all duration-300 ${
                isVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-95'
            }`}
            style={{ 
                borderColor: 'rgba(90,170,180,0.6)',
                minHeight: '100px',
                minWidth: '120px'
            }}
        >
            {/* 移除按钮 */}
            <button
                onClick={handleRemove}
                className="absolute top-[-6px] right-[-6px] w-[20px] h-[20px] bg-red-500 text-white rounded-full text-[12px] flex items-center justify-center hover:bg-red-600 transition-colors z-10 shadow-md"
                title="移除设备"
            >
                ×
            </button>

            {/* 设备标识标签 */}
            <div className="absolute top-[-8px] left-[8px] bg-blue-500 text-white text-[10px] px-[2px] py-[1px] rounded-full shadow-sm">
                已拖入
            </div>

            {/* 设备内容区域 */}
            <div className="w-full h-full min-h-[80px] flex items-center justify-center">
                {renderDeviceByType(deviceData)}
            </div>

            {/* 拖入时间戳 */}
            <div className="absolute bottom-[-6px] left-[50%] transform -translate-x-[50%] bg-gray-600 text-white text-[8px] px-[2px] py-[1px] rounded-full whitespace-nowrap">
                {new Date(deviceData.droppedAt).toLocaleTimeString()}
            </div>
        </div>
    );
};

export default DroppedDeviceContainer; 