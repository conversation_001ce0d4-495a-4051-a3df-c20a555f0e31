import React from 'react';
import { useDragLayer } from 'react-dnd';
import { DRAG_TYPES } from '../../../hooks/useDragContext.jsx';
import FlowMeterDevice from '../DevicesList/components/FlowMeterDevice';
import PressureGaugeDevice from '../DevicesList/components/PressureGaugeDevice';

/**
 * 拖拽预览层组件 - GPU加速优化版本
 * @returns {JSX.Element}
 */
const DragPreviewLayer = () => {
    const {
        isDragging,
        item,
        initialOffset,
        currentOffset,
    } = useDragLayer((monitor) => ({
        item: monitor.getItem(),
        itemType: monitor.getItemType(),
        initialOffset: monitor.getInitialSourceClientOffset(),
        currentOffset: monitor.getSourceClientOffset(),
        isDragging: monitor.isDragging(),
    }));

    // 根据设备类型渲染对应的组件
    const renderDeviceByType = (deviceData) => {
        if (!deviceData) return null;
        
        switch (deviceData.type) {
            case 'pressureGauge':
                return <PressureGaugeDevice data={deviceData} />;
            case 'flowMeter':
            default:
                return <FlowMeterDevice data={deviceData} />;
        }
    };

    if (!isDragging || !currentOffset || !item || item.type !== DRAG_TYPES.DEVICE) {
        return null;
    }

    const { x, y } = currentOffset;

    // 🚀 性能优化：使用GPU加速样式 - 修复版本
    const layerStyles = {
        position: 'fixed',
        pointerEvents: 'none',
        zIndex: 100,
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        // GPU加速优化
        willChange: 'transform',
        transform: 'translateZ(0)', // 强制启用硬件加速
    };

    // 🚀 关键优化：使用transform替代left/top，避免重绘 - 修复版本
    const itemStyles = {
        position: 'absolute',
        top: 0,
        left: 0,
        // 使用transform进行定位，启用GPU加速
        transform: `translate3d(${x - 50}px, ${y - 40}px, 0) rotate(3deg) scale(0.9)`,
        opacity: 0.8,
        // 优化过渡动画，只使用transform
        transition: 'transform 0.1s ease-out, opacity 0.1s ease-out',
        // GPU加速优化
        willChange: 'transform, opacity',
        backfaceVisibility: 'hidden', // 避免背面渲染
        perspective: '1000px', // 启用3D上下文
    };

    return (
        <div style={layerStyles}>
            <div style={itemStyles}>
                <div className="bg-white rounded-lg p-[4px] shadow-2xl border-2 border-blue-400 animate-pulse transform-gpu">
                    <div className="w-[100px] h-[80px] flex items-center justify-center">
                        {renderDeviceByType(item.deviceData)}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default React.memo(DragPreviewLayer); 