import React, { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import { Stage, Layer, Line, Rect, Circle, Path, Group, Text } from 'react-konva';
import { gsap } from 'gsap';
import NumberFlow from '@number-flow/react';

// 设备单元配置 (移至组件外部以避免重复创建)
const devices = [
    { x: 502, y: 115, topBar: '#cccccc', bottomBar: '#d2d1da' },
    { x: 608, y: 115, topBar: '#d2d1da', bottomBar: '#d2d1da' },
    { x: 718, y: 115, topBar: '#d2d1da', bottomBar: '#d2d1da' },
    { x: 830.75, y: 115, topBar: '#d2d1da', bottomBar: '#d2d1da' },
    { x: 936.75, y: 115, topBar: '#d2d1da', bottomBar: '#d2d1da' },
    { x: 1046.75, y: 115, topBar: '#d2d1da', bottomBar: '#d2d1da' }
];

const Lmh = () => {
    const containerRef = useRef(null);
    const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
    const [devicePositions, setDevicePositions] = useState([]); // 设备风扇位置状态
    const fanRefs = useRef([]); // 存储风扇动画引用
    const fanDOMRefs = useRef([]); // 存储风扇DOM元素的引用
    const [dashOffset, setDashOffset] = useState(0); // 虚线偏移量状态
    const konvaGroupRef = useRef(null);
    
    // 添加标题栏相关状态
    const [currentTime, setCurrentTime] = useState(new Date());
    const titleRef = useRef(null);
    const titleTextRef = useRef(null);
    const titleRefs = useRef([]); // 引用所有标题元素
    const [titleWidths, setTitleWidths] = useState({}); // 存储标题宽度
    const [isAnimating, setIsAnimating] = useState(false); // 动画锁定状态
    const [viewMode, setViewMode] = useState('2D'); // 视图模式状态
    const [isCanvasVisible, setIsCanvasVisible] = useState(false); // 画布动画状态
    const [isDataFlowing, setIsDataFlowing] = useState(false); // 数据流动状态
    
    // 🎬 新增：视图按钮闪烁动画状态
    const [buttonAnimStates, setButtonAnimStates] = useState({
        '2D': { isVisible: false },
        '2.5D': { isVisible: false },
        '3D': { isVisible: false }
    });
    const buttonRefs = useRef({
        '2D': null,
        '2.5D': null,
        '3D': null
    }); // 按钮DOM引用
    
    // 新增：流量与压力数据状态
    const [flowMetrics, setFlowMetrics] = useState({
        inletFlow: 0,
        inletPressure: 0,
        outletFlow: 0,
        outletPressure: 0,
        inletTotalFlow: 0,
        outletTotalFlow: 0,
    });
    
    // 注释：数据状态管理已移至NumberFlowOverlay组件中
    
    // 📊 标题数据 - 可配置的标题列表
    const [titleList, setTitleList] = useState([
        { 
            id: 'lmh-001', 
            name: '老马河泵房工况组态图', 
            isActive: true, // 当前激活的标题
            opacity: 1
        },
        { 
            id: 'test-001', 
            name: '测试泵房组态图', 
            isActive: false,
            opacity: 0.9
        },
        { 
            id: 'hospital-001', 
            name: '人民医院泵房工况组态图', 
            isActive: false,
            opacity: 0.8
        },
        { 
            id: 'rural-001', 
            name: '乡村供水一号泵房', 
            isActive: false,
            opacity: 0.7
        }
    ]);
    
    // 实时时间更新效果
    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentTime(new Date());
        }, 5000); // 每5秒更新一次时间
        
        return () => clearInterval(timer);
    }, []);

    // 新增：模拟数据更新
    useEffect(() => {
        if (isDataFlowing) {
            // 首先设置一个真实的初始值，NumberFlow会从0滚动到这个值
            setFlowMetrics({
                inletFlow: parseFloat((120 + Math.random() * 10).toFixed(2)),
                inletPressure: parseFloat((0.5 + Math.random() * 0.1).toFixed(2)),
                outletFlow: parseFloat((105 + Math.random() * 10).toFixed(2)),
                outletPressure: parseFloat((0.85 + Math.random() * 0.1).toFixed(2)),
                inletTotalFlow: 1234567.8,
                outletTotalFlow: 1102102.1,
            });

            // 然后，启动定时器，持续更新数据
            const interval = setInterval(() => {
                setFlowMetrics(prevMetrics => ({
                    inletFlow: parseFloat((120 + Math.random() * 10).toFixed(2)),
                    inletPressure: parseFloat((0.5 + Math.random() * 0.1).toFixed(2)),
                    outletFlow: parseFloat((105 + Math.random() * 10).toFixed(2)),
                    outletPressure: parseFloat((0.85 + Math.random() * 0.1).toFixed(2)),
                    inletTotalFlow: prevMetrics.inletTotalFlow + parseFloat(((120 + Math.random() * 10) / 1800).toFixed(2)), // 模拟累加
                    outletTotalFlow: prevMetrics.outletTotalFlow + parseFloat(((105 + Math.random() * 10) / 1800).toFixed(2)), // 模拟累加
                }));
            }, 2000);
            return () => clearInterval(interval);
        }
    }, [isDataFlowing]);

    // 注释：数据生成函数已移至NumberFlowOverlay组件中
    
    // 🎬 按钮入场动画 - 随机延迟启动
    useEffect(() => {
        const buttons = ['2D', '2.5D', '3D'];
        const timers = [];

        buttons.forEach((buttonKey) => {
            // 生成1到1.5秒的随机延迟
            const randomDelay = 1 + Math.random() * 0.5;

            const timer = setTimeout(() => {
                // 更新状态来触发动画
                setButtonAnimStates(prev => ({ ...prev, [buttonKey]: { isVisible: true } }));
            }, randomDelay * 1000);

            timers.push(timer);
        });

        // 清理所有定时器
        return () => {
            timers.forEach(timer => clearTimeout(timer));
        };
    }, []); // 只在组件挂载时执行一次

    // Konva 画布入场动画
    useEffect(() => {
        const timer = setTimeout(() => {
            setIsCanvasVisible(true);
        }, 3000);
        return () => clearTimeout(timer);
    }, []);

    // 在画布动画结束后，开始数据流动
    useEffect(() => {
        if (isCanvasVisible) {
            const dataDelay = setTimeout(() => {
                setIsDataFlowing(true);
            }, 2500); // 画布动画0.5s + 等待2s
            return () => clearTimeout(dataDelay);
        }
    }, [isCanvasVisible]);

    // 监听容器尺寸变化
    useEffect(() => {
        if (!containerRef.current) return;

        const updateDimensions = () => {
            const containerRect = containerRef.current.getBoundingClientRect();
            const width = containerRect.width || 400;
            const height = containerRect.height || 300;
            setDimensions({ width, height });
        };

        // 初始化尺寸
        updateDimensions();

        // 监听窗口尺寸变化
        window.addEventListener('resize', updateDimensions);
        
        // 使用 ResizeObserver 监听容器尺寸变化
        const resizeObserver = new ResizeObserver(updateDimensions);
        resizeObserver.observe(containerRef.current);

        return () => {
            window.removeEventListener('resize', updateDimensions);
            resizeObserver.disconnect();
            
            // 清理风扇动画
            fanRefs.current.forEach(animation => {
                if (animation) {
                    animation.kill();
                }
            });
            fanRefs.current = [];
        };
    }, []);

    // 计算缩放和位置 (使用 useMemo 优化)
    const { scale, offsetX, offsetY } = useMemo(() => {
        if (!dimensions.width || !dimensions.height) return { scale: 1, offsetX: 0, offsetY: 0 };
        
        const originalWidth = 1633;
        const originalHeight = 329;
        
        const scaleX = dimensions.width / originalWidth;
        const scaleY = dimensions.height / originalHeight;
        const scaleFactor = Math.min(scaleX, scaleY);
        
        const calculatedOffsetX = (dimensions.width - originalWidth * scaleFactor) / 2;
        const calculatedOffsetY = (dimensions.height - originalHeight * scaleFactor) / 2;
        
        return { scale: scaleFactor, offsetX: calculatedOffsetX, offsetY: calculatedOffsetY };
    }, [dimensions.width, dimensions.height]);

    // 设备单元配置已移至组件外部

    // 更新设备位置计算
    useEffect(() => {
        if (scale > 0) {
            const calculatedPositions = devices.map((device, index) => ({
                id: `fan-${index}`,
                x: (device.x + 35) * scale + offsetX, // 设备中心X + 偏移
                y: (device.y + 35) * scale + offsetY, // 设备中心Y + 偏移
                size: Math.max(20, Math.floor(24 * scale)), // 根据缩放调整大小，最小20px
                speed: 'normal' // 统一设置为normal速度
            }));

            setDevicePositions(calculatedPositions);
        }
    }, [scale, offsetX, offsetY]);

    // 风扇旋转动画 - 改为由useEffect触发
    const registerFanRef = useCallback((fanRef, index) => {
        // 这个函数只负责注册DOM元素的引用
        if (fanRef && !fanDOMRefs.current[index]) {
            fanDOMRefs.current[index] = fanRef;
        }
    }, []); // fanDOMRefs.current 是一个 ref，其变化不会触发此 useCallback 重新创建
    
    // 当数据开始流动时，触发风扇动画
    useEffect(() => {
        if (isDataFlowing) {
            fanDOMRefs.current.forEach((fanRef, index) => {
                // 根据反馈，只让1号和3号风扇旋转 (索引为 0 和 2)
                if (index !== 0 && index !== 2) {
                    return;
                }

                if (fanRef && !fanRefs.current[index]) {
                    // 使用时间线实现"延迟-缓动-循环"效果
                    const tl = gsap.timeline(); // 移除初始延迟

                    // 阶段一：缓慢启动
                    tl.fromTo(fanRef,
                        { rotation: 0 },
                        {
                            rotation: 360,
                            duration: 2, // 2秒完成第一次旋转
                            ease: 'power1.in',
                        }
                    );

                    // 阶段二：匀速循环
                    tl.to(fanRef, {
                        rotation: '+=360', // 相对值旋转
                        duration: 1.0, // 正常速度
                        ease: 'none',
                        repeat: -1,
                    });

                    fanRefs.current[index] = tl; // Store the timeline to kill it later
                }
            });
        }
    }, [isDataFlowing]);

    // 测量标题宽度以实现层叠效果
    useEffect(() => {
        const newWidths = {};
        let allRefsAvailable = true;
        titleList.forEach((_, index) => {
            if (!titleRefs.current[index]) {
                allRefsAvailable = false;
            }
        });

        if (allRefsAvailable) {
            titleRefs.current.forEach((el, i) => {
                if (el) {
                    newWidths[i] = el.offsetWidth;
                }
            });
            // 避免无限循环 - 只有当宽度真正发生变化时才更新
            const widthsChanged = JSON.stringify(newWidths) !== JSON.stringify(titleWidths);
            if (widthsChanged) {
                setTitleWidths(newWidths);
                
                // 使用 requestAnimationFrame 确保在DOM更新后设置位置
                requestAnimationFrame(() => {
                    // 直接设置层叠位置，无开场动画
                    titleRefs.current.forEach((el, index) => {
                        if (el) {
                            // 直接设置层叠位置
                            if (index > 0) {
                                const selfWidth = newWidths[index];
                                if (selfWidth) {
                                    gsap.set(el, {
                                        marginLeft: `-${selfWidth - 35}px`, // 统一留出35px的宽度
                                        opacity: 1,
                                        x: 0
                                    });
                                }
                            } else {
                                gsap.set(el, { 
                                    marginLeft: 0,
                                    opacity: 1,
                                    x: 0
                                });
                            }
                        }
                    });
                });
            }
        }
    }, [titleList, titleWidths]);

    // 选中标题的处理函数
    const handleTitleClick = useCallback((clickedId) => {
        const clickedIndex = titleList.findIndex(t => t.id === clickedId);
        // 如果点击的是第一个，或者宽度还没计算好，或者正在动画中，则不执行
        if (clickedIndex === 0 || !titleRefs.current.length || Object.keys(titleWidths).length < titleList.length || isAnimating) return;

        // 设置动画锁定
        setIsAnimating(true);

        // 使用更简单可靠的交换动画方式
        const clickedEl = titleRefs.current[clickedIndex];
        const firstEl = titleRefs.current[0];
        
        if (!clickedEl || !firstEl) {
            setIsAnimating(false);
            return;
        }

        // 1. 先简单淡出所有元素
        const tl = gsap.timeline({
            onComplete: () => {
                // 2. 更新React状态
                const newTitleList = [...titleList];
                const [clickedItem] = newTitleList.splice(clickedIndex, 1);
                newTitleList.unshift(clickedItem);

                const finalList = newTitleList.map((item, index) => ({
                    ...item,
                    isActive: index === 0,
                }));

                setTitleList(finalList);

                // 3. 状态更新后，重新设置正确的位置并淡入
                gsap.delayedCall(0.05, () => {
                    // 清除所有GSAP样式但保留基本样式
                    titleRefs.current.forEach(el => {
                        if (el) {
                            gsap.set(el, { clearProps: 'marginLeft,opacity' });
                        }
                    });
                    
                    // 等DOM更新后再设置位置
                    requestAnimationFrame(() => {
                        // 重新应用层叠效果
                        titleRefs.current.forEach((el, index) => {
                            if (el) {
                                const currentWidth = el.offsetWidth;
                                if (index === 0) {
                                    gsap.set(el, { marginLeft: 0, opacity: 0 });
                                } else if (currentWidth) {
                                    gsap.set(el, { 
                                        marginLeft: `-${currentWidth - 35}px`,
                                        opacity: 0
                                    });
                                }
                            }
                        });
                        
                        // 淡入动画
                        gsap.to(titleRefs.current, {
                            opacity: 1, 
                            duration: 0.3,
                            ease: 'power2.out',
                            stagger: 0.05,
                            onComplete: () => {
                                // 动画完成后解除锁定
                                setIsAnimating(false);
                            }
                        });
                    });
                }); // 使用GSAP delayedCall确保React状态更新完成
            }
        });

        // 淡出动画
        tl.to(titleRefs.current, {
            opacity: 0,
            duration: 0.2,
            ease: 'power2.in',
            stagger: 0.02
        });
    }, [titleList, titleWidths, isAnimating, titleRefs]); // 依赖项根据函数内部使用的状态和props添加
    
    // GSAP 动画处理标题展开和层叠
    const handleTitleMouseEnter = useCallback(() => {
        if (!titleRefs.current || isAnimating) return; // 添加动画锁定检查
        // 动画：所有标题展开，并保留10px间距
        titleRefs.current.forEach(el => {
            gsap.to(el, {
                marginLeft: '10px',
                duration: 0.4,
                ease: 'power2.out',
                overwrite: 'auto',
            });
        });
    }, [isAnimating, titleRefs]); // 依赖项

    const handleTitleMouseLeave = useCallback(() => {
        if (!titleRefs.current || !titleWidths || isAnimating) return; // 添加动画锁定检查
        // 动画：恢复层叠状态
        titleRefs.current.forEach((el, index) => {
            if (el) {
                if (index > 0) {
                    const selfWidth = titleWidths[index];
                    if (selfWidth) {
                        gsap.to(el, {
                            marginLeft: `-${selfWidth - 35}px`, // 统一留出35px的宽度
                            duration: 0.4,
                            ease: 'power2.inOut',
                            overwrite: 'auto',
                        });
                    }
                } else {
                    // 确保第一个元素归位到0
                    gsap.to(el, {
                        marginLeft: 0,
                        duration: 0.4,
                        ease: 'power2.inOut',
                        overwrite: 'auto',
                    });
                }
            }
        });
    }, [isAnimating, titleWidths, titleRefs]); // 依赖项

    // 虚线流动动画
    useEffect(() => {
        if (isDataFlowing) {
            const dashTween = { value: 0 };
            const tl = gsap.timeline({
                // 移除初始延迟，由isDataFlowing控制
                onUpdate: () => {
                    setDashOffset(dashTween.value);
                }
            });

            // 1. 缓慢启动阶段
            tl.to(dashTween, {
                value: -23,
                duration: 2, // 用2秒完成第一次流动
                ease: 'power1.in',
            });

            // 2. 匀速循环阶段
            tl.to(dashTween, {
                value: -46, // 每次前进-23
                duration: 0.5, // 正常速度
                ease: 'none',
                repeat: -1,
                onRepeat: () => {
                    // 每次重复时，将value重置，避免无限增大
                    gsap.set(dashTween, { value: -23 });
                }
            });

            return () => {
                tl.kill();
            };
        }
    }, [isDataFlowing]);

    return (
        <div 
            ref={containerRef} 
            className="w-full h-full bg-gray-50 rounded-lg overflow-hidden relative flex flex-col"
            style={{ width: '100%', height: '100%' }}
        >
            {/* 💫 按钮闪烁入场动画样式 - Keyframes版本 */}
            <style dangerouslySetInnerHTML={{
                __html: `
                /* 设备容器进场动画 - 急促闪烁效果 */
                @keyframes buttonEntranceFlash {
                    0% { opacity: 0; }
                    15% { opacity: 0.8; }
                    30% { opacity: 0.2; }
                    45% { opacity: 0.7; }
                    60% { opacity: 0.1; }
                    75% { opacity: 1; }
                    90% { opacity: 0.2; }
                    100% { opacity: 1; }
                }

                .button-animate-flash {
                    opacity: 0; /* 初始状态 */
                    animation-name: buttonEntranceFlash;
                    animation-duration: 0.3s; /* 动画时长, 调快一点 */
                    animation-timing-function: steps(7, end); /* 7次阶梯跳跃，实现频闪 */
                    animation-fill-mode: forwards; /* 动画结束后保持最后状态 */
                    pointer-events: auto; /* 动画开始后即可交互 */
                }
                `
            }} />
            
            {/* 标题栏容器 - 高度30px，宽度100%，左侧padding 20px */}
            <div className="h-[30px] w-full flex items-center justify-between relative flex-shrink-0">
                {/* 标题容器 - 横向布局 */}
                <div
                    ref={titleRef}
                    className="flex items-center ml-[25px] relative overflow-hidden"
                    style={{ opacity: 0.9 }}
                    onMouseEnter={handleTitleMouseEnter}
                    onMouseLeave={handleTitleMouseLeave}
                >
                    {/* 左侧遮罩层 - 防止标题超出显示 */}
                    <div 
                        className="absolute left-[-25px] top-0 bottom-0 w-[30px] bg-gradient-to-r from-gray-50 via-gray-50 to-transparent z-[1000] pointer-events-none"
                        style={{ zIndex: 1000 }}
                    ></div>
                    
                    {titleList.map((title, index) => {
                        const style = {
                            zIndex: titleList.length - index, // 后面的标题在下面
                            boxShadow: '20px 0 25px 0px rgba(0, 0, 0, 1.0)', // 深邃渐变阴影
                        };

                        return (
                            <div
                                key={title.id}
                                ref={el => { titleRefs.current[index] = el; }}
                                className={`${title.isActive ? 'bg-[#26774D]' : 'bg-[#333336]'} rounded-[3px] px-[5px] h-full flex items-center cursor-pointer transition-colors duration-300 relative ${!title.isActive && 'hover:bg-[#444247]'}`}
                                style={style}
                                onClick={() => handleTitleClick(title.id)}
                            >
                                <span
                                    ref={index === 0 ? titleTextRef : null} // 仅将引用附加到第一个标题
                                    className={`text-[14px] font-['DingTalkJinBuTi',sans-serif] ${title.isActive ? 'text-white' : 'text-white'} whitespace-nowrap select-none`}
                                    style={{ opacity: title.isActive ? 1 : title.opacity }} // 激活状态时opacity为1，其他状态使用数据中的透明度
                                >
                                    {title.name}
                                </span>
                            </div>
                        );
                    })}
                </div>
                
                {/* 右上角状态指示器和时间 */}
                <div className="flex items-center space-x-[10px] mr-[0]">
                    {/* 运行状态指示器 */}
                    <div className="flex items-center space-x-[5px]">
                        <div className="w-[8px] h-[8px] rounded-full bg-[#23C26D] animate-pulse"></div>
                        <span className="text-[10px] text-[#23C26D] font-['DingTalkJinBuTi']">运行中</span>
                    </div>
                    
                    {/* 关闭按钮 */}
                    <div
                        className="close-btn w-[16px] h-[16px] flex items-center justify-center hover:opacity-70 transition-opacity duration-200 cursor-pointer bg-[#333135]"
                        title="关闭"
                    >
                        <svg 
                            xmlns="http://www.w3.org/2000/svg" 
                            width="12" 
                            height="12" 
                            viewBox="0 0 24 24" 
                            fill="none" 
                            stroke="#B3B2B6"
                            strokeWidth="2.5" 
                            strokeLinecap="round" 
                            strokeLinejoin="round"
                        >
                            <path d="M18 6L6 18"/>
                            <path d="M6 6l12 12"/>
                        </svg>
                    </div>
                </div>
                
                {/* 顶部装饰线 */}
                <div 
                    ref={el => {
                        if (el && !el.dataset.decorated) {
                            // 添加标记避免重复执行
                            el.dataset.decorated = 'true';
                            
                            // 设置初始状态：使用scaleX实现从中心扩散
                            gsap.set(el, {
                                scaleX: 0,
                                transformOrigin: 'center',
                                opacity: 1,
                                display: 'block',
                                visibility: 'visible'
                            });
                            
                            // 延迟1.5秒后开始横线动画
                            const decorationTL = gsap.timeline({ 
                                delay: 1.5,
                                onComplete: () => {
                                    el.style.opacity = '0.8';
                                    el.style.transform = 'scaleX(1)';
                                    el.style.visibility = 'visible';
                                    el.style.display = 'block';
                                }
                            });
                            
                            // 从中心向两边扩散
                            decorationTL.to(el, {
                                scaleX: 1,
                                duration: 1.2,
                                ease: 'power2.out'
                            })
                            // 频闪效果 - 仅在扩散期间
                            .to(el, 
                                { 
                                    opacity: 1, 
                                    duration: 0.06,
                                    ease: 'steps(1)',
                                    repeat: 4,
                                    yoyo: true
                                }, '<')
                            // 最后确保停在稳定状态
                            .to(el, {
                                opacity: 0.8,
                                duration: 0.2,
                                ease: 'power2.out'
                            });
                        }
                    }}
                    className="absolute bottom-[0px] h-[2px] bg-gradient-to-r from-transparent via-[#2894B7] to-transparent"
                    style={{ width: '100%', opacity: 0.8 }}
                ></div>
            </div>
            
            {/* 主要内容区域 - 包含Konva画布 */}
            <div className="flex-1 overflow-hidden relative">
                {/* 右上角操作按钮组 */}
                <div className="absolute top-[10px] right-[0] z-20">
                    <div className="flex space-x-[8px]">
                        {/* 2D视图按钮 */}
                        <div 
                            ref={el => buttonRefs.current['2D'] = el}
                            className={`px-[5px] rounded-[4px] flex items-center justify-center cursor-pointer ${
                                buttonAnimStates['2D'].isVisible ? 'button-animate-flash' : 'opacity-0'
                            } ${
                                viewMode === '2D' 
                                    ? 'bg-[#23A760] shadow-[0_0_15px_rgba(35,167,96,0.5)]' 
                                    : 'bg-[#333135] hover:bg-[#444247]'
                            }`}
                            title="2D平面视图"
                            onClick={() => setViewMode('2D')}
                        >
                            <span className={`text-[12px] font-medium ${
                                viewMode === '2D' ? 'text-black' : 'text-white'
                            }`}>2D</span>
                        </div>
                        
                        {/* 2.5D视图按钮 */}
                        <div 
                            ref={el => buttonRefs.current['2.5D'] = el}
                            className={`px-[5px] rounded-[4px] flex items-center justify-center cursor-pointer ${
                                buttonAnimStates['2.5D'].isVisible ? 'button-animate-flash' : 'opacity-0'
                            } ${
                                viewMode === '2.5D' 
                                    ? 'bg-[#23A760] shadow-[0_0_15px_rgba(35,167,96,0.5)]' 
                                    : 'bg-[#333135] hover:bg-[#444247]'
                            }`}
                            title="斜45度伪3D视图"
                            onClick={() => setViewMode('2.5D')}
                        >
                            <span className={`text-[12px] font-medium ${
                                viewMode === '2.5D' ? 'text-black' : 'text-white'
                            }`}>2.5D</span>
                        </div>
                        
                        {/* 3D视图按钮 */}
                        <div 
                            ref={el => buttonRefs.current['3D'] = el}
                            className={`px-[5px] rounded-[4px] flex items-center justify-center cursor-pointer ${
                                buttonAnimStates['3D'].isVisible ? 'button-animate-flash' : 'opacity-0'
                            } ${
                                viewMode === '3D' 
                                    ? 'bg-[#23A760] shadow-[0_0_15px_rgba(35,167,96,0.5)]' 
                                    : 'bg-[#333135] hover:bg-[#444247]'
                            }`}
                            title="纯3D视图"
                            onClick={() => setViewMode('3D')}
                        >
                            <span className={`text-[12px] font-medium ${
                                viewMode === '3D' ? 'text-black' : 'text-white'
                            }`}>3D</span>
                        </div>
                    </div>
                </div>
                
                {/* React-Konva 画布 */}
                <div className={`w-full h-full flex items-center justify-center relative ${isCanvasVisible ? 'button-animate-flash' : 'opacity-0'}`}>
                    {dimensions.width > 0 && dimensions.height > 0 && (
                        <>
                        <Stage width={dimensions.width} height={dimensions.height}>
                        <Layer>
                            {/* 主组 */}
                            <Group ref={konvaGroupRef} x={offsetX} y={offsetY} scaleX={scale} scaleY={scale}>
                                {/* 顶部主管道 */}
                                <Line points={[537, 9, 648, 9]} stroke="#23C26D" strokeWidth={2} dash={[15, 8]} dashOffset={dashOffset} />
                                <Line points={[643, 9, 754, 9]} stroke="#23C26D" strokeWidth={2} dash={[15, 8]} dashOffset={dashOffset} />
                                <Line points={[753, 9, 864, 9]} stroke="#ffffff" strokeWidth={2} />
                                <Line points={[865.75, 9, 976.75, 9]} stroke="#ffffff" strokeWidth={2} />
                                <Line points={[971.75, 9, 1082.75, 9]} stroke="#ffffff" strokeWidth={2} />

                                {/* 底部主管道 */}
                                <Line points={[536.32, 319, 647.32, 319]} stroke="#23C26D" strokeWidth={2} dash={[15, 8]} dashOffset={dashOffset} />
                                <Line points={[642.32, 319, 753.32, 319]} stroke="#23C26D" strokeWidth={2} dash={[15, 8]} dashOffset={dashOffset} />
                                <Line points={[752.32, 319, 863.32, 319]} stroke="#23C26D" strokeWidth={2} dash={[15, 8]} dashOffset={dashOffset} />
                                <Line points={[865.07, 319, 976.07, 319]} stroke="#23C26D" strokeWidth={2} dash={[15, 8]} dashOffset={dashOffset} />
                                <Line points={[971.07, 319, 1082.07, 319]} stroke="#23C26D" strokeWidth={2} dash={[15, 8]} dashOffset={dashOffset} />

                                {/* 竖直管道 */}
                                {[537, 643, 753, 865.75, 971.75, 1081.75].map((x, index) => {
                                    // 根据反馈，为1号和3号泵的竖管添加流动效果 (索引为 0 和 2)
                                    const isActive = index === 0 || index === 2;
                                    return (
                                        <Group key={index}>
                                            <Line
                                                points={[x, 12, x, 316]}
                                                stroke={isActive ? "#23C26D" : "#d2d1da"}
                                                strokeWidth={2}
                                                dash={isActive ? [15, 8] : undefined}
                                                dashOffset={isActive ? dashOffset : 0}
                                            />
                                            <Circle x={x} y={9} radius={3} fill="#d2d1da" stroke="#d2d1da" strokeWidth={2} />
                                            <Circle x={x} y={319} radius={3} fill="#d2d1da" stroke="#d2d1da" strokeWidth={2} />
                                        </Group>
                                    );
                                })}

                                {/* 设备单元 */}
                                {devices.map((device, index) => (
                                    <Group key={index}>
                                        {/* 主体方框 */}
                                        <Rect
                                            x={device.x}
                                            y={device.y}
                                            width={70}
                                            height={70}
                                            cornerRadius={9.8}
                                            fill="#141414"
                                            stroke="#d2d1da"
                                            strokeWidth={1}
                                        />
                                        
                                        {/* 风扇图标 */}
                                        <Group x={device.x + 35} y={device.y + 35}>
                                            <Path
                                                ref={ref => ref && registerFanRef(ref, index)}
                                                data="M961.62478 335.013799c-160.113889-143.047486-351.5679 42.20056-408.662775 107.983786-5.895666-3.723579-12.411929-6.516263-19.238491-8.067754-62.369945-128.153171 239.550237-215.657273 185.868643-310.918831-107.983786-191.454011-327.985234-118.533926-384.459513-61.749349-143.047486 158.562398 42.821156 350.016409 108.294084 408.973074-3.723579 5.895666-6.516263 12.411929-8.378053 18.928192-128.153171 62.369945-215.657273-239.239939-310.918831-185.558345-191.764309 107.983786-118.533926 327.985234-61.749349 384.149215 158.872696 143.047486 350.016409-42.510858 408.973074-107.983786 5.895666 3.723579 12.722228 6.516263 19.23849 8.067755 62.369945 128.153171-239.550237 215.657273-185.868642 310.918831 108.294084 191.454011 327.985234 118.533926 384.459513 61.749349 142.737188-158.562398-42.510858-350.016409-108.604382-408.973074 3.723579-5.895666 6.516263-12.411929 8.378052-18.928192 128.153171-62.369945 215.657273 239.239939 310.918831 185.558344 191.764309-107.983786 118.533926-327.985234 61.749349-384.149215z"
                                                fill="#d2d1da"
                                                scaleX={0.03}
                                                scaleY={0.03}
                                                offsetX={527.5}
                                                offsetY={512}
                                            />
                                        </Group>

                                        {/* 顶部指示条 */}
                                        <Rect
                                            x={device.x + 24.5}
                                            y={device.y - 6}
                                            width={21}
                                            height={6}
                                            cornerRadius={1.44}
                                            fill={device.topBar}
                                        />

                                        {/* 底部指示条 */}
                                        <Rect
                                            x={device.x + 24.5}
                                            y={device.y + 70}
                                            width={21}
                                            height={6}
                                            cornerRadius={1.44}
                                            fill={device.bottomBar}
                                        />

                                        {/* 设备标签背景 */}
                                        <Rect
                                            x={device.x + 15}
                                            y={device.y + 82}
                                            width={40}
                                            height={18}
                                            cornerRadius={2}
                                            fill="#171818"
                                        />

                                        {/* 设备标签文字 */}
                                        <Text
                                            x={device.x + 35}
                                            y={device.y + 86}
                                            text={`${['一', '二', '三', '四', '五', '六'][index]}号泵`}
                                            fontSize={13}
                                            fontFamily="DingTalkJinBuTi, sans-serif"
                                            fill="#ffffff"
                                            align="center"
                                            offsetX={20}
                                        />
                                    </Group>
                                ))}

                                {/* 外部容器 */}
                                <Rect x={14} y={115} width={170} height={70} cornerRadius={9.8} fill="transparent" stroke="#d2d1da" strokeWidth={1} />
                                <Rect x={241.33} y={115} width={170} height={70} cornerRadius={9.8} fill="transparent" stroke="#d2d1da" strokeWidth={1} />
                                <Rect x={1235} y={115.09} width={170} height={70} cornerRadius={9.8} fill="transparent" stroke="#d2d1da" strokeWidth={1} />
                                <Rect x={1462.33} y={115.09} width={170} height={70} cornerRadius={9.8} fill="transparent" stroke="#d2d1da" strokeWidth={1} />

                                {/* 进口流量计容器内的SVG图标 - 调整位置完全在容器内 */}
                                <Group x={44} y={150}>
                                    <Path
                                        data="M505.173333 71.253333a438.186667 438.186667 0 0 0-289.706666 768 30.293333 30.293333 0 0 0 42.666666-2.56 29.866667 29.866667 0 0 0-2.56-42.666666 377.6 377.6 0 1 1 494.08 3.413333 30.293333 30.293333 0 1 0 38.826667 46.506667 437.76 437.76 0 0 0-283.306667-772.693334z"
                                        fill="#23C26D"
                                        scaleX={0.035}
                                        scaleY={0.035}
                                        offsetX={512}
                                        offsetY={512}
                                    />
                                    <Path
                                        data="M494.08 269.653333a18.773333 18.773333 0 0 0 17.92-18.346666V189.44a18.346667 18.346667 0 1 0-36.693333 0v61.866667a18.773333 18.773333 0 0 0 18.773333 18.346666zM401.493333 202.666667a17.493333 17.493333 0 0 0-23.04-10.666667 17.066667 17.066667 0 0 0-10.666666 21.333333l20.48 60.16a18.346667 18.346667 0 0 0 17.066666 11.946667 13.653333 13.653333 0 0 0 5.973334 0 18.346667 18.346667 0 0 0 11.093333-23.466667zM302.933333 256a17.92 17.92 0 1 0-27.733333 23.04l40.106667 47.786667a15.36 15.36 0 0 0 13.226666 5.973333 17.493333 17.493333 0 0 0 12.8-7.253333 18.346667 18.346667 0 0 0 2.133334-26.88z m-40.106666 145.066667a35.84 35.84 0 0 0 8.533333 2.133333 18.773333 18.773333 0 0 0 9.813333-34.133333l-53.333333-33.706667a17.92 17.92 0 0 0-24.32 5.973333 17.493333 17.493333 0 0 0 5.973333 24.32z m-99.84 52.48a17.92 17.92 0 0 0 14.933333 20.48l60.586667 11.093333h3.84a19.626667 19.626667 0 0 0 18.346666-15.786667 18.773333 18.773333 0 0 0-14.933333-20.906666l-60.586667-10.666667a18.773333 18.773333 0 0 0-22.186666 15.786667z m19.626666 132.693333h3.84l60.586667-11.093333a17.92 17.92 0 0 0-5.973333-35.413334l-61.013334 11.093334a17.493333 17.493333 0 0 0-14.506666 20.906666 15.36 15.36 0 0 0 17.066666 14.506667z m85.333334 40.106667L213.333333 657.92a16.64 16.64 0 0 0-5.973333 24.32 19.626667 19.626667 0 0 0 15.786667 9.813333 16.64 16.64 0 0 0 8.533333-2.56l53.76-31.573333a16.64 16.64 0 0 0 5.973333-24.32 16.213333 16.213333 0 0 0-24.32-7.253333z m512 59.733333a19.626667 19.626667 0 0 0 15.786666-9.813333 17.92 17.92 0 0 0-5.973333-24.32l-53.76-31.573334a17.493333 17.493333 0 0 0-24.32 5.973334 17.92 17.92 0 0 0 5.973333 24.32L768 682.666667a10.666667 10.666667 0 0 0 8.533333 3.84z m-20.906667-152.32a17.92 17.92 0 0 0-5.973333 35.413333l57.6 11.093334h3.413333a20.053333 20.053333 0 0 0 18.346667-14.933334 17.92 17.92 0 0 0-14.506667-20.48l-62.293333-11.093333zM810.666667 432.64l-61.013334 11.093333a17.066667 17.066667 0 0 0-14.506666 20.906667 18.346667 18.346667 0 0 0 18.346666 14.506667h3.413334l59.733333-9.813334a17.066667 17.066667 0 0 0 14.506667-20.48 20.48 20.48 0 0 0-21.76-14.933333z m-90.026667-36.266667a18.346667 18.346667 0 0 0 8.533333-2.56l53.333334-31.573333a17.92 17.92 0 1 0-18.346667-30.293333l-53.333333 31.573333a16.64 16.64 0 0 0-5.973334 24.32 20.906667 20.906667 0 0 0 15.786667 8.533333z m-5.973333-148.48a19.2 19.2 0 0 0-25.6 2.56L647.68 298.666667a18.773333 18.773333 0 0 0 2.133333 25.6 24.32 24.32 0 0 0 12.373334 4.693333 18.346667 18.346667 0 0 0 13.226666-5.973333l40.106667-47.36a20.053333 20.053333 0 0 0-2.133333-27.733334z m-130.56 36.693334a18.346667 18.346667 0 0 0 17.066666-12.373334L620.8 213.333333a17.493333 17.493333 0 0 0-11.093333-23.04 17.493333 17.493333 0 0 0-23.04 11.093334l-20.906667 58.026666a18.346667 18.346667 0 0 0 11.093333 23.466667 8.96 8.96 0 0 0 5.973334 1.706667z m37.973333 88.746666a25.173333 25.173333 0 0 0-35.413333 4.693334l-80.213334 105.813333a45.653333 45.653333 0 0 0-36.693333 17.066667 42.666667 42.666667 0 0 0-8.533333 31.573333 42.666667 42.666667 0 0 0 17.066666 29.44 42.666667 42.666667 0 0 0 26.88 8.533333H512a42.666667 42.666667 0 0 0 29.44-17.066666 47.786667 47.786667 0 0 0 7.253333-40.106667l80.213334-105.813333a23.893333 23.893333 0 0 0 4.693333-18.346667 34.56 34.56 0 0 0-10.666667-15.786667z"
                                        fill="#23C26D"
                                        scaleX={0.035}
                                        scaleY={0.035}
                                        offsetX={512}
                                        offsetY={512}
                                    />
                                </Group>

                                {/* 进口压力计容器内的SVG图标 - 压力表图标 */}
                                <Group x={271.33} y={150}>
                                    <Path
                                        data="M512 0a512 512 0 1 1 0 1024A512 512 0 0 1 512 0z m0 59.562667A452.494222 452.494222 0 0 0 59.392 512 452.494222 452.494222 0 0 0 512 964.437333 452.494222 452.494222 0 0 0 964.608 512 452.494222 452.494222 0 0 0 512 59.562667zM512 227.555556l113.777778 398.222222-113.777778 142.222222L398.222222 625.777778l113.777778-398.222222z m0 355.555555a42.666667 42.666667 0 1 0 0 85.333333 42.666667 42.666667 0 0 0 0-85.333333z m362.780444-39.253333l68.039112 5.973333c-1.820444 20.48-5.12 40.846222-9.841778 60.984889l-66.446222-15.701333c3.982222-16.896 6.769778-34.076444 8.248888-51.313778zM149.048889 541.070222c1.422222 17.294222 4.039111 34.474667 7.907555 51.427556l-66.56 15.132444a432.128 432.128 0 0 1-9.386666-61.041778l68.039111-5.518222zM893.952 309.475556c9.671111 18.204444 17.976889 37.034667 24.974222 56.433777l-64.284444 23.096889a366.535111 366.535111 0 0 0-21.048889-47.559111l60.359111-31.971555z m-762.311111-3.128889l60.131555 32.426666a368.355556 368.355556 0 0 0-21.504 47.616l-64.056888-23.552a444.074667 444.074667 0 0 1 25.486222-56.490666z m172.600889-173.511111l32.768 59.904a365.226667 365.226667 0 0 0-43.804445 28.216888l-40.903111-54.613333a433.493333 433.493333 0 0 1 51.939556-33.450667z m411.875555-1.934223c18.318222 9.784889 35.783111 20.764444 52.337778 32.938667l-40.448 55.011556a362.609778 362.609778 0 0 0-44.032-27.761778L716.231111 130.844444z m-171.235555-50.062222l-5.233778 68.096a361.415111 361.415111 0 0 0-51.996444-0.227555l-4.551112-68.152889a429.681778 429.681778 0 0 1 61.781334 0.284444z"
                                        fill="#2894B7"
                                        scaleX={0.032}
                                        scaleY={0.032}
                                        offsetX={512}
                                        offsetY={512}
                                    />
                                </Group>

                                {/* 出口流量计容器内的SVG图标 - 调整位置完全在容器内 */}
                                <Group x={1265} y={150.09}>
                                    <Path
                                        data="M505.173333 71.253333a438.186667 438.186667 0 0 0-289.706666 768 30.293333 30.293333 0 0 0 42.666666-2.56 29.866667 29.866667 0 0 0-2.56-42.666666 377.6 377.6 0 1 1 494.08 3.413333 30.293333 30.293333 0 1 0 38.826667 46.506667 437.76 437.76 0 0 0-283.306667-772.693334z"
                                        fill="#23C26D"
                                        scaleX={0.035}
                                        scaleY={0.035}
                                        offsetX={512}
                                        offsetY={512}
                                    />
                                    <Path
                                        data="M494.08 269.653333a18.773333 18.773333 0 0 0 17.92-18.346666V189.44a18.346667 18.346667 0 1 0-36.693333 0v61.866667a18.773333 18.773333 0 0 0 18.773333 18.346666zM401.493333 202.666667a17.493333 17.493333 0 0 0-23.04-10.666667 17.066667 17.066667 0 0 0-10.666666 21.333333l20.48 60.16a18.346667 18.346667 0 0 0 17.066666 11.946667 13.653333 13.653333 0 0 0 5.973334 0 18.346667 18.346667 0 0 0 11.093333-23.466667zM302.933333 256a17.92 17.92 0 1 0-27.733333 23.04l40.106667 47.786667a15.36 15.36 0 0 0 13.226666 5.973333 17.493333 17.493333 0 0 0 12.8-7.253333 18.346667 18.346667 0 0 0 2.133334-26.88z m-40.106666 145.066667a35.84 35.84 0 0 0 8.533333 2.133333 18.773333 18.773333 0 0 0 9.813333-34.133333l-53.333333-33.706667a17.92 17.92 0 0 0-24.32 5.973333 17.493333 17.493333 0 0 0 5.973333 24.32z m-99.84 52.48a17.92 17.92 0 0 0 14.933333 20.48l60.586667 11.093333h3.84a19.626667 19.626667 0 0 0 18.346666-15.786667 18.773333 18.773333 0 0 0-14.933333-20.906666l-60.586667-10.666667a18.773333 18.773333 0 0 0-22.186666 15.786667z m19.626666 132.693333h3.84l60.586667-11.093333a17.92 17.92 0 0 0-5.973333-35.413334l-61.013334 11.093334a17.493333 17.493333 0 0 0-14.506666 20.906666 15.36 15.36 0 0 0 17.066666 14.506667z m85.333334 40.106667L213.333333 657.92a16.64 16.64 0 0 0-5.973333 24.32 19.626667 19.626667 0 0 0 15.786667 9.813333 16.64 16.64 0 0 0 8.533333-2.56l53.76-31.573333a16.64 16.64 0 0 0 5.973333-24.32 16.213333 16.213333 0 0 0-24.32-7.253333z m512 59.733333a19.626667 19.626667 0 0 0 15.786666-9.813333 17.92 17.92 0 0 0-5.973333-24.32l-53.76-31.573334a17.493333 17.493333 0 0 0-24.32 5.973334 17.92 17.92 0 0 0 5.973333 24.32L768 682.666667a10.666667 10.666667 0 0 0 8.533333 3.84z m-20.906667-152.32a17.92 17.92 0 0 0-5.973333 35.413333l57.6 11.093334h3.413333a20.053333 20.053333 0 0 0 18.346667-14.933334 17.92 17.92 0 0 0-14.506667-20.48l-62.293333-11.093333zM810.666667 432.64l-61.013334 11.093333a17.066667 17.066667 0 0 0-14.506666 20.906667 18.346667 18.346667 0 0 0 18.346666 14.506667h3.413334l59.733333-9.813334a17.066667 17.066667 0 0 0 14.506667-20.48 20.48 20.48 0 0 0-21.76-14.933333z m-90.026667-36.266667a18.346667 18.346667 0 0 0 8.533333-2.56l53.333334-31.573333a17.92 17.92 0 1 0-18.346667-30.293333l-53.333333 31.573333a16.64 16.64 0 0 0-5.973334 24.32 20.906667 20.906667 0 0 0 15.786667 8.533333z m-5.973333-148.48a19.2 19.2 0 0 0-25.6 2.56L647.68 298.666667a18.773333 18.773333 0 0 0 2.133333 25.6 24.32 24.32 0 0 0 12.373334 4.693333 18.346667 18.346667 0 0 0 13.226666-5.973333l40.106667-47.36a20.053333 20.053333 0 0 0-2.133333-27.733334z m-130.56 36.693334a18.346667 18.346667 0 0 0 17.066666-12.373334L620.8 213.333333a17.493333 17.493333 0 0 0-11.093333-23.04 17.493333 17.493333 0 0 0-23.04 11.093334l-20.906667 58.026666a18.346667 18.346667 0 0 0 11.093333 23.466667 8.96 8.96 0 0 0 5.973334 1.706667z m37.973333 88.746666a25.173333 25.173333 0 0 0-35.413333 4.693334l-80.213334 105.813333a45.653333 45.653333 0 0 0-36.693333 17.066667 42.666667 42.666667 0 0 0-8.533333 31.573333 42.666667 42.666667 0 0 0 17.066666 29.44 42.666667 42.666667 0 0 0 26.88 8.533333H512a42.666667 42.666667 0 0 0 29.44-17.066666 47.786667 47.786667 0 0 0 7.253333-40.106667l80.213334-105.813333a23.893333 23.893333 0 0 0 4.693333-18.346667 34.56 34.56 0 0 0-10.666667-15.786667z"
                                        fill="#23C26D"
                                        scaleX={0.035}
                                        scaleY={0.035}
                                        offsetX={512}
                                        offsetY={512}
                                    />
                                </Group>

                                {/* 出口压力计容器内的SVG图标 - 压力表图标 */}
                                <Group x={1492.33} y={150.09}>
                                    <Path
                                        data="M512 0a512 512 0 1 1 0 1024A512 512 0 0 1 512 0z m0 59.562667A452.494222 452.494222 0 0 0 59.392 512 452.494222 452.494222 0 0 0 512 964.437333 452.494222 452.494222 0 0 0 964.608 512 452.494222 452.494222 0 0 0 512 59.562667zM512 227.555556l113.777778 398.222222-113.777778 142.222222L398.222222 625.777778l113.777778-398.222222z m0 355.555555a42.666667 42.666667 0 1 0 0 85.333333 42.666667 42.666667 0 0 0 0-85.333333z m362.780444-39.253333l68.039112 5.973333c-1.820444 20.48-5.12 40.846222-9.841778 60.984889l-66.446222-15.701333c3.982222-16.896 6.769778-34.076444 8.248888-51.313778zM149.048889 541.070222c1.422222 17.294222 4.039111 34.474667 7.907555 51.427556l-66.56 15.132444a432.128 432.128 0 0 1-9.386666-61.041778l68.039111-5.518222zM893.952 309.475556c9.671111 18.204444 17.976889 37.034667 24.974222 56.433777l-64.284444 23.096889a366.535111 366.535111 0 0 0-21.048889-47.559111l60.359111-31.971555z m-762.311111-3.128889l60.131555 32.426666a368.355556 368.355556 0 0 0-21.504 47.616l-64.056888-23.552a444.074667 444.074667 0 0 1 25.486222-56.490666z m172.600889-173.511111l32.768 59.904a365.226667 365.226667 0 0 0-43.804445 28.216888l-40.903111-54.613333a433.493333 433.493333 0 0 1 51.939556-33.450667z m411.875555-1.934223c18.318222 9.784889 35.783111 20.764444 52.337778 32.938667l-40.448 55.011556a362.609778 362.609778 0 0 0-44.032-27.761778L716.231111 130.844444z m-171.235555-50.062222l-5.233778 68.096a361.415111 361.415111 0 0 0-51.996444-0.227555l-4.551112-68.152889a429.681778 429.681778 0 0 1 61.781334 0.284444z"
                                        fill="#2894B7"
                                        scaleX={0.032}
                                        scaleY={0.032}
                                        offsetX={512}
                                        offsetY={512}
                                    />
                                </Group>

                                {/* 外部容器标签 */}
                                {/* 进口流量计标签背景 */}
                                {/* 进口流量计文字 */}
                                <Text
                                    x={54}
                                    y={98}
                                    text="进口流量计"
                                    fontSize={13}
                                    fontFamily="DingTalkJinBuTi, sans-serif"
                                    fill="#ffffff"
                                    align="center"
                                    offsetX={40}
                                />

                                {/* 进口压力计文字 */}
                                <Text
                                    x={281.33}
                                    y={98}
                                    text="进口压力计"
                                    fontSize={13}
                                    fontFamily="DingTalkJinBuTi, sans-serif"
                                    fill="#ffffff"
                                    align="center"
                                    offsetX={40}
                                />

                                {/* 出口流量计文字 */}
                                <Text
                                    x={1275}
                                    y={98}
                                    text="出口流量计"
                                    fontSize={13}
                                    fontFamily="DingTalkJinBuTi, sans-serif"
                                    fill="#ffffff"
                                    align="center"
                                    offsetX={40}
                                />

                                {/* 出口压力计文字 */}
                                <Text
                                    x={1502.33}
                                    y={98}
                                    text="出口压力计"
                                    fontSize={13}
                                    fontFamily="DingTalkJinBuTi, sans-serif"
                                    fill="#ffffff"
                                    align="center"
                                    offsetX={40}
                                />

                                {/* 连接线和曲线 */}
                                {/* 左侧连接线 */}
                                <Line
                                    points={[184, 150, 241.33, 150]}
                                    stroke="#23C26D"
                                    strokeWidth={2}
                                    dash={[15, 8]}
                                    dashOffset={dashOffset}
                                />
                                
                                {/* 左侧弯曲连接线 */}
                                <Path
                                    data="M 411.33 150 Q 476 150 476 80 Q 476 10 536 10"
                                    stroke="#23C26D"
                                    strokeWidth={2}
                                    fill=""
                                    dash={[15, 8]}
                                    dashOffset={dashOffset}
                                />
                                
                                {/* 右侧连接线 */}
                                <Line
                                    points={[1405, 150.09, 1462.33, 150.09]}
                                    stroke="#23C26D"
                                    strokeWidth={2}
                                    dash={[15, 8]}
                                    dashOffset={dashOffset}
                                />
                                
                                {/* 右侧弯曲连接线 */}
                                <Path
                                    data="M 1080 319 Q 1158 319.5 1158 234.8 Q 1158 150.1 1235 150.09"
                                    stroke="#23C26D"
                                    strokeWidth={2}
                                    fill=""
                                    dash={[15, 8]}
                                    dashOffset={dashOffset}
                                />
                            </Group>
                        </Layer>
                        </Stage>
                        
                        {/* 数据显示悬浮层 */}
                        <div
                            className="absolute top-0 left-0 pointer-events-none"
                            style={{
                                width: dimensions.width,
                                height: dimensions.height,
                            }}
                        >
                            <div
                                style={{
                                    position: 'absolute',
                                    left: offsetX,
                                    top: offsetY,
                                    transform: `scale(${scale})`,
                                    transformOrigin: 'top left',
                                }}
                            >
                                {/* 进口流量 */}
                                <div className="absolute pointer-events-auto" style={{ left: 14, top: 115, width: 170, height: 70 }}>
                                    <div className="w-full h-full flex flex-col items-center justify-center pl-[20px]">
                                        <div className="flex items-end">
                                            <NumberFlow value={flowMetrics.inletFlow} precision={2} className="text-[18px] text-[#23C26D] font-mono" duration={800} />
                                            <span className="text-[12px] text-[#23C26D] ml-[5px] mb-[2px]">m³/h</span>
                                        </div>
                                        <div className="flex items-end mt-[5px]">
                                            <NumberFlow value={flowMetrics.inletTotalFlow} precision={2} className="text-[13px] text-[#23C26D] font-mono" duration={800} />
                                            <span className="text-[12px] text-[#23C26D] ml-[5px] mb-0">m³</span>
                                        </div>
                                    </div>
                                </div>
                                {/* 进口压力 */}
                                <div className="absolute pointer-events-auto" style={{ left: 241.33, top: 115, width: 170, height: 70 }}>
                                     <div className="w-full h-full flex flex-col items-center justify-center pl-[20px]">
                                        <div className="flex items-end">
                                            <NumberFlow value={flowMetrics.inletPressure} precision={2} className="text-[20px] text-[#2894B7] font-mono" duration={800} />
                                            <span className="text-[12px] text-[#2894B7] ml-[5px] mb-[2px]">MPa</span>
                                        </div>
                                    </div>
                                </div>
                                {/* 出口流量 */}
                                <div className="absolute pointer-events-auto" style={{ left: 1235, top: 115.09, width: 170, height: 70 }}>
                                     <div className="w-full h-full flex flex-col items-center justify-center pl-[20px]">
                                        <div className="flex items-end">
                                            <NumberFlow value={flowMetrics.outletFlow} precision={2} className="text-[18px] text-[#23C26D] font-mono" duration={800} />
                                            <span className="text-[12px] text-[#23C26D] ml-[5px] mb-[2px]">m³/h</span>
                                        </div>
                                        <div className="flex items-end mt-[5px]">
                                            <NumberFlow value={flowMetrics.outletTotalFlow} precision={2} className="text-[13px] text-[#23C26D] font-mono" duration={800} />
                                            <span className="text-[12px] text-[#23C26D] ml-[5px] mb-0">m³</span>
                                        </div>
                                    </div>
                                </div>
                                {/* 出口压力 */}
                                <div className="absolute pointer-events-auto" style={{ left: 1462.33, top: 115.09, width: 170, height: 70 }}>
                                     <div className="w-full h-full flex flex-col items-center justify-center pl-[20px]">
                                        <div className="flex items-end">
                                            <NumberFlow value={flowMetrics.outletPressure} precision={2} className="text-[20px] text-[#2894B7] font-mono" duration={800} />
                                            <span className="text-[12px] text-[#2894B7] ml-[5px] mb-[2px]">MPa</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        </>
                        )}
                </div>
                
            </div>
            
            {/* 底部装饰栏 */}
            <div 
                ref={el => {
                    if (el && !el.dataset.animated) {
                        el.dataset.animated = 'true';
                        
                        gsap.set(el, { 
                            scaleY: 0, 
                            transformOrigin: 'bottom',
                            opacity: 0 
                        });
                        
                        // 延迟1.5秒后开始底部栏向上扩散动画（与顶部装饰线同时）
                        gsap.timeline({ 
                            delay: 1.5,
                            onComplete: () => {
                                el.style.transform = '';
                                el.style.opacity = '1';
                            }
                        })
                            // 从底部向上快速扩散
                            .to(el, {
                                scaleY: 1,
                                opacity: 1,
                                duration: 0.4,
                                ease: 'power2.out'
                            })
                            // 同时开始闪烁效果
                            .fromTo(el, 
                                { opacity: 1 },
                                {
                                    opacity: 0.3,
                                    duration: 0.07,
                                    repeat: 5, // 6次闪烁
                                    yoyo: true,
                                    ease: 'steps(1)'
                                }, '<') // 与扩散同时开始
                            // 最后确保完全显示
                            .set(el, {
                                opacity: 1,
                                scaleY: 1
                            });
                    }
                }}
                className="absolute bottom-[0px] left-[0px] right-[0px] h-[30px] bg-gradient-to-r from-[#1a1a1a]/50 via-[#2894B7]/5 to-[#1a1a1a]/50 backdrop-blur-sm border-t border-[#2894B7]/15"
            >
                <div className="flex items-center justify-between px-[20px] h-full">
                    {/* 左侧信息 */}
                    <div className="flex items-center space-x-[15px]">
                        <span 
                            ref={el => {
                                if (el && !el.dataset.typed) {
                                    el.dataset.typed = 'true';
                                    
                                    let text = "设备编号: KONVA-001";
                                    el.innerHTML = text;
                                    const finalWidth = el.offsetWidth;
                                    el.style.width = (finalWidth + 10) + 'px';
                                    el.style.minWidth = (finalWidth + 10) + 'px';
                                    el.style.whiteSpace = 'nowrap';
                                    gsap.set(el, { opacity: 0 });
                                    
                                    // 延迟2.1秒后开始打字机效果
                                    gsap.delayedCall(2.1, () => {
                                        el.innerHTML = "";
                                        gsap.set(el, { opacity: 1 });
                                        
                                        const tl = gsap.timeline();
                                        for (let i = 0; i < text.length; i++) {
                                            tl.call(() => {
                                                el.innerHTML = text.substring(0, i + 1);
                                            }, null, i * 0.02);
                                        }
                                    });
                                }
                            }}
                            className="text-[12px] text-[#2894B7] opacity-70 font-['DingTalkJinBuTi']"
                        ></span>
                        <div className="w-[1px] h-[12px] bg-[#2894B7] opacity-30"></div>
                        <span 
                            ref={el => {
                                if (el && !el.dataset.typed) {
                                    el.dataset.typed = 'true';
                                    
                                    let text = "版本: v1.0.0";
                                    el.innerHTML = text;
                                    const finalWidth = el.offsetWidth;
                                    el.style.width = (finalWidth + 10) + 'px';
                                    el.style.minWidth = (finalWidth + 10) + 'px';
                                    el.style.whiteSpace = 'nowrap';
                                    gsap.set(el, { opacity: 0 });
                                    
                                    gsap.delayedCall(2.1, () => {
                                        el.innerHTML = "";
                                        gsap.set(el, { opacity: 1 });
                                        
                                        const tl = gsap.timeline();
                                        for (let i = 0; i < text.length; i++) {
                                            tl.call(() => {
                                                el.innerHTML = text.substring(0, i + 1);
                                            }, null, i * 0.02);
                                        }
                                    });
                                }
                            }}
                            className="text-[12px] text-[#2894B7] opacity-70 font-['DingTalkJinBuTi']"
                        ></span>
                        <div className="w-[1px] h-[12px] bg-[#2894B7] opacity-30"></div>
                        <span 
                            ref={el => {
                                if (el && !el.dataset.typed) {
                                    el.dataset.typed = 'true';
                                    
                                    let text = "在线设备: 6 / 6";
                                    el.innerHTML = text;
                                    const finalWidth = el.offsetWidth;
                                    el.style.width = (finalWidth + 10) + 'px';
                                    el.style.minWidth = (finalWidth + 10) + 'px';
                                    el.style.whiteSpace = 'nowrap';
                                    gsap.set(el, { opacity: 0 });
                                    
                                    gsap.delayedCall(2.1, () => {
                                        el.innerHTML = "";
                                        gsap.set(el, { opacity: 1 });
                                        
                                        const tl = gsap.timeline();
                                        for (let i = 0; i < text.length; i++) {
                                            tl.call(() => {
                                                el.innerHTML = text.substring(0, i + 1);
                                            }, null, i * 0.02);
                                        }
                                    });
                                }
                            }}
                            className="text-[12px] text-white opacity-50 font-['DingTalkJinBuTi']"
                        ></span>
                        <div className="w-[1px] h-[12px] bg-[#2894B7] opacity-30"></div>
                        <span 
                            ref={el => {
                                if (el && !el.dataset.typed) {
                                    el.dataset.typed = 'true';
                                    
                                    let text = "运行正常";
                                    el.innerHTML = text;
                                    const finalWidth = el.offsetWidth;
                                    el.style.width = (finalWidth + 10) + 'px';
                                    el.style.minWidth = (finalWidth + 10) + 'px';
                                    gsap.set(el, { opacity: 0 });
                                    
                                    gsap.delayedCall(2.1, () => {
                                        el.innerHTML = "";
                                        gsap.set(el, { opacity: 1 });
                                        
                                        const tl = gsap.timeline();
                                        for (let i = 0; i < text.length; i++) {
                                            tl.call(() => {
                                                el.innerHTML = text.substring(0, i + 1);
                                            }, null, i * 0.02);
                                        }
                                    });
                                }
                            }}
                            className="text-[12px] text-[#23C26D] opacity-70 font-['DingTalkJinBuTi']"
                        ></span>
                    </div>
                    
                    {/* 右侧信息 */}
                    <div className="flex items-center space-x-[15px]">
                        <span 
                            ref={el => {
                                if (el && !el.dataset.typed) {
                                    el.dataset.typed = 'true';
                                    
                                    let text = `最后更新: ${currentTime.toLocaleString()}`;
                                    el.innerHTML = text;
                                    const finalWidth = el.offsetWidth;
                                    el.style.width = (finalWidth + 10) + 'px';
                                    el.style.minWidth = (finalWidth + 10) + 'px';
                                    el.style.fontSize = '12px';
                                    el.style.whiteSpace = 'nowrap';
                                    gsap.set(el, { opacity: 0 });
                                    
                                    gsap.delayedCall(2.1, () => {
                                        el.innerHTML = "";
                                        gsap.set(el, { opacity: 1 });
                                        
                                        const tl = gsap.timeline();
                                        for (let i = 0; i < text.length; i++) {
                                            tl.call(() => {
                                                el.innerHTML = text.substring(0, i + 1);
                                            }, null, i * 0.015);
                                        }
                                    });
                                }
                            }}
                            className="text-[10px] text-[#2894B7] opacity-50 font-['DingTalkJinBuTi']"
                        ></span>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Lmh;