import React, { useRef, useEffect, useCallback } from 'react';
import Demo1Svg from '/src/assets/imgs/demo1.svg?react';
import gsap from 'gsap';

// 性能优化：提取常量避免重复创建
const ANIMATION_CONFIG = {
  delay: 2,
  strokeDashArray: '30 15',
  strokeDashOffset: -45,
  flowStartDuration: 2,
  steadyFlowDuration: 1.0
};

const SPECIFIC_IDS = ['1a8abee3a6cfdf20-3', 'd5ac148becb70bc-14'];

const STROKE_COLORS = {
  white: ['white', '#ffffff', '#fff', 'rgb(255, 255, 255)'],
  green: ['green', '#008000', '#00ff00']
};

const Demo1SvgComponent = React.memo(() => {
  const svgRef = useRef(null);

  // 性能优化：提取颜色检查函数，使用useCallback避免重复创建
  const isTargetStroke = useCallback((stroke, cellId) => {
    if (SPECIFIC_IDS.includes(cellId)) return true;
    
    if (!stroke) return false;
    
    const lowerStroke = stroke.toLowerCase();
    return STROKE_COLORS.white.includes(stroke) || 
           STROKE_COLORS.green.some(color => lowerStroke.includes(color.toLowerCase()));
  }, []);

  // 性能优化：提取流动动画函数，使用useCallback避免重复创建
  const createFlowAnimation = useCallback((path) => {
    const tl = gsap.timeline({ delay: ANIMATION_CONFIG.delay });
    
    // 第一阶段：慢速流动开始
    tl.to(path, {
      strokeDashoffset: ANIMATION_CONFIG.strokeDashOffset,
      duration: ANIMATION_CONFIG.flowStartDuration,
      ease: 'power1.in',
    });
    
    // 第二阶段：进入稳定的流动速度
    tl.to(path, {
      strokeDashoffset: `+=${ANIMATION_CONFIG.strokeDashOffset}`,
      duration: ANIMATION_CONFIG.steadyFlowDuration,
      repeat: -1,
      ease: 'linear',
    });
  }, []);

  useEffect(() => {
    if (!svgRef.current) return;
    const svgElement = svgRef.current;
    
    // 查找所有路径元素 - 需要根据实际SVG结构调整选择器
    const paths = svgElement.querySelectorAll('path');
    
    paths.forEach((path) => {
      // 获取元素的data-cell-id属性
      const cellId = path.getAttribute('data-cell-id');
      
      // 检查路径的颜色，使用优化的检查函数
      const stroke = path.getAttribute('stroke') || path.style.stroke;
      
      if (isTargetStroke(stroke, cellId)) {
        // 设置虚线效果 - 使用常量配置
        path.setAttribute('stroke-dasharray', ANIMATION_CONFIG.strokeDashArray);
        
        // 为虚线流动添加开场动画策略 - 使用优化的函数
        createFlowAnimation(path);
      }
    });
  }, [isTargetStroke, createFlowAnimation]);

  return (
    <div className="flex flex-col h-full">
      <div className="flex-1 overflow-hidden flex items-center justify-center">
        <Demo1Svg ref={svgRef} className="w-full h-full" />
      </div>
    </div>
  );
});

// 添加显示名称以便调试
Demo1SvgComponent.displayName = 'Demo1SvgComponent';

export default Demo1SvgComponent; 