import { useRef, useEffect, useState } from 'react';
import * as PIXI from 'pixi.js';

// 🎨 PixiJS 高性能工况图组件
const Lmh2 = () => {
    const canvasRef = useRef(null);
    const appRef = useRef(null);
    const mainContainerRef = useRef(null); // 保存主容器引用
    const waterFlowLinesRef = useRef([]); // 保存水流线条引用
    const animationRef = useRef(null); // 保存动画引用
    const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
    const [isInitialized, setIsInitialized] = useState(false); // 标记是否已初始化

    // 监听容器尺寸变化
    useEffect(() => {
        const updateDimensions = () => {
            if (canvasRef.current) {
                const { clientWidth, clientHeight } = canvasRef.current;
                setDimensions({ width: clientWidth, height: clientHeight });
            }
        };

        // 初始化尺寸
        updateDimensions();

        // 监听窗口大小变化
        window.addEventListener('resize', updateDimensions);

        // 使用 ResizeObserver 监听容器大小变化
        const resizeObserver = new ResizeObserver(updateDimensions);
        if (canvasRef.current) {
            resizeObserver.observe(canvasRef.current);
        }

        return () => {
            window.removeEventListener('resize', updateDimensions);
            resizeObserver.disconnect();
        };
    }, []);

    // 🎯 初始化 PIXI 应用（只执行一次）
    useEffect(() => {
        if (dimensions.width === 0 || dimensions.height === 0) return;
        if (isInitialized) return; // 防止重复初始化

        // 创建 PIXI 应用实例 - 使用新的 PixiJS 8.x API
        const app = new PIXI.Application();

        // 初始化应用
        const initApp = async () => {
            try {
                await app.init({
                    width: dimensions.width,
                    height: dimensions.height,
                    backgroundColor: 0x000000, // 透明背景
                    backgroundAlpha: 0, // 设置背景透明度为0
                    antialias: true,
                    resolution: window.devicePixelRatio || 1,
                    autoDensity: true,
                });

                // 检查组件是否仍然挂载
                if (!canvasRef.current) {
                    app.destroy(true);
                    return;
                }

                appRef.current = app;

                // 清空容器内容
                if (canvasRef.current) {
                    canvasRef.current.innerHTML = '';
                }

                // 使用新的 canvas API 替代 view
                if (canvasRef.current && app.canvas) {
                    canvasRef.current.appendChild(app.canvas);
                    // 设置 canvas 样式以适应容器
                    app.canvas.style.width = '100%';
                    app.canvas.style.height = '100%';
                    app.canvas.style.objectFit = 'contain';
                }

                // 🎯 绘制工况图的各个组件（只绘制一次）
                const waterTexture = startWaterFlowAnimation(app);
                drawFlowChart(app, dimensions, waterTexture);

                setIsInitialized(true); // 标记为已初始化
            } catch (error) {
                console.error('PIXI 应用初始化失败:', error);
                if (app) {
                    app.destroy(true);
                }
            }
        };

        initApp();

        // 清理函数
        return () => {
            // 停止动画
            if (animationRef.current) {
                appRef.current?.ticker.remove(animationRef.current);
                animationRef.current = null;
            }

            if (appRef.current) {
                try {
                    // 确保正确清理
                    if (canvasRef.current && appRef.current.canvas && canvasRef.current.contains(appRef.current.canvas)) {
                        canvasRef.current.removeChild(appRef.current.canvas);
                    }
                    appRef.current.destroy(true, {
                        children: true,
                        texture: true,
                        baseTexture: true
                    });
                } catch (error) {
                    console.error('PIXI 应用清理失败:', error);
                } finally {
                    appRef.current = null;
                    mainContainerRef.current = null;
                    waterFlowLinesRef.current = [];
                    setIsInitialized(false);
                }
            }
        };
    }, [dimensions.width, dimensions.height, isInitialized]);

    // 🔄 处理尺寸变化（只更新缩放，不重新绘制）
    useEffect(() => {
        if (!isInitialized || !appRef.current || !mainContainerRef.current) return;
        if (dimensions.width === 0 || dimensions.height === 0) return;

        // 更新应用尺寸
        appRef.current.renderer.resize(dimensions.width, dimensions.height);

        // 确保 canvas 样式同步更新
        if (appRef.current.canvas) {
            appRef.current.canvas.style.width = '100%';
            appRef.current.canvas.style.height = '100%';
        }

        // 重新计算缩放比例
        updateContainerScale(mainContainerRef.current, dimensions);
    }, [dimensions, isInitialized]);

    // 🔄 更新容器缩放（用于尺寸变化时）
    const updateContainerScale = (mainContainer, dimensions) => {
        // 计算工况图的实际边界
        // 左侧：流量计在x=10，宽度160（-80到+80），所以左边界是10-80=-70
        // 右侧：压力计在x=1380，宽度160（-80到+80），所以右边界是1380+80=1460
        // 上下边界需要考虑新的管道系统：上方到y=60，下方到y=340
        const actualLeft = -70;   // 左侧流量计的左边界
        const actualRight = 1460; // 右侧压力计的右边界
        const actualTop = 60;     // 上方管道的上边界
        const actualBottom = 340; // 下方管道的下边界

        const actualWidth = actualRight - actualLeft;   // 1410
        const actualHeight = actualBottom - actualTop;  // 230

        // 添加边距，确保工况图不会贴边
        const padding = 20; // 20px 边距
        const availableWidth = dimensions.width - padding * 2;
        const availableHeight = dimensions.height - padding * 2;

        // 计算缩放比例，确保工况图完全显示在可视范围内
        const scaleX = availableWidth / actualWidth;
        const scaleY = availableHeight / actualHeight;
        const scale = Math.min(scaleX, scaleY, 1); // 限制最大缩放为1，避免过度放大

        // 确保缩放值为正数
        const finalScale = Math.max(scale, 0.1); // 最小缩放为0.1，避免过小

        // 更新缩放
        mainContainer.scale.set(finalScale);

        // 计算实际显示尺寸
        const scaledWidth = actualWidth * finalScale;
        const scaledHeight = actualHeight * finalScale;

        // 重新居中显示 - 考虑实际边界偏移
        const offsetX = actualLeft * finalScale; // 左边界偏移
        const offsetY = actualTop * finalScale;  // 上边界偏移

        mainContainer.x = (dimensions.width - scaledWidth) / 2 - offsetX;
        mainContainer.y = (dimensions.height - scaledHeight) / 2 - offsetY;
    };

    // 🎨 绘制完整的工况图（只执行一次）
    const drawFlowChart = (app, dimensions, waterTexture) => {
        // 创建主容器
        const mainContainer = new PIXI.Container();

        // 保存主容器引用，用于后续缩放更新
        mainContainerRef.current = mainContainer;

        // 应用初始缩放
        updateContainerScale(mainContainer, dimensions);

        app.stage.addChild(mainContainer);

        const graphics = new PIXI.Graphics();
        mainContainer.addChild(graphics);

        // 🔗 绘制主管道系统
        drawPipelineSystem(graphics, mainContainer, waterTexture);

        // 📊 绘制仪表组件
        drawInstruments(mainContainer);

        // ⚙️ 绘制阀门组件
        drawValves(mainContainer);
    };

    // 🌊 创建水流纹理
    const createWaterFlowTexture = () => {
        const canvas = document.createElement('canvas');
        canvas.width = 32;
        canvas.height = 8;
        const ctx = canvas.getContext('2d');

        // 绘制水流图案
        ctx.fillStyle = '#00ff88';
        ctx.fillRect(0, 2, 16, 4);
        ctx.fillRect(24, 2, 8, 4);

        return PIXI.Texture.from(canvas);
    };

    // 🌊 启动水流动画
    const startWaterFlowAnimation = (app) => {
        const waterTexture = createWaterFlowTexture(app);

        const animateWaterFlow = () => {
            // 更新所有水流线条的纹理偏移
            waterFlowLinesRef.current.forEach((line) => {
                if (line && line.tilePosition) {
                    // 降低流动速度
                    const flowSpeed = 0.3;

                    // 检查线条的方向来决定流动方向
                    const rotation = line.rotation;
                    const isHorizontal = Math.abs(rotation) < Math.PI / 4 || Math.abs(rotation - Math.PI) < Math.PI / 4;
                    const isVerticalDown = Math.abs(rotation - Math.PI / 2) < Math.PI / 4;
                    const isVerticalUp = Math.abs(rotation + Math.PI / 2) < Math.PI / 4;

                    if (isHorizontal) {
                        // 水平线：从左到右流动
                        if (rotation > Math.PI / 2 || rotation < -Math.PI / 2) {
                            // 线条方向是从右到左，需要反向流动
                            line.tilePosition.x -= flowSpeed;
                        } else {
                            // 线条方向是从左到右
                            line.tilePosition.x += flowSpeed;
                        }
                    } else if (isVerticalDown) {
                        // 垂直线：从上到下流动
                        line.tilePosition.x += flowSpeed;
                    } else if (isVerticalUp) {
                        // 垂直线：从下到上，需要反向流动
                        line.tilePosition.x -= flowSpeed;
                    } else {
                        // 弧线：统一从左到右流动
                        line.tilePosition.x += flowSpeed;
                    }
                }
            });
        };

        // 保存动画函数引用
        animationRef.current = animateWaterFlow;

        // 添加到ticker
        app.ticker.add(animateWaterFlow);

        return waterTexture;
    };



    // 🌊 创建水流线条
    const createWaterFlowLine = (container, x1, y1, x2, y2, waterTexture) => {
        const length = Math.sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1));
        const angle = Math.atan2(y2 - y1, x2 - x1);

        const tilingSprite = new PIXI.TilingSprite({
            texture: waterTexture,
            width: length,
            height: 3
        });
        tilingSprite.x = x1;
        tilingSprite.y = y1;
        tilingSprite.rotation = angle;
        tilingSprite.anchor.set(0, 0.5);

        container.addChild(tilingSprite);
        waterFlowLinesRef.current.push(tilingSprite);

        return tilingSprite;
    };

    // 🌊 创建弧线水流效果
    const createWaterFlowCurve = (container, startX, startY, controlX, controlY, endX, endY, waterTexture) => {
        // 创建多个小的水流线段来模拟弧线流动，不绘制背景实线
        const segments = 30; // 增加分段数以获得更平滑的效果
        for (let i = 0; i < segments; i++) {
            const t1 = i / segments;
            const t2 = (i + 1) / segments;

            // 计算贝塞尔曲线上的点
            const x1 = (1 - t1) * (1 - t1) * startX + 2 * (1 - t1) * t1 * controlX + t1 * t1 * endX;
            const y1 = (1 - t1) * (1 - t1) * startY + 2 * (1 - t1) * t1 * controlY + t1 * t1 * endY;
            const x2 = (1 - t2) * (1 - t2) * startX + 2 * (1 - t2) * t2 * controlX + t2 * t2 * endX;
            const y2 = (1 - t2) * (1 - t2) * startY + 2 * (1 - t2) * t2 * controlY + t2 * t2 * endY;

            const length = Math.sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1));
            const angle = Math.atan2(y2 - y1, x2 - x1);

            const tilingSprite = new PIXI.TilingSprite({
                texture: waterTexture,
                width: length,
                height: 3
            });
            tilingSprite.x = x1;
            tilingSprite.y = y1;
            tilingSprite.rotation = angle;
            tilingSprite.anchor.set(0, 0.5);

            container.addChild(tilingSprite);
            waterFlowLinesRef.current.push(tilingSprite);
        }
    };

    // 🔗 绘制管道系统
    const drawPipelineSystem = (graphics, container, waterTexture) => {
        // 使用新的线条样式API
        graphics.setStrokeStyle({
            width: 3,
            color: 0x00ff88,
            alpha: 1
        });

        // 移除主水平管道，让泵机与进出口容器保持在同一水平线

        // 左侧进口管道连接（横向布局）- 水流管道
        createWaterFlowLine(container, 90, 200, 140, 200, waterTexture);

        // 右侧出口管道连接（横向布局）- 水流管道
        createWaterFlowLine(container, 1230, 200, 1300, 200, waterTexture);

        // 绘制泵机连接管道系统（上下连接线+横向连接线）
        const pumpColors = [
            0xff6b6b, // 红色 - 一号泵
            0x4ecdc4, // 青色 - 二号泵
            0x45b7d1, // 蓝色 - 三号泵
            0x96ceb4, // 绿色 - 四号泵
            0xfeca57, // 黄色 - 五号泵
            0xfd79a8  // 粉色 - 六号泵
        ];

        // 绘制每个泵机的上下连接线
        for (let i = 0; i < 6; i++) {
            const x = 435 + i * 100;

            // 一号泵使用水流样式（绿色），其他泵保持原色
            if (i === 0) {
                // 一号泵上下连接线使用水流样式
                createWaterFlowLine(container, x, 200, x, 60, waterTexture);
                createWaterFlowLine(container, x, 200, x, 340, waterTexture);
            } else {
                // 其他泵保持原色实线
                const color = pumpColors[i];
                graphics.setStrokeStyle({ width: 2, color: color });

                // 上方连接线
                graphics.moveTo(x, 200);
                graphics.lineTo(x, 60);
                graphics.stroke();

                // 下方连接线
                graphics.moveTo(x, 200);
                graphics.lineTo(x, 340);
                graphics.stroke();
            }
        }

        // 绘制上方横向连接线（分段，每段不同颜色）
        const topColors = [
            0xff6b6b, // 红色 - 连接一号泵和二号泵
            0x4ecdc4, // 青色 - 连接二号泵和三号泵
            0x45b7d1, // 蓝色 - 连接三号泵和四号泵
            0x96ceb4, // 绿色 - 连接四号泵和五号泵
            0xfeca57  // 黄色 - 连接五号泵和六号泵
        ];

        // 绘制5段上方横线（连接6个泵机的垂直线）
        for (let i = 0; i < 5; i++) {
            const startX = 435 + i * 100;       // 当前泵机位置
            const endX = 435 + (i + 1) * 100;   // 下一个泵机位置
            const y = 60;                       // 上方横线高度

            graphics.setStrokeStyle({ width: 2, color: topColors[i] });
            graphics.moveTo(startX, y);
            graphics.lineTo(endX, y);
            graphics.stroke();
        }

        // 绘制5段下方横线（连接6个泵机的垂直线）- 全部改为水流样式
        for (let i = 0; i < 5; i++) {
            const startX = 435 + i * 100;       // 当前泵机位置
            const endX = 435 + (i + 1) * 100;   // 下一个泵机位置
            const y = 340;                      // 下方横线高度

            createWaterFlowLine(container, startX, y, endX, y, waterTexture);
        }

        // 绘制左侧弧线连接（进口压力计右侧 → 顶部绿线左端）- 水流样式
        const startX1 = 300;  // 进口压力计右侧（220+80）
        const startY1 = 200;  // 压力计高度
        const endX1 = 435;    // 绿线左端（确保连接到端点）
        const endY1 = 60;     // 绿线高度
        const controlX1 = startX1 + 80; // 控制点X，增大弧度
        const controlY1 = endY1;        // 控制点Y（与目标高度相同）

        createWaterFlowCurve(container, startX1, startY1, controlX1, controlY1, endX1, endY1, waterTexture);

        // 绘制右侧弧线连接（底部绿色线右端 → 出口流量计左侧）- 水流样式
        const startX2 = 935;  // 绿色线右端（确保连接到端点）
        const startY2 = 340;  // 绿色线高度
        const endX2 = 1070;   // 出口流量计左侧（1150-80）
        const endY2 = 200;    // 流量计高度
        const controlX2 = endX2 - 80;   // 控制点X，增大弧度
        const controlY2 = startY2;      // 控制点Y（与起始高度相同）

        createWaterFlowCurve(container, startX2, startY2, controlX2, controlY2, endX2, endY2, waterTexture);

        // 绘制连接节点
        graphics.setStrokeStyle({ width: 0 });
        graphics.setFillStyle({ color: 0x00ff88 });

        // 移除所有连接点，保持简洁的设计
        const connectionPoints = [];

        connectionPoints.forEach(([x, y]) => {
            graphics.circle(x, y, 4);
            graphics.fill();
        });
    };

    // 📊 绘制仪表组件
    const drawInstruments = (mainContainer) => {
        // 🌊 左侧进口流量计（向左移动增加间距）
        createFlowMeter(mainContainer, 10, 200, '进口流量计');

        // 🔽 左侧进口压力计（横向布局，与流量计同一水平线，保持距离）
        createPressureMeter(mainContainer, 220, 200, '进口压力计');

        // 🌊 右侧出口流量计（横向布局，与进口保持一致，去掉数据）
        createFlowMeter(mainContainer, 1150, 200, '出口流量计');

        // 🔽 右侧出口压力计（向右移动增加间距）
        createPressureMeter(mainContainer, 1380, 200, '出口压力计');
    };

    // ⚙️ 绘制阀门组件
    const drawValves = (mainContainer) => {
        const valveNames = ['一号泵', '二号泵', '三号泵', '四号泵', '五号泵', '六号泵'];

        valveNames.forEach((name, index) => {
            // 计算居中位置：主管道从305到1065，中心点是685，6个泵机总宽度500，起始位置435
            const x = 435 + index * 100; // 调整起始位置为435以实现居中
            const y = 200; // 与进出口容器保持在同一水平线
            createValve(mainContainer, x, y, name);
        });
    };

    // 🌊 创建流量计组件
    const createFlowMeter = (parentContainer, x, y, title, mainValue = '', unit = '', totalValue = '') => {
        const container = new PIXI.Container();
        container.x = x;
        container.y = y;

        // 背景框（稍微缩小宽度，透明背景）
        const bg = new PIXI.Graphics();
        bg.setStrokeStyle({ width: 1, color: 0x00ff88 });
        bg.roundRect(-80, -35, 160, 70, 8);
        bg.stroke();
        container.addChild(bg);

        // 流量计图标（调整位置适应新容器）
        const icon = new PIXI.Graphics();
        icon.setFillStyle({ color: 0x00ff88 });
        icon.circle(-50, 0, 15);
        icon.fill();

        // 添加刻度线
        for (let i = 0; i < 8; i++) {
            const angle = (i / 8) * Math.PI * 2;
            const startX = -50 + Math.cos(angle) * 10;
            const startY = Math.sin(angle) * 10;
            const endX = -50 + Math.cos(angle) * 12;
            const endY = Math.sin(angle) * 12;

            icon.setStrokeStyle({ width: 1, color: 0x1a1a1a });
            icon.moveTo(startX, startY);
            icon.lineTo(endX, endY);
            icon.stroke();
        }
        container.addChild(icon);

        // 主数值（只在有数据时显示）
        if (mainValue && unit) {
            const mainText = new PIXI.Text({
                text: `${mainValue} ${unit}`,
                style: {
                    fontFamily: 'Arial',
                    fontSize: 14,
                    fill: 0x00ff88,
                    fontWeight: 'bold',
                }
            });
            mainText.x = -20;
            mainText.y = -10;
            container.addChild(mainText);
        }

        // 累计值（只在有数据时显示）
        if (totalValue) {
            const totalText = new PIXI.Text({
                text: totalValue,
                style: {
                    fontFamily: 'Arial',
                    fontSize: 10,
                    fill: 0xcccccc,
                }
            });
            totalText.x = -20;
            totalText.y = 8;
            container.addChild(totalText);
        }

        // 标题
        const titleText = new PIXI.Text({
            text: title,
            style: {
                fontFamily: 'Arial',
                fontSize: 12, // 稍微增大字体
                fill: 0xffffff, // 修改为白色
            }
        });
        titleText.x = -titleText.width / 2;
        titleText.y = -55;
        container.addChild(titleText);

        // 修复：添加到父容器而不是 app.stage
        parentContainer.addChild(container);
    };

    // 🔽 创建压力计组件
    const createPressureMeter = (parentContainer, x, y, title, value = '', unit = '') => {
        const container = new PIXI.Container();
        container.x = x;
        container.y = y;

        // 背景框（与流量计保持一致的尺寸：160x70，透明背景）
        const bg = new PIXI.Graphics();
        bg.setStrokeStyle({ width: 1, color: 0x4a9eff });
        bg.roundRect(-80, -35, 160, 70, 8);
        bg.stroke();
        container.addChild(bg);

        // 压力计图标（调整位置以适应新的容器尺寸）
        const icon = new PIXI.Graphics();
        icon.setFillStyle({ color: 0x4a9eff });
        icon.circle(-50, 0, 15);
        icon.fill();

        // 指针
        icon.setStrokeStyle({ width: 2, color: 0x1a1a1a });
        icon.moveTo(-50, 0);
        icon.lineTo(-50 + 10, -6);
        icon.stroke();
        container.addChild(icon);

        // 数值文本（只在有数据时显示）
        if (value && unit) {
            const valueText = new PIXI.Text({
                text: `${value} ${unit}`,
                style: {
                    fontFamily: 'Arial',
                    fontSize: 14,
                    fill: 0x4a9eff,
                    fontWeight: 'bold',
                }
            });
            valueText.x = -15;
            valueText.y = -10;
            container.addChild(valueText);
        }

        // 标题
        const titleText = new PIXI.Text({
            text: title,
            style: {
                fontFamily: 'Arial',
                fontSize: 12, // 稍微增大字体
                fill: 0xffffff, // 修改为白色
            }
        });
        titleText.x = -titleText.width / 2;
        titleText.y = -55;
        container.addChild(titleText);

        // 修复：添加到父容器而不是 app.stage
        parentContainer.addChild(container);
    };

    // ⚙️ 创建阀门组件
    const createValve = (parentContainer, x, y, name) => {
        const container = new PIXI.Container();
        container.x = x;
        container.y = y;

        // 阀门背景
        const bg = new PIXI.Graphics();
        bg.setFillStyle({ color: 0x141414, alpha: 0.9 }); // 恢复背景色为#141414
        bg.setStrokeStyle({ width: 1, color: 0xffffff }); // 边线修改细一点
        bg.roundRect(-30, -30, 60, 60, 8);
        bg.fill();
        bg.stroke();
        container.addChild(bg);

        // 泵机风扇（十字形状）
        const fan = new PIXI.Graphics();
        fan.setStrokeStyle({ width: 3, color: 0xffffff });

        // 绘制十字形状
        // 水平线
        fan.moveTo(-15, 0);
        fan.lineTo(15, 0);
        fan.stroke();

        // 垂直线
        fan.moveTo(0, -15);
        fan.lineTo(0, 15);
        fan.stroke();

        container.addChild(fan);

        // 中心圆点
        const center = new PIXI.Graphics();
        center.setFillStyle({ color: 0xffffff });
        center.circle(0, 0, 2);
        center.fill();
        container.addChild(center);

        // 阀门名称背景遮罩
        const nameBg = new PIXI.Graphics();
        const nameText = new PIXI.Text({
            text: name,
            style: {
                fontFamily: 'Arial',
                fontSize: 10,
                fill: 0xffffff,
            }
        });

        // 计算文字尺寸并创建背景
        const textWidth = nameText.width;
        const textHeight = nameText.height;
        const padding = 2; // 背景边距

        nameBg.setFillStyle({ color: 0x141414 }); // 背景色改为#141414，无透明度
        nameBg.roundRect(-textWidth/2 - padding, 40 - padding, textWidth + padding*2, textHeight + padding*2, 3);
        nameBg.fill();
        container.addChild(nameBg);

        // 阀门名称
        nameText.x = -nameText.width / 2;
        nameText.y = 40;
        container.addChild(nameText);

        // 修复：添加到父容器而不是 app.stage
        parentContainer.addChild(container);
    };

    return (
        <div className="w-full h-full relative">
            <div
                ref={canvasRef}
                className="w-full h-full"
            />
        </div>
    );
};

export default Lmh2;
