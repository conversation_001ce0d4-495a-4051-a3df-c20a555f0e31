import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { gsap } from 'gsap';
import LevelMeterDetail from './components/LevelMeterDetail';

// 常量配置
const ANIMATION_CONFIG = {
    bottomLine: {
        delay: 800,
        duration: 800,
        ease: 'power2.out',
        flickerDuration: 0.06,
        flickerRepeats: 20
    },
    rightBottomLine: {
        delay: 300,
        duration: 600,
        ease: 'power2.out',
        flickerDuration: 0.05,
        flickerRepeats: 15
    },
    titleBackground: {
        delay: 100
    },
    menuButtons: {
        delay: 50,
        lineAnimationDuration: 600,
        textAnimationDelay: 600,
        maxRandomDelay: 500
    },
    typing: {
        interval: 100
    }
};

const MENU_BUTTONS = ['设备首页', '数据记录', '设备信息', '设备设置'];

const COLORS = {
    primary: '#23A861',
    secondary: '#909999',
    white: '#FFFFFF',
    gray: '#A9A6A9',
    background: '#1C2A27',
    line: 'rgba(90,170,180,0.5)'
};

// 菜单按钮组件 - 优化后的版本
const MenuButton = React.memo(({ 
    name, 
    isSelected, 
    onClick, 
    buttonRef, 
    isAnimationComplete, 
    isLineAnimationComplete,
    allAnimationsComplete 
}) => {
    const buttonStyle = useMemo(() => ({
        color: isSelected ? COLORS.primary : COLORS.secondary
    }), [isSelected]);

    const lineStyle = useMemo(() => ({
        backgroundColor: isSelected ? COLORS.primary : COLORS.secondary
    }), [isSelected]);

    return (
        <div 
            ref={buttonRef}
            className="relative cursor-pointer hover:opacity-80 transition-opacity duration-200 ml-[12px] inline-block"
            onClick={(event) => onClick(event)}
        >
            <div 
                className={`menu-text text-[14px] font-['DingTalkJinBuTi',sans-serif] font-medium pt-[6px] pb-[6px] text-left transition-all duration-200 ${
                    isAnimationComplete ? 'opacity-100 transform translate-y-[0px]' : 'opacity-0 transform translate-y-[5px]'
                }`}
                style={buttonStyle}
            >
                {name}
            </div>
            <div 
                className={`menu-bottom-line absolute bottom-[1px] left-[0px] right-[0px] h-[3px] transition-all duration-200 ${
                    isLineAnimationComplete ? 'opacity-100' : 'opacity-0'
                } ${isSelected && allAnimationsComplete ? 'menu-breathing' : ''}`} 
                style={{...lineStyle}}
            />
        </div>
    );
});

// 设备详情页面组件
const DeviceInfo = () => {
    // 所有状态声明必须在组件顶部
    const [selectedButton, setSelectedButton] = useState('设备首页');
    const [displayedTitle, setDisplayedTitle] = useState('');
    const [isTypingComplete, setIsTypingComplete] = useState(false);
    const [shouldStartTyping, setShouldStartTyping] = useState(false);
    const [showTitleBackground, setShowTitleBackground] = useState(false);
    const [showMenuButton, setShowMenuButton] = useState(false);
    const [buttonAnimationDelays, setButtonAnimationDelays] = useState({});
    const [buttonAnimationsComplete, setButtonAnimationsComplete] = useState({});
    const [menuLineAnimationsComplete, setMenuLineAnimationsComplete] = useState({});
    const [allMenuAnimationsComplete, setAllMenuAnimationsComplete] = useState(false);
    
    // 所有 ref 声明
    const titleTextRef = useRef(null);
    const bottomLineRef = useRef(null);
    const decorativeLineRef = useRef(null);
    const titleBackgroundRef = useRef(null);
    const menuButtonRef = useRef(null);
    const dataRecordButtonRef = useRef(null);
    const deviceInfoButtonRef = useRef(null);
    const deviceSettingsButtonRef = useRef(null);
    const rightBottomLineRef = useRef(null);
    
    // 设备名称（临时写死）
    const deviceName = "枫林小区1号液位计";

    // 按钮点击处理 - 使用 useCallback 优化，添加闪烁动画
    const handleButtonClick = useCallback((buttonName, event) => {
        // 创建闪烁动画
        const currentTarget = event.currentTarget;
        
        gsap.to(currentTarget, {
            filter: 'brightness(0.8)', 
            duration: 0.05, 
            yoyo: true, 
            repeat: 3,
            ease: 'power1.inOut',
            onComplete: () => {
                // 动画完成后更新状态
                setSelectedButton(buttonName);
            }
        });
    }, []);

    // 计算标题容器宽度 - 使用 useMemo 优化
    const titleContainerWidth = useMemo(() => `${deviceName.length * 14 + 60}px`, [deviceName.length]);
    const titleBackgroundWidth = useMemo(() => `${deviceName.length * 14 + 30}px`, [deviceName.length]);

    // 菜单按钮组件列表 - 使用 useMemo 优化
    const menuButtonComponents = useMemo(() => {
        const buttonRefs = [menuButtonRef, dataRecordButtonRef, deviceInfoButtonRef, deviceSettingsButtonRef];
        
        return MENU_BUTTONS.map((name, index) => (
            <MenuButton 
                key={name}
                name={name} 
                isSelected={selectedButton === name} 
                onClick={(event) => handleButtonClick(name, event)}
                buttonRef={buttonRefs[index]}
                isAnimationComplete={buttonAnimationsComplete[name]}
                isLineAnimationComplete={menuLineAnimationsComplete[name]}
                allAnimationsComplete={allMenuAnimationsComplete}
            />
        ));
    }, [selectedButton, buttonAnimationsComplete, menuLineAnimationsComplete, allMenuAnimationsComplete, handleButtonClick]);

    // 底部线条扩散动画 - 组件加载时立即开始
    useEffect(() => {
        if (bottomLineRef.current) {
            const tl = gsap.timeline({ delay: ANIMATION_CONFIG.bottomLine.delay / 1000 });
            
            gsap.set(bottomLineRef.current, {
                width: 0,
                left: '50%',
                right: 'auto',
                transformOrigin: 'center center',
                transform: 'translateX(-50%)',
                opacity: 1
            });
            
            tl.to(bottomLineRef.current, {
                width: '100%',
                left: '0%',
                transform: 'translateX(0%)',
                duration: ANIMATION_CONFIG.bottomLine.duration / 1000,
                ease: ANIMATION_CONFIG.bottomLine.ease,
                onComplete: () => {
                    setShouldStartTyping(true);
                }
            });
            
            tl.fromTo(bottomLineRef.current, 
                { autoAlpha: 0.6 },
                { 
                    autoAlpha: 1, 
                    duration: ANIMATION_CONFIG.bottomLine.flickerDuration, 
                    ease: "steps(1)",
                    repeat: ANIMATION_CONFIG.bottomLine.flickerRepeats,
                    yoyo: true
                }, "<");
        }
    }, []);
    
    // 右侧底部线条动画
    useEffect(() => {
        if (!showMenuButton || !rightBottomLineRef.current) return;
        
        const timer = setTimeout(() => {
            const tl = gsap.timeline();
            
            gsap.set(rightBottomLineRef.current, {
                width: 0,
                left: '50%',
                right: 'auto',
                transformOrigin: 'center center',
                transform: 'translateX(-50%)',
                opacity: 1
            });
            
            tl.to(rightBottomLineRef.current, {
                width: '100%',
                left: '0%',
                transform: 'translateX(0%)',
                duration: ANIMATION_CONFIG.rightBottomLine.duration / 1000,
                ease: ANIMATION_CONFIG.rightBottomLine.ease
            });
            
            tl.fromTo(rightBottomLineRef.current, 
                { autoAlpha: 0.6 },
                { 
                    autoAlpha: 1, 
                    duration: ANIMATION_CONFIG.rightBottomLine.flickerDuration, 
                    ease: "steps(1)",
                    repeat: ANIMATION_CONFIG.rightBottomLine.flickerRepeats,
                    yoyo: true
                }, "<");
        }, ANIMATION_CONFIG.rightBottomLine.delay);
        
        return () => clearTimeout(timer);
    }, [showMenuButton]);
    
    // 标题背景和菜单按钮动画
    useEffect(() => {
        if (!isTypingComplete || !titleBackgroundRef.current) return;
        
                 const timer = setTimeout(() => {
             titleBackgroundRef.current.classList.add('animate-titleBackground');
             
             setTimeout(() => {
                 const delays = Object.fromEntries(
                     MENU_BUTTONS.map(name => [name, Math.random() * ANIMATION_CONFIG.menuButtons.maxRandomDelay])
                 );
                 
                 setShowMenuButton(true);
                 setButtonAnimationDelays(delays);
                 
                 setTimeout(() => {
                     const buttonRefs = [menuButtonRef, dataRecordButtonRef, deviceInfoButtonRef, deviceSettingsButtonRef];
                     
                     MENU_BUTTONS.forEach((name, index) => {
                         const ref = buttonRefs[index];
                         if (ref.current) {
                             setTimeout(() => {
                                 const bottomLine = ref.current.querySelector('.menu-bottom-line');
                                 const textElement = ref.current.querySelector('.menu-text');
                                 
                                 if (bottomLine) {
                                     // 随机选择扩散动画类型
                                     const animationType = Math.random() > 0.5 ? 'center' : 'left';
                                     bottomLine.classList.add(`animate-menuLine-${animationType}`);
                                     
                                     setTimeout(() => {
                                         setMenuLineAnimationsComplete(prev => ({
                                             ...prev,
                                             [name]: true
                                         }));
                                     }, ANIMATION_CONFIG.menuButtons.lineAnimationDuration);
                                     
                                     setTimeout(() => {
                                         if (textElement) {
                                             textElement.classList.add('animate-fadeInUp');
                                             setButtonAnimationsComplete(prev => {
                                                 const newState = {
                                                     ...prev,
                                                     [name]: true
                                                 };
                                                 // 检查是否所有按钮动画都完成了
                                                 const allComplete = MENU_BUTTONS.every(buttonName => 
                                                     newState[buttonName] === true
                                                 );
                                                 if (allComplete) {
                                                     // 延迟一点时间再开始呼吸灯效果，确保扩散动画完全结束
                                                     setTimeout(() => {
                                                         setAllMenuAnimationsComplete(true);
                                                     }, 500);
                                                 }
                                                 return newState;
                                             });
                                         }
                                     }, ANIMATION_CONFIG.menuButtons.textAnimationDelay);
                                 }
                             }, delays[name]);
                         }
                     });
                 }, 50);
             }, ANIMATION_CONFIG.menuButtons.delay);
         }, ANIMATION_CONFIG.titleBackground.delay);
        
        return () => clearTimeout(timer);
    }, [isTypingComplete]);

    // 打字机效果
    useEffect(() => {
        if (!shouldStartTyping) return;
        
        let currentIndex = 0;
        const typingInterval = setInterval(() => {
            if (currentIndex <= deviceName.length) {
                setDisplayedTitle(deviceName.slice(0, currentIndex));
                currentIndex++;
            } else {
                clearInterval(typingInterval);
                setIsTypingComplete(true);
            }
        }, ANIMATION_CONFIG.typing.interval);
        
        return () => clearInterval(typingInterval);
    }, [shouldStartTyping, deviceName]);
    
    return (
        <div className="device-info-container w-full h-full bg-transparent flex flex-col" style={{ minHeight: '200px', opacity: 1, visibility: 'visible' }}>
            {/* 顶部标题栏 - 横向布局：左侧标题，右侧菜单 */}
            <div className="title-header relative h-[30px] flex items-center px-[0] flex-shrink-0 z-[1]">
                {/* 左侧标题区域 */}
                <div className="flex items-center relative h-[25px] pointer-events-none" style={{ width: titleContainerWidth }}>
                    {/* 斜条纹背景层 */}
                    <div 
                        ref={titleBackgroundRef}
                        className="absolute top-[2px] left-[10px] bg-[repeating-linear-gradient(-45deg,#1C2A27,#1C2A27_10px,transparent_10px,transparent_20px)] rounded-[4px] opacity-0 h-[20px] z-0 transition-opacity duration-300"
                        style={{ width: titleBackgroundWidth }}
                    />
                    
                    {/* 设备名称 - 打字机效果 */}
                    <h1 
                        ref={titleTextRef}
                        className={`relative text-[14px] font-['DingTalkJinBuTi',sans-serif] text-[#FFFFFF] font-medium pl-[28px] pr-[20px] py-[4px] z-10 ${shouldStartTyping && !isTypingComplete ? 'animate-typingBlink' : ''}`}
                    >
                        {displayedTitle}
                        {/* 打字机光标效果 */}
                        {shouldStartTyping && !isTypingComplete && (
                            <span className="ml-[2px] text-[#23A861] animate-pulse">|</span>
                        )}
                    </h1>
                    
                    {/* 左侧区域底部线条 */}
                    <div 
                        ref={bottomLineRef}
                        className="absolute bottom-[-2px] h-[1px] bg-[#BFBEC4] opacity-0"
                    />
                </div>
                
                {/* 右侧菜单区域 */}
                <div className="flex items-center flex-grow">
                    {/* 菜单按钮容器 */}
                    <div className="flex items-center">
                        {showMenuButton && menuButtonComponents}
                    </div>
                    
                    {/* 占位容器 - 自适应宽度，拉满剩余空间 */}
                    <div className="flex-grow relative ml-[20px] h-[25px]">
                        {/* 占位容器底部线条 */}
                        <div 
                            ref={rightBottomLineRef}
                            className="absolute bottom-[-2px] h-[1px] bg-[#858582] opacity-0"
                        />
                    </div>
                </div>
            </div>
            
            {/* 主要内容区域 */}
            <div className="content-area flex-grow">
                {/* 根据选中的菜单按钮显示不同内容 */}
                {selectedButton === '设备首页' && (
                    <LevelMeterDetail />
                )}
                {selectedButton === '数据记录' && (
                    <div className="w-full h-full flex items-center justify-center text-[#909999] text-[16px] font-['DingTalkJinBuTi',sans-serif]">
                        数据记录页面开发中...
                    </div>
                )}
                {selectedButton === '设备信息' && (
                    <div className="w-full h-full flex items-center justify-center text-[#909999] text-[16px] font-['DingTalkJinBuTi',sans-serif]">
                        设备信息页面开发中...
                    </div>
                )}
                {selectedButton === '设备设置' && (
                    <div className="w-full h-full flex items-center justify-center text-[#909999] text-[16px] font-['DingTalkJinBuTi',sans-serif]">
                        设备设置页面开发中...
                    </div>
                )}
            </div>
            
            {/* 菜单按钮动画样式 */}
            <style dangerouslySetInnerHTML={{
                __html: `
                @keyframes menuButtonBreathing {
                    0%, 100% { 
                        background-color: rgba(35, 168, 97, 0.4); 
                    }
                    50% { 
                        background-color: rgba(35, 168, 97, 1); 
                    }
                }
                @keyframes menuLineExpandFromCenter {
                    0% {
                        transform: scaleX(0);
                        transform-origin: center;
                        opacity: 0.2;
                    }
                    30% {
                        transform: scaleX(1);
                        transform-origin: center;
                        opacity: 0.2;
                    }
                    35%, 45%, 55%, 65%, 75%, 85% {
                        opacity: 1;
                    }
                    40%, 50%, 60%, 70%, 80%, 90% {
                        opacity: 0.2;
                    }
                    100% {
                        transform: scaleX(1);
                        transform-origin: center;
                        opacity: 0.8;
                    }
                }
                @keyframes menuLineExpandFromLeft {
                    0% {
                        transform: scaleX(0);
                        transform-origin: left;
                        opacity: 0.2;
                    }
                    30% {
                        transform: scaleX(1);
                        transform-origin: left;
                        opacity: 0.2;
                    }
                    35%, 45%, 55%, 65%, 75%, 85% {
                        opacity: 1;
                    }
                    40%, 50%, 60%, 70%, 80%, 90% {
                        opacity: 0.2;
                    }
                    100% {
                        transform: scaleX(1);
                        transform-origin: left;
                        opacity: 0.8;
                    }
                }
                @keyframes fadeInUp {
                    0% {
                        opacity: 0;
                        transform: translateY(5px);
                    }
                    100% {
                        opacity: 1;
                        transform: translateY(0px);
                    }
                }
                .menu-breathing {
                    animation: menuButtonBreathing 1.5s ease-in-out infinite;
                }
                .animate-menuLine-center {
                    animation: menuLineExpandFromCenter 0.6s ease-out forwards;
                }
                .animate-menuLine-left {
                    animation: menuLineExpandFromLeft 0.6s ease-out forwards;
                }
                .animate-fadeInUp {
                    animation: fadeInUp 0.3s ease-out forwards;
                }
                `
            }} />
        </div>
    );
};

export default DeviceInfo;
