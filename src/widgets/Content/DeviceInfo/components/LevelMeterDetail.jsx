import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import NumberFlow from '@number-flow/react';
import { gsap } from 'gsap';

// 性能优化：提取常量避免重复创建
const ANIMATION_CONFIG = {
    initialDelay: 1500,
    randomDelayRange: 1000,
    flashDuration: 800,
    containerShowDelay: 1500,
    initialAnimationDelay: 3300,
    waterAnimationDuration: 3000,
    cycleDuration: 2000,
    cycleInterval: 3000
};

const WATER_LEVEL_RANGE = {
    min: 6.25,
    max: 87.5,
    range: 81.25
};

const TREND_ARROWS = {
    up: '↑',
    down: '↓',
    stable: '→',
    default: ''
};

const TREND_COLORS = {
    up: '#23A861',
    down: '#FF6B6B',
    stable: '#FFA500',
    default: '#23A861'
};

// 🎯 性能优化：提取样式常量
const CONTAINER_STYLES = {
    backgroundStripes: `repeating-linear-gradient(
        -45deg,
        rgba(39, 160, 188, 0.5) 0px,
        rgba(39, 160, 188, 0.5) 20px,
        rgba(30, 130, 150, 0.5) 20px,
        rgba(30, 130, 150, 0.5) 40px
    )`,
    gridPattern: `
        linear-gradient(rgba(35, 168, 97, 0.2) 1px, transparent 1px),
        linear-gradient(90deg, rgba(35, 168, 97, 0.2) 1px, transparent 1px)
    `
};

// 液位计详情页面组件 - 使用memo优化重新渲染
const LevelMeterDetail = React.memo(() => {
    // 🎯 性能优化：合并相关状态，减少状态数量
    const [levelData] = useState({
        highAlarmValue: 7.0,
        lowAlarmValue: 2.0,
        highAlarmTrend: 'stable',
        lowAlarmTrend: 'stable',
        currentLevel: 74.8,
        totalHeight: 8.0
    });

    // 🎯 性能优化：合并动画相关状态
    const [animationStates, setAnimationStates] = useState({
        showContainer: false,
        isInitialAnimating: true,
        hasCompletedInitial: false,
        showLeftLines: false,
        showHighAlarmContainer: false,
        showLowAlarmContainer: false,
        isHighAlarmFlashing: false,
        isLowAlarmFlashing: false
    });

    // 🎯 性能优化：合并水位相关状态
    const [waterStates, setWaterStates] = useState({
        waterLevel: 100,
        heightValue: 0,
        isHighAlarm: false,
        isLowAlarm: false
    });

    // 🎯 性能优化：合并显示值状态
    const [displayValues, setDisplayValues] = useState({
        highAlarm: 0.0,
        lowAlarm: 0.0
    });
    
    const heightRef = useRef({ value: 0 });
    const animationTimelines = useRef([]); // 🎯 性能优化：统一管理动画时间线

    // 🎯 性能优化：使用useCallback优化状态更新函数
    const updateAnimationStates = useCallback((updates) => {
        setAnimationStates(prev => ({ ...prev, ...updates }));
    }, []);

    const updateWaterStates = useCallback((updates) => {
        setWaterStates(prev => ({ ...prev, ...updates }));
    }, []);

    const updateDisplayValues = useCallback((updates) => {
        setDisplayValues(prev => ({ ...prev, ...updates }));
    }, []);

    // 计算报警线位置（百分比）- 使用useCallback优化
    const calculateAlarmLinePosition = useCallback((alarmValue) => {
        const position = ((levelData.totalHeight - alarmValue) / levelData.totalHeight) * 100;
        return Math.max(0, Math.min(100, position));
    }, [levelData.totalHeight]);
    
    // 使用useMemo缓存计算结果，避免重复计算
    const alarmLinePositions = useMemo(() => ({
        high: calculateAlarmLinePosition(levelData.highAlarmValue),
        low: calculateAlarmLinePosition(levelData.lowAlarmValue)
    }), [calculateAlarmLinePosition, levelData.highAlarmValue, levelData.lowAlarmValue]);
    
    // 获取趋势箭头和颜色 - 使用useCallback优化
    const getTrendArrow = useCallback((trend) => TREND_ARROWS[trend] || TREND_ARROWS.default, []);
    const getTrendColor = useCallback((trend) => TREND_COLORS[trend] || TREND_COLORS.default, []);

    // 🎯 性能优化：合并GSAP动画函数
    const createWaterLevelAnimation = useCallback((targetHeight, duration = ANIMATION_CONFIG.waterAnimationDuration) => {
        // 清理之前的动画
        animationTimelines.current.forEach(tl => tl && tl.kill());
        animationTimelines.current = [];

        const animation = gsap.to(heightRef.current, {
            value: targetHeight,
            duration: duration / 1000,
            ease: 'power1.out',
            onUpdate: () => {
                if (!heightRef.current.updating) {
                    heightRef.current.updating = true;
                    requestAnimationFrame(() => {
                        updateWaterStates({ heightValue: heightRef.current.value });
                        heightRef.current.updating = false;
                    });
                }
            },
            onComplete: () => {
                heightRef.current.value = targetHeight;
                updateWaterStates({ heightValue: targetHeight });
            }
        });

        animationTimelines.current.push(animation);
        return animation;
    }, [updateWaterStates]);

    // 开场动画效果 - 优化版本
    useEffect(() => {
        updateAnimationStates({ showLeftLines: true });
        
        // 🎯 性能优化：使用统一的定时器管理
        const timers = [];
        
        // 高位报警容器动画
        const highAlarmDelay = ANIMATION_CONFIG.initialDelay + Math.random() * ANIMATION_CONFIG.randomDelayRange;
        timers.push(setTimeout(() => {
            updateAnimationStates({ 
                showHighAlarmContainer: true, 
                isHighAlarmFlashing: true 
            });
        }, highAlarmDelay));
        
        // 低位报警容器动画
        const lowAlarmDelay = ANIMATION_CONFIG.initialDelay + Math.random() * ANIMATION_CONFIG.randomDelayRange;
        timers.push(setTimeout(() => {
            updateAnimationStates({ 
                showLowAlarmContainer: true, 
                isLowAlarmFlashing: true 
            });
        }, lowAlarmDelay));
        
        // 高位报警数值显示
        timers.push(setTimeout(() => {
            updateAnimationStates({ isHighAlarmFlashing: false });
            updateDisplayValues({ highAlarm: levelData.highAlarmValue });
        }, highAlarmDelay + ANIMATION_CONFIG.flashDuration));
        
        // 低位报警数值显示
        timers.push(setTimeout(() => {
            updateAnimationStates({ isLowAlarmFlashing: false });
            updateDisplayValues({ lowAlarm: levelData.lowAlarmValue });
        }, lowAlarmDelay + ANIMATION_CONFIG.flashDuration));
        
        // 容器显示
        timers.push(setTimeout(() => {
            updateAnimationStates({ showContainer: true });
        }, ANIMATION_CONFIG.containerShowDelay));
        
        // 初始水位动画
        timers.push(setTimeout(() => {
            updateAnimationStates({ isInitialAnimating: false });
            
            const airLevel = Math.random() * WATER_LEVEL_RANGE.range + WATER_LEVEL_RANGE.min;
            updateWaterStates({ waterLevel: airLevel });
            
            const targetHeight = levelData.totalHeight * (100 - airLevel) / 100;
            createWaterLevelAnimation(targetHeight);
            
            // 标记初始动画完成
            setTimeout(() => {
                updateAnimationStates({ hasCompletedInitial: true });
            }, ANIMATION_CONFIG.waterAnimationDuration);
        }, ANIMATION_CONFIG.initialAnimationDelay));
        
        return () => {
            timers.forEach(timer => clearTimeout(timer));
        };
    }, [levelData.highAlarmValue, levelData.lowAlarmValue, levelData.totalHeight, 
        updateAnimationStates, updateDisplayValues, updateWaterStates, createWaterLevelAnimation]);
    
    // 水位动画效果 - 优化版本
    useEffect(() => {
        if (!animationStates.hasCompletedInitial) return;
        
        const animateWaterLevel = () => {
            const newAirLevel = Math.random() * WATER_LEVEL_RANGE.range + WATER_LEVEL_RANGE.min;
            updateWaterStates({ waterLevel: newAirLevel });
            
            const newHeight = levelData.totalHeight * (100 - newAirLevel) / 100;
            createWaterLevelAnimation(newHeight, ANIMATION_CONFIG.cycleDuration);
        };
        
        const interval = setInterval(animateWaterLevel, ANIMATION_CONFIG.cycleInterval);
        return () => clearInterval(interval);
    }, [animationStates.hasCompletedInitial, levelData.totalHeight, 
        updateWaterStates, createWaterLevelAnimation]);
    
    // 监听水位变化，判断报警状态 - 优化版本
    useEffect(() => {
        const isHighAlarm = waterStates.heightValue >= levelData.highAlarmValue;
        const isLowAlarm = waterStates.heightValue <= levelData.lowAlarmValue;
        
        if (isHighAlarm !== waterStates.isHighAlarm || isLowAlarm !== waterStates.isLowAlarm) {
            updateWaterStates({ isHighAlarm, isLowAlarm });
        }
    }, [waterStates.heightValue, levelData.highAlarmValue, levelData.lowAlarmValue, 
        waterStates.isHighAlarm, waterStates.isLowAlarm, updateWaterStates]);

    // 🎯 性能优化：组件卸载时清理所有动画
    useEffect(() => {
        return () => {
            animationTimelines.current.forEach(tl => tl && tl.kill());
            animationTimelines.current = [];
        };
    }, []);

    // 🎯 极致性能优化：纯GPU加速的transform方案，完全避免重绘
    const containerTransition = useMemo(() => 
        animationStates.isInitialAnimating ? 'transition-none' : 'transition-transform duration-[3000ms] ease-out'
    , [animationStates.isInitialAnimating]);

    // 🎯 性能优化：水位分割线专用transition，确保top属性动画同步
    const waterLineTransition = useMemo(() => 
        animationStates.isInitialAnimating ? 'transition-none' : 'transition-top duration-[3000ms] ease-out'
    , [animationStates.isInitialAnimating]);

    // 🎯 性能优化：预计算transform值，避免重复计算
    const transformValues = useMemo(() => ({
        airMaskScale: waterStates.waterLevel / 100, // 空气层遮罩缩放比例
    }), [waterStates.waterLevel]);

    return (
        <div className="w-full h-full flex items-stretch p-[10px] relative overflow-hidden box-border">
            {/* 背景网格线 */}
            <div 
                className="absolute inset-0 opacity-[0.1]" 
                style={{
                    backgroundImage: CONTAINER_STYLES.gridPattern,
                    backgroundSize: '20px 20px'
                }}
            />
            
            {/* 主容器 */}
            <div className="w-full h-full flex items-center gap-[20px] relative">
                
                {/* 左侧水位参数显示区域 */}
                <div className="w-[180px] h-full shrink-0 relative">
                    {/* 装饰线 */}
                    <div className={`absolute top-[0px] left-[0px] w-full h-[2px] bg-gradient-to-r from-transparent via-[#FF6B6B] to-transparent z-10 ${
                        animationStates.showLeftLines ? 'animate-line-expand-flash' : 'w-[0px] opacity-0'
                    }`} />
                    <div className={`absolute bottom-[0px] left-[0px] w-full h-[2px] bg-gradient-to-r from-transparent via-[#FFA500] to-transparent z-10 ${
                        animationStates.showLeftLines ? 'animate-line-expand-flash-delay' : 'w-[0px] opacity-0'
                    }`} />
                    
                    {/* 上半部分容器 - 高位报警信息 */}
                    <div className="absolute top-[0px] left-[0px] w-full h-[50%] flex items-center justify-center">
                        <div className={`transition-opacity duration-300 ${
                            animationStates.showHighAlarmContainer ? 
                                (animationStates.isHighAlarmFlashing ? 'animate-left-container-flash' : 'opacity-100') 
                                : 'opacity-0'
                        }`}>
                            <div className="text-center">
                                <div className="text-[12px] text-[#909999] font-['DingTalkJinBuTi',sans-serif] mb-[4px] tracking-wide text-center">
                                    高位报警
                                </div>
                                <div className="flex items-baseline justify-center space-x-[4px]">
                                    <NumberFlow 
                                        value={displayValues.highAlarm}
                                        format={{ 
                                            minimumFractionDigits: 1, 
                                            maximumFractionDigits: 1 
                                        }}
                                        className={`text-[36px] text-[#FF6B6B] font-['ChakraPetch-Light',sans-serif] font-bold leading-none ${
                                            waterStates.isHighAlarm ? 'animate-breathe' : ''
                                        }`}
                                        suffix=" m"
                                    />
                                    <span 
                                        className="text-[24px] font-bold leading-none"
                                        style={{ color: '#FF6B6B' }}
                                    >
                                        {getTrendArrow(levelData.highAlarmTrend)}
                                    </span>
                                </div>
                                <div className="flex items-center justify-center space-x-[8px] mt-[6px]">
                                    <div className={`w-[8px] h-[8px] bg-[#FF6B6B] rounded-full ${
                                        waterStates.isHighAlarm ? 'animate-dot-blink' : 'animate-pulse'
                                    }`} />
                                    <span className="text-[10px] text-[#909999] font-['DingTalkJinBuTi',sans-serif]">
                                        HIGH ALARM
                                    </span>
                                </div>
                                <div className="text-[10px] text-[#606060] font-['DingTalkJinBuTi',sans-serif] mt-[2px] text-center">
                                    {waterStates.isHighAlarm ? '报警中' : '启用'}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    {/* 下半部分容器 - 低位报警信息 */}
                    <div className="absolute bottom-[0px] left-[0px] w-full h-[50%] flex items-center justify-center">
                        <div className={`transition-opacity duration-300 ${
                            animationStates.showLowAlarmContainer ? 
                                (animationStates.isLowAlarmFlashing ? 'animate-left-container-flash' : 'opacity-100') 
                                : 'opacity-0'
                        }`}>
                            <div className="text-center">
                                <div className="text-[12px] text-[#909999] font-['DingTalkJinBuTi',sans-serif] mb-[4px] tracking-wide text-center">
                                    低位报警
                                </div>
                                <div className="flex items-baseline justify-center space-x-[4px]">
                                    <NumberFlow 
                                        value={displayValues.lowAlarm}
                                        format={{ 
                                            minimumFractionDigits: 1, 
                                            maximumFractionDigits: 1 
                                        }}
                                        className={`text-[36px] text-[#FFA500] font-['ChakraPetch-Light',sans-serif] font-bold leading-none ${
                                            waterStates.isLowAlarm ? 'animate-breathe' : ''
                                        }`}
                                        suffix=" m"
                                    />
                                    <span 
                                        className="text-[24px] font-bold leading-none"
                                        style={{ color: getTrendColor(levelData.lowAlarmTrend) }}
                                    >
                                        {getTrendArrow(levelData.lowAlarmTrend)}
                                    </span>
                                </div>
                                <div className="flex items-center justify-center space-x-[8px] mt-[6px]">
                                    <div className={`w-[8px] h-[8px] bg-[#FFA500] rounded-full ${
                                        waterStates.isLowAlarm ? 'animate-dot-blink-low' : 'animate-pulse'
                                    }`} />
                                    <span className="text-[10px] text-[#909999] font-['DingTalkJinBuTi',sans-serif]">
                                        LOW ALARM
                                    </span>
                                </div>
                                <div className="text-[10px] text-[#606060] font-['DingTalkJinBuTi',sans-serif] mt-[2px] text-center">
                                    {waterStates.isLowAlarm ? '报警中' : '启用'}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                {/* 右侧水池容器 */}
                <div className={`flex-1 h-full relative border-dashed border-[1px] border-[#BFBEC4] overflow-hidden transition-opacity duration-300 ${
                    animationStates.showContainer ? 
                        (animationStates.isInitialAnimating ? 'animate-container-flash' : 'opacity-100') 
                        : 'opacity-0'
                }`}>
                    
                    {/* 水池总高度显示 */}
                    <div className="absolute top-[0] left-[0] text-[12px] text-white font-['DingTalkJinBuTi',sans-serif] z-50 bg-black/60 px-[8px] py-[2px] rounded-[4px]">
                        总高: {levelData.totalHeight}m
                    </div>
                    
                    {/* 🎯 GPU加速优化：固定背景+动态遮罩方案 */}
                    <div className="absolute inset-0 w-full h-full overflow-hidden">
                        {/* 底层完整的水位背景 - 条纹效果 */}
                        <div 
                            className="absolute inset-0 w-full h-full"
                            style={{
                                background: CONTAINER_STYLES.backgroundStripes
                            }}
                        />
                        
                        {/* 中间虚线 - 完整高度 */}
                        <div 
                            className="absolute w-[1px] h-full"
                            style={{
                                left: '50%',
                                transform: 'translateX(-50%)',
                                backgroundImage: 'repeating-linear-gradient(to bottom, white 0px, white 5px, transparent 5px, transparent 10px)'
                            }}
                        />
                        
                        {/* 🚀 极致性能优化：纯GPU加速空气层遮罩 - 使用scaleY，完全避免重绘 */}
                        <div 
                            className={`absolute top-0 left-0 w-full h-full will-change-transform ${containerTransition}`}
                            style={{
                                transform: `scaleY(${transformValues.airMaskScale})`,
                                transformOrigin: 'top',
                                backfaceVisibility: 'hidden',
                                backgroundColor: '#141414', // 使用页面主背景色遮挡斜条纹
                                zIndex: 10
                            }}
                        />
                    </div>
                    
                    {/* 🚀 性能优化：水位分割线容器 - 使用专用transition确保同步动画 */}
                    <div 
                        className={`absolute left-0 right-0 w-full ${waterLineTransition}`}
                        style={{ 
                            height: 'auto',
                            top: `${waterStates.waterLevel}%`,
                            zIndex: 15
                        }}
                    >
                        {/* 水位线 */}
                        <div className="w-full h-[2px] bg-[#2DB4CA] mb-[1px]" />
                        <div className="w-full h-[2px] bg-[#E6E5EB]" />
                        
                        {/* 高度显示容器 */}
                        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 px-[10px] py-[0] rounded-[30px] z-30" 
                             style={{backgroundColor: '#ffffff'}} >
                            <div className="absolute inset-[-8px] bg-white rounded-[30px] z-25" />
                            <NumberFlow 
                                value={waterStates.heightValue}
                                format={{ 
                                    minimumFractionDigits: 1, 
                                    maximumFractionDigits: 1 
                                }}
                                className="text-[14px] font-['DingTalkJinBuTi'] font-bold relative z-35"
                                style={{color: '#000000'}}
                                suffix=" m"
                            />
                        </div>
                    </div>
                    
                    {/* 🎯 固定位置报警线：高位报警线 - 静态定位无需GPU加速 */}
                    <div 
                        className={`absolute left-[0px] right-[0px] w-full h-[2px] z-20 ${
                            waterStates.isHighAlarm ? 'animate-pulse' : ''
                        }`}
                        style={{ 
                            top: `${alarmLinePositions.high}%`,
                            borderTop: '2px dashed rgba(255, 107, 107, 0.4)'
                        }}
                    >
                        <div className="absolute right-[5px] top-[-18px] text-[10px] text-[#FF6B6B] font-['DingTalkJinBuTi',sans-serif] bg-white/80 px-[4px] rounded">
                            高位报警
                        </div>
                    </div>
                    
                    {/* 🎯 固定位置报警线：低位报警线 - 静态定位无需GPU加速 */}
                    <div 
                        className={`absolute left-[0px] right-[0px] w-full h-[2px] z-20 ${
                            waterStates.isLowAlarm ? 'animate-pulse' : ''
                        }`}
                        style={{ 
                            top: `${alarmLinePositions.low}%`,
                            borderTop: '2px dashed rgba(255, 165, 0, 0.4)'
                        }}
                    >
                        <div className="absolute right-[5px] top-[-18px] text-[10px] text-[#FFA500] font-['DingTalkJinBuTi',sans-serif] bg-white/80 px-[4px] rounded">
                            低位报警
                        </div>
                    </div>
                    
                </div>
            </div>
            
            {/* 装饰线 */}
            <div className="absolute top-0 left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-[#23A861] to-transparent opacity-40" />
            <div className="absolute bottom-0 left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-[#23A861] to-transparent opacity-40" />
            
            {/* 🎯 性能优化：简化的CSS动画定义 */}
            <style dangerouslySetInnerHTML={{
                __html: `
                @keyframes containerFlash {
                    0%, 100% { opacity: 1; }
                    20%, 40%, 60%, 80% { opacity: 0.1; }
                    30%, 50%, 70%, 90% { opacity: 1; }
                }
                .animate-container-flash { animation: containerFlash 0.8s ease-out; }
                
                @keyframes lineExpandFlash {
                    0% { width: 0%; left: 50%; transform: translateX(-50%); opacity: 0; }
                    20% { width: 100%; left: 0%; transform: translateX(0%); opacity: 1; }
                    25%, 35%, 45%, 55%, 65%, 75%, 85% { opacity: 1; }
                    30%, 40%, 50%, 60%, 70%, 80%, 90% { opacity: 0.3; }
                    100% { width: 100%; left: 0%; transform: translateX(0%); opacity: 0.8; }
                }
                .animate-line-expand-flash { animation: lineExpandFlash 1.5s ease-out forwards; }
                
                @keyframes lineExpandFlashDelay {
                    0%, 15% { width: 0%; left: 50%; transform: translateX(-50%); opacity: 0; }
                    35% { width: 100%; left: 0%; transform: translateX(0%); opacity: 1; }
                    40%, 50%, 60%, 70%, 80%, 90% { opacity: 1; }
                    45%, 55%, 65%, 75%, 85%, 95% { opacity: 0.3; }
                    100% { width: 100%; left: 0%; transform: translateX(0%); opacity: 0.8; }
                }
                .animate-line-expand-flash-delay { animation: lineExpandFlashDelay 1.5s ease-out forwards; }
                
                @keyframes leftContainerFlash {
                    0%, 100% { opacity: 1; }
                    25%, 35%, 45%, 55%, 65%, 75%, 85%, 95% { opacity: 1; }
                    30%, 40%, 50%, 60%, 70%, 80%, 90% { opacity: 0.1; }
                }
                .animate-left-container-flash { animation: leftContainerFlash 0.8s ease-out; }
                
                @keyframes breatheGlow {
                    0%, 100% { opacity: 0.4; transform: scale(1); filter: brightness(1); }
                    50% { opacity: 1; transform: scale(1.02); filter: brightness(1.3); }
                }
                .animate-breathe { animation: breatheGlow 1.5s ease-in-out infinite; }
                
                @keyframes dotBlink {
                    0%, 100% { opacity: 0.3; transform: scale(1); box-shadow: 0 0 0 0 currentColor; }
                    50% { opacity: 1; transform: scale(1.2); box-shadow: 0 0 0 4px rgba(255, 107, 107, 0.3); }
                }
                .animate-dot-blink { animation: dotBlink 1s ease-in-out infinite; }
                
                @keyframes dotBlinkLow {
                    0%, 100% { opacity: 0.3; transform: scale(1); box-shadow: 0 0 0 0 currentColor; }
                    50% { opacity: 1; transform: scale(1.2); box-shadow: 0 0 0 4px rgba(255, 165, 0, 0.3); }
                }
                .animate-dot-blink-low { animation: dotBlinkLow 1s ease-in-out infinite; }
                `
            }} />
        </div>
    );
});

// 🎯 性能优化：添加显示名称便于调试
LevelMeterDetail.displayName = 'LevelMeterDetail';

export default LevelMeterDetail; 