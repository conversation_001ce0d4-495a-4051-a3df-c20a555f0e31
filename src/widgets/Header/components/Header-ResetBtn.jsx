import React, { useEffect, useRef, useCallback, memo, useState } from 'react';
import { gsap } from 'gsap';
import AnimationUtils from '../../../utils/animations';

// 定义颜色常量，保持与其他按钮一致
const ICON_COLOR = 'rgba(90,170,180,0.8)';
const ICON_COLOR_HOVER_BG = 'rgba(90,170,180,0.1)';
const ICON_COLOR_HOVER_BORDER = 'rgba(130,210,220,1)'; // 悬停时边框高亮颜色
const ROTATION_ANIMATION_DEGREES = 360; // 旋转动画的角度
const ROTATION_ANIMATION_DURATION = 0.8; // 旋转动画的持续时间

const HeaderResetBtn = () => {
    const btnRef = useRef(null);
    const [isHovered, setIsHovered] = useState(false); // 追踪鼠标悬停状态
    const rotationAnimationRef = useRef(null); // 用于存储旋转动画的引用

    // 初始化按钮动画效果
    useEffect(() => {
        const btnElement = btnRef.current;
        if (!btnElement) return;

        const sequence = [
            { opacity: 0.8, duration: 0.15 },
            { opacity: 0.2, duration: 0.1 },
            { opacity: 0.8, duration: 0.1 },
            { opacity: 0.4, duration: 0.1 },
            { opacity: 0.8, duration: 0.1 },
            { opacity: 0.1, duration: 0.1 },
            { opacity: 0.7, duration: 0.1 },
            { opacity: 0.4, duration: 0.1 },
            { opacity: 1, duration: 0.1 }
        ];

        const tl = AnimationUtils.createCustomBlinkEffect(btnRef, sequence);
        
        return () => {
            tl?.kill();
        };
    }, []);

    // 鼠标按下缩小效果
    const handleMouseDown = useCallback(() => {
        if (btnRef.current) {
            gsap.to(btnRef.current, { scale: 0.9, duration: 0.1 });
        }
    }, []);

    // 鼠标松开或移开恢复效果
    const handleMouseUpOrLeave = useCallback(() => {
        if (btnRef.current) {
            gsap.to(btnRef.current, { scale: 1, duration: 0.1 });
        }
    }, []);

    // 执行重置布局功能
    const handleResetLayout = useCallback(() => {
        // 触发网格布局的重置功能
        const event = new CustomEvent('resetGridLayout');
        window.dispatchEvent(event);
        
        // console.log('🔄 重置网格布局'); // 已注释掉，减少控制台输出
    }, []);

    // 旋转动画函数
    const animateRotation = useCallback((targetRef, isEntering) => {
        if (rotationAnimationRef.current) {
            rotationAnimationRef.current.kill(); // 先停止任何正在进行的旋转动画
        }
        
        const icon = targetRef?.querySelector('.reset-icon');
        if (!icon) return;

        if (isEntering) {
            rotationAnimationRef.current = gsap.to(icon, {
                rotation: ROTATION_ANIMATION_DEGREES,
                duration: ROTATION_ANIMATION_DURATION,
                ease: 'power1.inOut',
                repeat: -1,
                yoyo: false
            });
        } else {
            // 停止动画并回到初始位置
            gsap.to(icon, {
                rotation: 0,
                duration: ROTATION_ANIMATION_DURATION / 2,
                ease: 'power1.out'
            });
        }
    }, []);

    // 鼠标移入处理
    const handleMouseEnter = useCallback(() => {
        setIsHovered(true);
        if (btnRef.current) {
            gsap.to(btnRef.current, { borderColor: ICON_COLOR_HOVER_BORDER, duration: 0.2 });
            animateRotation(btnRef.current, true);
        }
    }, [animateRotation]);

    // 鼠标移出处理
    const handleMouseLeave = useCallback(() => {
        setIsHovered(false);
        if (btnRef.current) {
            gsap.to(btnRef.current, { borderColor: ICON_COLOR, duration: 0.2 });
            animateRotation(btnRef.current, false);
        }
        // 确保移开时恢复按钮大小
        if (btnRef.current?.style.transform === 'scale(0.9)') {
             gsap.to(btnRef.current, { scale: 1, duration: 0.1 });
        }
    }, [animateRotation]);

    return (
        <div
            ref={btnRef}
            title="重置布局"
            className={`w-[23px] h-[23px] cursor-pointer flex items-center justify-center border transition-colors duration-300 ease-in-out hover:bg-[${ICON_COLOR_HOVER_BG}] relative`}
            style={{ borderColor: ICON_COLOR }} // 初始边框颜色
            onMouseDown={handleMouseDown}
            onMouseUp={handleMouseUpOrLeave}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
            onClick={handleResetLayout}
        >
            {/* 重置图标：圆形箭头 */}
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" className="reset-icon">
                {/* 圆弧箭头 */}
                <path 
                    d="M4 8C4 5.79 5.79 4 8 4C10.21 4 12 5.79 12 8C12 10.21 10.21 12 8 12C7 12 6.1 11.6 5.4 11" 
                    stroke={ICON_COLOR} 
                    strokeWidth="1" 
                    fill="none"
                />
                {/* 箭头头部 */}
                <polyline 
                    points="5,9.5 5.4,11 4,11.5" 
                    fill="none" 
                    stroke={ICON_COLOR} 
                    strokeWidth="1"
                />
            </svg>
        </div>
    );
};

export default memo(HeaderResetBtn); 