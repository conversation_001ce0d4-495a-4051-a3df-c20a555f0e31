import React, { useState, useEffect, useRef, useMemo } from 'react';
import { gsap } from 'gsap';

// 为Title-Bar添加样式
if (typeof document !== 'undefined' && !document.getElementById('title-bar-styles')) {
    const styleElement = document.createElement('style');
    styleElement.id = 'title-bar-styles';
    styleElement.textContent = `
        .title-bar-root {
            /* 用于外部控制居中等，如果需要 */
            opacity: 1;
        }
        .title-bar-container {
            position: relative;
            height: 25px; /* 固定高度 */
            background-color: rgba(24, 82, 88, 0.5);
            display: flex; 
            align-items: center;
            justify-content: center; 
            overflow: visible; /* 改为visible，让线条在变换时可以显示 */
            margin: 0 auto; 
            box-sizing: border-box;
            transform-origin: center; /* 确保变换从中心开始 */
            opacity: 1; /* 默认可见，由GSAP控制显示 */
            /* 不预设transform:scaleX(0)，由GSAP动态控制 */
        }
        .title-bar-line {
            position: absolute;
            top: 0;
            bottom: 0;
            width: 3px; /* 线条厚度 */
            background-color: #4FA3AA;
            opacity: 1; /* 确保线条默认可见 */
        }
        .line-left {
            left: 0;
        }
        .line-right {
            right: 0;
        }
        .title-text-area {
            font-family: 'ChakraPetch-Light', sans-serif;
            font-size: 13px;
            letter-spacing: 2px;
            color: #7EFCFA; /* 文字颜色 */
            /* text-shadow: 0 0 2px rgba(62, 213, 233, 0.5); */
            white-space: nowrap; /* 防止文字换行 */
            /* 重要：不要预设opacity为0，由GSAP控制 */
            display: flex; /* 让内部span能正确排列 */
            visibility: visible; /* 确保可见 */
        }
        .title-text-area span {
            display: inline-block; /* 使每个字块状化，能应用transform */
            /* 不要预设opacity为0，由GSAP控制 */
            position: relative; /* 为了可能的transform动画 */
            visibility: visible; /* 确保可见 */
        }
    `;
    document.head.appendChild(styleElement);
}

/**
 * 标题栏组件 - DIV + GSAP动画 实现版本
 * @returns {JSX.Element}
 */
const TitleBar = React.memo(() => {
    const titleText = "津惠隆智慧水务数字孪生平台";
    const chars = useMemo(() => titleText.split(''), [titleText]);
    
    // Refs
    const containerRef = useRef(null);
    const textMeasureRef = useRef(null);
    const leftLineRef = useRef(null);
    const rightLineRef = useRef(null);
    const textAreaRef = useRef(null);
    const charRefsArray = useRef([]);
    
    // 布局参数
    const lineThickness = 2; // px
    const sidePadding = 20; // px
    
    // 动画阶段
    const [animationState, setAnimationState] = useState('initial');
    
    // 清理字符refs
    useEffect(() => {
        charRefsArray.current = new Array(chars.length);
    }, [chars.length]);
    
    // 主动画序列
    useEffect(() => {
        if (!gsap) {
            console.error('[TitleBar] GSAP not found, please install it: npm install gsap');
            return;
        }
        
        // 确保所有DOM元素已挂载
        if (
            animationState === 'initial' && 
            containerRef.current && 
            leftLineRef.current && 
            rightLineRef.current && 
            textMeasureRef.current &&
            textAreaRef.current
        ) {
            // console.log('[TitleBar] All refs ready, starting animation'); // 已注释掉，减少控制台输出
            
            // 1. 测量文字宽度
            const measureAndInitialize = async () => {
                // 等待字体加载
                try {
                    await document.fonts.ready;
                    // console.log("[TitleBar] Fonts loaded"); // 已注释掉，减少控制台输出
                } catch (error) {
                    console.warn("[TitleBar] Font loading error:", error);
                }
                
                // 测量文字宽度 - 添加额外的安全检查
                let textWidth = 0;
                try {
                    if (textMeasureRef.current) {
                        // console.log("[TitleBar] Measuring text with element:", textMeasureRef.current); // 已注释掉，减少控制台输出
                        textWidth = textMeasureRef.current.offsetWidth || 0;
                        // console.log("[TitleBar] Raw text width:", textWidth); // 已注释掉，减少控制台输出
                    } else {
                        console.warn("[TitleBar] textMeasureRef.current is null");
                    }
                } catch (error) {
                    console.error("[TitleBar] Error measuring text width:", error);
                }
                
                // 如果测量值异常，使用估算值
                const calculatedTextWidth = textWidth > 0 ? textWidth : titleText.length * 15;
                const finalContainerWidth = calculatedTextWidth + sidePadding + (lineThickness * 2);
                // console.log("[TitleBar] Final container width:", finalContainerWidth); // 已注释掉，减少控制台输出
                
                // 设置容器最终宽度
                if (containerRef.current) {
                    containerRef.current.style.width = `${finalContainerWidth}px`;
                }
                
                // 初始化元素状态 - 添加安全检查
                if (containerRef.current) {
                    gsap.set(containerRef.current, { 
                        scaleX: 0,
                        autoAlpha: 1
                    });
                }
                
                const lineRefs = [leftLineRef.current, rightLineRef.current].filter(Boolean);
                if (lineRefs.length > 0) {
                    gsap.set(lineRefs, {
                        left: '50%',
                        xPercent: -50,
                        autoAlpha: 1
                    });
                }
                
                if (textAreaRef.current) {
                    gsap.set(textAreaRef.current, {
                        autoAlpha: 0  // 文字区域初始隐藏
                    });
                }
                
                charRefsArray.current.forEach(ref => {
                    if (ref) {
                        gsap.set(ref, { autoAlpha: 0 });
                    }
                });
                
                // console.log("[TitleBar] Starting animation sequence"); // 已注释掉，减少控制台输出
                startAnimationSequence();
            };
            
            // 2. 动画序列
            const startAnimationSequence = () => {
                // 主时间线
                const mainTimeline = gsap.timeline({
                    onComplete: () => {/* console.log("[TitleBar] Main timeline completed"); */} // 已注释掉，减少控制台输出
                });
                
                // 阶段2: 线条移动到两侧
                const repositionTimeline = gsap.timeline({
                    onComplete: () => {/* console.log("[TitleBar] Line repositioning completed"); */} // 已注释掉，减少控制台输出
                });
                
                repositionTimeline
                    .to(leftLineRef.current, { left: 0, xPercent: 0, duration: 0.3, ease: "power2.out" }, 0)
                    .to(rightLineRef.current, { left: "auto", right: 0, xPercent: 0, duration: 0.3, ease: "power2.out" }, 0);
                
                // 阶段3: 从中间向两侧展开
                const expandTimeline = gsap.timeline({
                    onComplete: () => {/* console.log("[TitleBar] Container expansion completed"); */} // 已注释掉，减少控制台输出
                });
                
                expandTimeline.to(containerRef.current, {
                    scaleX: 1,
                    duration: 0.7,
                    ease: "power3.out"
                });
                
                // 阶段4: 文字动画
                const textTimeline = gsap.timeline({ 
                    onStart: () => {/* console.log("[TitleBar] Text animation started"); */}, // 已注释掉，减少控制台输出
                    onComplete: () => {/* console.log("[TitleBar] Text animation completed"); */} // 已注释掉，减少控制台输出
                });
                
                // 初始设置文字区域不可见，为闪烁做准备
                gsap.set(textAreaRef.current, { autoAlpha: 0 });

                // 整体文字区域闪烁效果
                textTimeline
                    .to(textAreaRef.current, { autoAlpha: 0.3, duration: 0.05, ease: "steps(1)" }) // 快速亮起
                    .to(textAreaRef.current, { autoAlpha: 0.4, duration: 0.07, ease: "steps(1)" }) // 快速熄灭
                    .to(textAreaRef.current, { autoAlpha: 0.2, duration: 0.06, ease: "steps(1)" }) // 再次快速亮起
                    .to(textAreaRef.current, { autoAlpha: 0.5, duration: 0.07, ease: "steps(1)" }) // 再次快速熄灭
                    .to(textAreaRef.current, { autoAlpha: 1, duration: 0.3, ease: "power1.in" }); // 最终稳定显示
                
                mainTimeline
                    .add(repositionTimeline)
                    .add(expandTimeline, "+=0.3") // 延迟0.3秒后播放展开动画
                    .add(textTimeline);
                
                mainTimeline.play();
                setAnimationState('animating');
            };
            
            measureAndInitialize();
        }
    }, [animationState, chars, sidePadding, titleText.length]);
    
    return (
        <div className="title-bar-root">
            <div ref={containerRef} className="title-bar-container">
                <div ref={leftLineRef} className="title-bar-line line-left"></div>
                <div ref={rightLineRef} className="title-bar-line line-right"></div>
                <div ref={textAreaRef} className="title-text-area">
                    {/* 测量用的隐藏文本 */}
                    <span ref={textMeasureRef} style={{ visibility: 'hidden', position: 'absolute' }}>
                        {titleText}
                    </span>
                    
                    {/* 动画字符 */}
                    {chars.map((char, index) => (
                        <span 
                            key={index}
                            ref={el => charRefsArray.current[index] = el}
                        >
                            {char}
                        </span>
                    ))}
                </div>
            </div>
        </div>
    );
});

// 设置组件显示名，方便调试
TitleBar.displayName = 'TitleBar';

export default TitleBar;