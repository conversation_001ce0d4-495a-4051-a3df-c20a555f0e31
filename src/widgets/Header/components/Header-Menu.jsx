import React, { useState, useEffect, useRef, useCallback, memo, useMemo } from 'react';
import { gsap } from 'gsap';

// 添加全局样式以消除所有按钮的focus和hover样式
if (typeof document !== 'undefined' && !document.getElementById('menu-button-styles')) {
    const styleElement = document.createElement('style');
    styleElement.id = 'menu-button-styles';
    styleElement.textContent = `
        .header-menu-btn:focus, 
        .header-menu-btn:hover, 
        .header-menu-btn:active,
        .header-menu-btn:focus-visible,
        .header-menu-btn:focus-within {
            outline: none !important;
            box-shadow: none !important;
            -webkit-box-shadow: none !important;
            -moz-box-shadow: none !important;
            /* border-color: inherit !important; */ /* 移除这条规则，避免冲突 */
        }
        
        /* 覆盖任何可能的浏览器默认焦点样式 */
        .header-menu-btn:focus {
            outline: 0 !important;
        }
        
        /* 确保呼吸灯按钮永远使用动画样式，不被焦点覆盖 */
        .breathing-btn {
            outline: 0 !important;
            /* border-color: rgba(79, 163, 170, 0.5); */ /* 移除 !important */
        }
    `;
    document.head.appendChild(styleElement);
}

// 辅助组件：带斜边效果的按钮
const SlantedButton = memo(({ children, onClick, isActive }) => {
    const buttonRef = useRef(null);
    const buttonContainerRef = useRef(null);
    const slantedLineRef = useRef(null);
    const lineRef = useRef(null);
    
    // 边线样式
    const topEdgeRef = useRef(null);
    const rightEdgeRef = useRef(null);
    const leftEdgeRef = useRef(null);
    const bottomEdgeRef = useRef(null); // 添加底部边线引用
    const edgeAnimationRef = useRef(null);
    
    // 创建科技感启动效果
    useEffect(() => {
        const initialIsActive = isActive; // 捕获初始isActive状态用于启动动画
        const startupDelay = Math.random() * 1.5; // 随机延迟0-1.5秒
        const startupDuration = 0.2 + Math.random() * 0.3; // 随机持续时间0.2-0.5秒
        
        // 所有边线元素
        const edgeElements = [
            topEdgeRef.current, 
            rightEdgeRef.current, 
            leftEdgeRef.current, 
            bottomEdgeRef.current,
            slantedLineRef.current
        ].filter(el => el); // 过滤掉未初始化的元素
        
        // 将按钮本身也加入动画控制
        const allElementsToAnimate = [...edgeElements];
        if (buttonRef.current) {
            allElementsToAnimate.push(buttonRef.current);
        }
        
        if (allElementsToAnimate.length === 0) return; // 如果没有任何元素可以动画，则返回
        
        // 设置初始状态：所有元素（边线、按钮）完全透明
        gsap.set(allElementsToAnimate, { opacity: 0 });
        // 单独为按钮设置一个非常暗淡的初始背景和文字色
        // 即便它在opacity:0时不可见，这是为了在opacity变为1时，这些颜色能平滑过渡或被onStart回调覆盖
        if (buttonRef.current) {
            gsap.set(buttonRef.current, { 
                backgroundColor: 'rgba(10, 25, 35, 0.1)', 
                color: 'rgba(103, 200, 208, 0.1)'
            });
        }
        
        // 整体闪烁效果时间线
        const timeline = gsap.timeline({ delay: startupDelay });
        
        // 第一次闪烁 - 快速亮起
        timeline.to(allElementsToAnimate, { // 作用于所有动画元素
            opacity: 1,
            duration: startupDuration / 2,
            stagger: 0.03,
            ease: 'power1.in',
            onStart: () => { // 确保在动画开始时设置正确的亮色
                // 边框使用未选中状态的较亮版本
                edgeElements.forEach(el => gsap.set(el, { backgroundColor: 'rgba(90, 170, 180, 0.8)' })); // 未选中边框的亮版
                if (buttonRef.current) {
                    gsap.set(buttonRef.current, {
                        backgroundColor: 'rgba(25, 50, 70, 0.95)', // 未选中按钮背景的亮版
                        color: 'rgba(120, 220, 230, 1)'       // 未选中按钮文字的亮版
                    });
                }
            }
        })
        
        // 第二次闪烁 - 短暂消失
        .to(allElementsToAnimate, { // 作用于所有动画元素
            opacity: 0.2,
            duration: startupDuration / 3,
            stagger: 0.01,
            ease: 'power1.out',
            onStart: () => { // 确保在动画开始时设置正确的暗色 (这些颜色已经是基于未选中状态的暗色，保持不变)
                edgeElements.forEach(el => gsap.set(el, { backgroundColor: 'rgba(79, 163, 170, 0.5)' }));
                if (buttonRef.current) {
                    gsap.set(buttonRef.current, {
                        backgroundColor: 'rgba(10, 25, 35, 0.5)', 
                        color: 'rgba(79, 163, 170, 0.6)'
                    });
                }
            }
        })
        
        // 第三次闪烁 - 最终亮起并保持
        .to(allElementsToAnimate, { // 作用于所有动画元素
            opacity: 1,
            duration: startupDuration,
            stagger: 0.05,
            ease: 'sine.inOut',
            onStart: () => {
                if (initialIsActive) {
                    // 如果按钮最终是激活状态，这里使用一个明亮的"激活闪烁"颜色
                    edgeElements.forEach(el => gsap.set(el, { backgroundColor: 'rgba(126, 252, 250, 0.9)' })); // 激活状态闪烁边框
                    if (buttonRef.current) {
                        gsap.set(buttonRef.current, {
                            backgroundColor: 'rgba(30, 85, 95, 0.95)', // 激活状态闪烁背景
                            color: 'rgba(220, 255, 255, 1)'          // 激活状态闪烁文字 (亮白偏青)
                        });
                    }
                } else {
                    // 如果按钮最终是非激活状态，这里直接使用最终的"非激活"颜色，避免再次高亮
                    edgeElements.forEach(el => gsap.set(el, { backgroundColor: 'rgba(70,150,160,0.6)' })); // 非激活状态最终边框色
                    if (buttonRef.current) {
                        gsap.set(buttonRef.current, {
                            backgroundColor: 'rgba(10, 25, 35, 0.85)', // 非激活状态最终背景色
                            color: '#67C8D0'                         // 非激活状态最终文字颜色
                        });
                    }
                }
            }
        })
        
        // 最终状态 - 恢复正常亮度，基于组件挂载时的isActive状态
        .to(allElementsToAnimate, { // 这个 .to 主要是为了时间上的延续和确保opacity最终为1
            opacity: 1, //确保所有元素最终是可见的
            duration: 0.5, // 额外0.5秒保持，然后onComplete确保最终精确颜色
            stagger: 0.02,
            ease: 'sine.out', 
            onComplete: () => { // 动画完成时设置最终样式
                // 这个onComplete现在主要是为了确保在动画序列结束后，按钮处于绝对正确的最终状态
                // 特别是对于激活状态，它会将闪烁的亮色调整回标准的激活色
                if (initialIsActive) {
                    edgeElements.forEach(el => gsap.set(el, { 
                        backgroundColor: 'rgba(79, 163, 170, 0.5)', // 标准激活边框色
                        opacity: 1 
                    }));
                    if (buttonRef.current) {
                        gsap.set(buttonRef.current, {
                            backgroundColor: 'rgba(24, 82, 88, 0.5)', // 标准激活背景色
                            color: '#ffffff', // 标准激活文字颜色
                            opacity: 1
                        });
                    }
                } else {
                    // 对于非激活状态，颜色已在上一个步骤的onStart中设置为最终状态
                    // 这里再次set可以作为双重保险，确保状态正确
                    edgeElements.forEach(el => gsap.set(el, { 
                        backgroundColor: 'rgba(70,150,160,0.6)',
                        opacity: 1
                    }));
                    if (buttonRef.current) {
                        gsap.set(buttonRef.current, {
                            backgroundColor: 'rgba(10, 25, 35, 0.85)',
                            color: '#67C8D0',
                            opacity: 1
                        });
                    }
                }
            }
        });
        
        // 按钮文字闪烁效果已被整合，不再需要独立执行
        
    }, []); // 确保此启动动画仅在组件挂载时执行一次
    
    // 监听isActive状态变化，处理按钮从激活到非激活的转换
    useEffect(() => {
        // 清理所有现有动画以避免冲突
        if (lineRef.current) {
            lineRef.current.kill();
            lineRef.current = null;
        }
        if (edgeAnimationRef.current) {
            edgeAnimationRef.current.kill();
            edgeAnimationRef.current = null;
        }
        
        if (!isActive) {
            // 当按钮从激活变为非激活状态时，恢复斜边线为普通状态
            gsap.to(slantedLineRef.current, {
                opacity: 1,
                backgroundColor: 'rgba(70,150,160,0.6)',
                duration: 0.3,
                ease: 'power2.out'
            });
            
            // 恢复按钮的背景和文字颜色
            gsap.to(buttonRef.current, {
                backgroundColor: 'rgba(10, 25, 35, 0.85)',
                color: '#67C8D0',
                duration: 0.3,
                ease: 'power2.out'
            });
            
            // 恢复边线颜色
            gsap.to([topEdgeRef.current, rightEdgeRef.current, leftEdgeRef.current, bottomEdgeRef.current], {
                backgroundColor: 'rgba(70,150,160,0.6)',
                duration: 0.3,
                ease: 'power2.out'
            });
            
            // 移除CSS类
            if (buttonRef.current) {
                buttonRef.current.classList.remove('breathing-btn');
            }
        } else {
            // 当按钮变为激活状态时，将斜边线保持显示并改变颜色
            if (buttonRef.current) {
                buttonRef.current.classList.add('breathing-btn');
            }
            
            // 创建呼吸灯效果 - 为边线
            const edgeAnimation = gsap.timeline({
                repeat: -1,
                yoyo: true
            });
            
            // 对边线应用呼吸效果
            edgeAnimation.to([topEdgeRef.current, rightEdgeRef.current, leftEdgeRef.current, bottomEdgeRef.current, slantedLineRef.current], {
                backgroundColor: 'rgba(126, 252, 250, 0.9)', // 亮色状态
                duration: 0.5,
                ease: 'sine.inOut',
                stagger: 0 // 所有边线同时变化
            }).to([topEdgeRef.current, rightEdgeRef.current, leftEdgeRef.current, bottomEdgeRef.current, slantedLineRef.current], {
                backgroundColor: 'rgba(79, 163, 170, 0.5)', // 暗色状态
                duration: 0.5,
                ease: 'sine.inOut',
                stagger: 0 // 所有边线同时变化
            });
            
            // 保存引用以便后续清理
            edgeAnimationRef.current = edgeAnimation;
        }
        
        // 组件卸载或状态改变时清理动画
        return () => {
            if (edgeAnimationRef.current) {
                edgeAnimationRef.current.kill();
                edgeAnimationRef.current = null;
            }
            if (lineRef.current) { // 确保清理lineRef中的动画
                lineRef.current.kill();
                lineRef.current = null;
            }
        };
    }, [isActive]); // 仅在isActive变化时运行

    // 创建一个包含斜角边线的样式
    const activeBackgroundStyle = useMemo(() => ({
        background: 'rgba(24, 82, 88, 0.5)',
        clipPath: 'polygon(0 0, 100% 0, 100% calc(100% - 8px), calc(100% - 8px) 100%, 0 100%)',
        position: 'relative',
        outline: 'none',
        WebkitTapHighlightColor: 'transparent', // 禁用移动设备点击高亮
    }), []); // 这个样式不依赖isActive，依赖项为空

    const normalBackgroundStyle = useMemo(() => ({
        background: 'rgba(10, 25, 35, 0.85)',
        color: '#67C8D0',
        clipPath: 'polygon(0 0, 100% 0, 100% calc(100% - 8px), calc(100% - 8px) 100%, 0 100%)',
        position: 'relative',
        outline: 'none',
        WebkitTapHighlightColor: 'transparent', // 禁用移动设备点击高亮
    }), []); // 这个样式也不依赖isActive，依赖项为空

    // 创建斜边线样式
    const slantedEdgeStyle = useMemo(() => ({
        content: '""',
        position: 'absolute',
        top: '16px',
        right: '0',
        width: '11px',
        height: '1px',
        background: isActive ? 'rgba(79, 163, 170, 0.5)' : 'rgba(70,150,160,0.6)',
        transform: 'rotate(-45deg)',
        transformOrigin: 'bottom right',
        opacity: 1 // 始终显示斜线
    }), [isActive]); // 依赖 isActive
    
    const edgeBaseStyle = useMemo(() => ({
        position: 'absolute',
        background: isActive ? 'rgba(79, 163, 170, 0.5)' : 'rgba(70,150,160,0.6)',
    }), [isActive]); // 依赖 isActive

    // 处理鼠标进入事件
    const handleMouseEnter = () => {
        if (!isActive && buttonRef.current) {
            gsap.to(buttonRef.current, {
                backgroundColor: 'rgba(20, 50, 60, 0.95)',
                color: '#9EECF2',
                duration: 0.3,
                ease: 'power2.out'
            });
            
            // 同时也改变斜边线和其他边线的颜色
            gsap.to([slantedLineRef.current, topEdgeRef.current, rightEdgeRef.current, leftEdgeRef.current, bottomEdgeRef.current], {
                backgroundColor: '#9EECF2',
                duration: 0.3,
                ease: 'power2.out'
            });
        }
    };

    // 处理鼠标离开事件
    const handleMouseLeave = () => {
        if (!isActive && buttonRef.current) {
            gsap.to(buttonRef.current, {
                backgroundColor: 'rgba(10, 25, 35, 0.85)',
                color: '#67C8D0',
                duration: 0.3,
                ease: 'power2.out'
            });
            
            // 同时也恢复斜边线和其他边线的颜色
            gsap.to([slantedLineRef.current, topEdgeRef.current, rightEdgeRef.current, leftEdgeRef.current, bottomEdgeRef.current], {
                backgroundColor: 'rgba(70,150,160,0.6)',
                duration: 0.3,
                ease: 'power2.out'
            });
        }
    };

    // 处理鼠标点击事件
    const handleClick = (e) => {
        if (buttonRef.current) {
            // 创建闪烁动画时间线
            const flashTl = gsap.timeline({
                onComplete: () => {
                    // 动画完成后调用原始onClick处理函数
                    onClick(e);
                    flashTl.kill();
                }
            });
            
            // 按钮本体闪烁效果：透明度 + 缩放
            flashTl.to(buttonRef.current, { 
                opacity: 0.3, 
                scale: 0.97, 
                duration: 0.05 
            })
            .to(buttonRef.current, { 
                opacity: 0.7, 
                duration: 0.05 
            })
            .to(buttonRef.current, { 
                opacity: 0.4, 
                duration: 0.05 
            })
            .to(buttonRef.current, { 
                opacity: 0.8, 
                duration: 0.05 
            })
            .to(buttonRef.current, { 
                opacity: 1.0, 
                scale: 1.0, 
                duration: 0.08 
            });
            
            // 边线闪烁效果
            const lineElements = [
                topEdgeRef.current,
                rightEdgeRef.current,
                leftEdgeRef.current,
                bottomEdgeRef.current,
                slantedLineRef.current
            ].filter(el => el); // 过滤掉null元素
            
            if (lineElements.length > 0) {
                // 先变亮到高亮色
                gsap.to(lineElements, { 
                    backgroundColor: '#00FFFF', // 青色高亮
                    opacity: 1.0,
                    duration: 0.05,
                    onComplete: () => {
                        // 然后恢复到原始颜色
                        const originalColor = isActive 
                            ? 'rgba(79, 163, 170, 0.5)' 
                            : 'rgba(70,150,160,0.6)';
                        gsap.to(lineElements, { 
                            backgroundColor: originalColor,
                            opacity: 0.8,
                            duration: 0.1
                        });
                    }
                });
            }
            
            // 背景闪烁效果
            if (buttonRef.current) {
                const originalBgColor = isActive 
                    ? 'rgba(24, 82, 88, 0.5)' 
                    : 'rgba(10, 25, 35, 0.85)';
                const flashBgColor = isActive 
                    ? 'rgba(50, 150, 160, 0.95)' // 激活状态闪烁色
                    : 'rgba(30, 80, 90, 0.95)';  // 非激活状态闪烁色
                
                // 背景颜色先变亮
                gsap.to(buttonRef.current, {
                    backgroundColor: flashBgColor,
                    duration: 0.05,
                    onComplete: () => {
                        // 然后恢复原色
                        gsap.to(buttonRef.current, {
                            backgroundColor: originalBgColor,
                            duration: 0.15
                        });
                    }
                });
            }
        }
        
        // 注意：这里不再直接调用onClick，而是由动画完成后在onComplete中调用
    };

    return (
        <div className="relative" ref={buttonContainerRef}>
            <button 
                ref={buttonRef}
                className={`
                    header-menu-btn h-[25px] w-[120px] flex items-center justify-center px-[18px] mr-[1px] cursor-pointer
                    font-['ChakraPetch-Light'] text-[12px] font-normal uppercase box-border transition-colors rounded-none
                    ${isActive 
                        ? 'text-white' 
                        : ''} /* 选中时文字颜色通过activeBackgroundStyle控制，这里清空 */
                    focus:outline-none focus:ring-0 
                    hover:outline-none hover:ring-0
                    active:outline-none active:ring-0
                `}
                style={isActive ? activeBackgroundStyle : normalBackgroundStyle}
                onClick={handleClick}
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
            >
            {children}
        </button>
            {/* 边线元素 */}
            <div ref={topEdgeRef} style={{ ...edgeBaseStyle, top: 0, left: 0, width: '100%', height: '1px' }}></div>
            <div ref={leftEdgeRef} style={{ ...edgeBaseStyle, top: 0, left: 0, height: '100%', width: '1px' }}></div>
            <div ref={rightEdgeRef} style={{ ...edgeBaseStyle, top: 0, right: 0, height: 'calc(100% - 8px)', width: '1px' }}></div>
            <div ref={bottomEdgeRef} style={{ ...edgeBaseStyle, bottom: 0, left: 0, width: 'calc(100% - 8px)', height: '1px' }}></div> {/* 调整底部边线 */}
            {/* 添加斜边线 - 始终显示 */}
            <div ref={slantedLineRef} style={slantedEdgeStyle}></div>
        </div>
    );
});

const HeaderMenu = ({ onMenuReady }) => {
    const [activeItem, setActiveItem] = useState('zone-m2'); // 默认激活 "ZONE M-2"
    const [menuItems, setMenuItems] = useState([]);
    const [loading, setLoading] = useState(true);
    const onMenuReadyCalled = useRef(false); // 用于确保 onMenuReady 只被调用一次

    useEffect(() => {
        const fetchMenuItems = async () => {
            setLoading(true);
            onMenuReadyCalled.current = false; // 重置调用标记
            try {
                await new Promise(resolve => setTimeout(resolve, 800));
                const apiResponse = [
                    { id: 'factory', label: 'FACTORY' },
                    { id: 'zone-m2', label: 'ZONE M-2', iconType: 'status_active' },
                    { id: 'zone-n3', label: 'ZONE N-3' },
                    { id: 'reactor', label: 'REACTOR' },
                    { id: 'h-part', label: 'H - PART' },
                    { id: 'display', label: 'DISPLAY', iconType: 'status_indicator' },
                ];
                setMenuItems(apiResponse);
            } catch (error) {
                console.error('获取菜单项失败:', error);
                setMenuItems([
                    { id: 'factory', label: 'FACTORY' },
                    { id: 'zone-m2', label: 'ZONE M-2' },
                ]);
            } finally {
                setLoading(false);
            }
        };
        fetchMenuItems();
    }, []);

    // 当 loading 变为 false 且 onMenuReady 未被调用过时，调用 onMenuReady
    useEffect(() => {
        if (!loading && typeof onMenuReady === 'function' && !onMenuReadyCalled.current) {
            onMenuReady();
            onMenuReadyCalled.current = true; // 标记已调用
        }
    }, [loading, onMenuReady]);

    // 使用 useCallback 包装 setActiveItem 调用，以稳定 onClick prop
    const handleItemClick = useCallback((itemId) => {
        setActiveItem(itemId);
    }, []); // 依赖项为空数组，因为 setActiveItem 的引用是稳定的

    // 图标组件不再显示方块
    const Icon = ({ type, isActive }) => {
        // 不再显示任何图标
        return null;
    };

    // 加载中的占位显示
    if (loading) {
        return (
            <div className="flex items-center h-[27px] px-[2px] gap-[10px]">
                {[1, 2, 3, 4].map((item) => (
                    <div key={item} className="w-[120px] h-[25px] bg-black/20 animate-pulse rounded-none"></div>
                ))}
            </div>
        );
    }

    return (
        <div className="flex items-center h-[27px] px-[2px] gap-[10px]"> {/* 保持容器高度 */}
            {menuItems.map((item) => (
                <SlantedButton
                    key={item.id}
                    onClick={() => handleItemClick(item.id)} // 使用 useCallback 包装的函数
                    isActive={item.id === activeItem}
                >
                    {item.label}
                </SlantedButton>
            ))}
        </div>
    );
};

export default HeaderMenu;
