import React, { useEffect, useRef, useCallback, memo, useState } from 'react';
import { gsap } from 'gsap';
import AnimationUtils from '../../../utils/animations';

// 定义颜色常量，方便统一修改
const ICON_COLOR = 'rgba(90,170,180,0.8)';
const ICON_COLOR_HOVER_BG = 'rgba(90,170,180,0.1)';
const ICON_COLOR_HOVER_BORDER = 'rgba(130,210,220,1)'; // 悬停时边框高亮颜色
const ARROW_ANIMATION_OFFSET = 1.5; // 箭头动画的移动距离
const ARROW_ANIMATION_DURATION = 0.3; // 箭头动画的持续时间

const HeaderMaximizeBtn = () => {
    const btnRef = useRef(null);
    const [isFullScreen, setIsFullScreen] = useState(false); // 追踪全屏状态
    const [isHovered, setIsHovered] = useState(false); // 追踪鼠标悬停状态
    const arrowAnimationRef = useRef(null); // 用于存储箭头动画的引用

    // 初始化按钮动画效果
    useEffect(() => {
        const btnElement = btnRef.current;
        if (!btnElement) return;

        const sequence = [
            { opacity: 0.5, duration: 0.15 },
            { opacity: 0.7, duration: 0.1 },
            { opacity: 0.2, duration: 0.1 },
            { opacity: 0.5, duration: 0.1 },
            { opacity: 0.4, duration: 0.1 },
            { opacity: 1, duration: 0.1 }
        ];

        const tl = AnimationUtils.createCustomBlinkEffect(btnRef, sequence);
        
        // 处理全屏状态变化事件 (例如用户按 ESC 退出全屏)
        const handleFullScreenChange = () => {
            setIsFullScreen(!!(document.fullscreenElement || document.mozFullScreenElement || document.webkitFullscreenElement || document.msFullscreenElement));
        };

        document.addEventListener('fullscreenchange', handleFullScreenChange);
        document.addEventListener('mozfullscreenchange', handleFullScreenChange);
        document.addEventListener('webkitfullscreenchange', handleFullScreenChange);
        document.addEventListener('msfullscreenchange', handleFullScreenChange);

        return () => {
            tl?.kill();
            document.removeEventListener('fullscreenchange', handleFullScreenChange);
            document.removeEventListener('mozfullscreenchange', handleFullScreenChange);
            document.removeEventListener('webkitfullscreenchange', handleFullScreenChange);
            document.removeEventListener('msfullscreenchange', handleFullScreenChange);
        };
    }, []);

    // 鼠标按下缩小效果
    const handleMouseDown = useCallback(() => {
        if (btnRef.current) {
            gsap.to(btnRef.current, { scale: 0.9, duration: 0.1 });
        }
    }, []);

    // 鼠标松开或移开恢复效果
    const handleMouseUpOrLeave = useCallback(() => {
        if (btnRef.current) {
            gsap.to(btnRef.current, { scale: 1, duration: 0.1 });
        }
    }, []);

    // 点击切换全屏/退出全屏
    const toggleFullScreen = useCallback(() => {
        const doc = document;
        const docEl = doc.documentElement;

        const requestFullScreen = docEl.requestFullscreen || docEl.mozRequestFullScreen || docEl.webkitRequestFullscreen || docEl.msRequestFullscreen;
        const cancelFullScreen = doc.exitFullscreen || doc.mozCancelFullScreen || doc.webkitExitFullscreen || doc.msExitFullscreen;

        const isInFullScreen = !!(doc.fullscreenElement || doc.mozFullScreenElement || doc.webkitFullscreenElement || doc.msFullscreenElement);

        if (!isInFullScreen) {
            if (requestFullScreen) {
                requestFullScreen.call(docEl).then(() => {
                    setIsFullScreen(true);
                }).catch(err => {
                    console.error(`进入全屏模式错误: ${err.message} (${err.name})`);
                });
            }
        } else {
            if (cancelFullScreen) {
                cancelFullScreen.call(doc).then(() => {
                    setIsFullScreen(false);
                }).catch(err => {
                    console.error(`退出全屏模式错误: ${err.message} (${err.name})`);
                });
            }
        }
    }, []);

    // 箭头动画函数
    const animateArrows = useCallback((targetRef, isEntering) => {
        if (arrowAnimationRef.current) {
            arrowAnimationRef.current.kill(); // 先停止任何正在进行的箭头动画
        }
        const arrows = targetRef?.querySelectorAll('.arrow-top-left, .arrow-bottom-right');
        if (!arrows || arrows.length === 0) return;

        if (isEntering) {
            arrowAnimationRef.current = gsap.timeline({ repeat: -1, yoyo: true });
            arrows.forEach(arrow => {
                const isTopLeft = arrow.classList.contains('arrow-top-left');
                // 全屏和非全屏状态下，箭头的视觉方向相反
                // isFullScreen 为 true 时是收缩箭头，视觉上向内
                // isFullScreen 为 false 时是展开箭头，视觉上向外
                const offsetMultiplier = isFullScreen ? 1 : -1;
                const xOffset = isTopLeft ? -ARROW_ANIMATION_OFFSET * offsetMultiplier : ARROW_ANIMATION_OFFSET * offsetMultiplier;
                const yOffset = isTopLeft ? -ARROW_ANIMATION_OFFSET * offsetMultiplier : ARROW_ANIMATION_OFFSET * offsetMultiplier;

                arrowAnimationRef.current.to(arrow, {
                    x: xOffset,
                    y: yOffset,
                    duration: ARROW_ANIMATION_DURATION,
                    ease: 'power1.inOut'
                }, 0); // 0 表示所有动画同时开始
            });
        } else {
            gsap.to(arrows, {
                x: 0,
                y: 0,
                duration: ARROW_ANIMATION_DURATION / 2, // 快速恢复
                ease: 'power1.out'
            });
        }
    }, [isFullScreen]);

    // 鼠标移入处理
    const handleMouseEnter = useCallback(() => {
        setIsHovered(true);
        if (btnRef.current) {
            gsap.to(btnRef.current, { borderColor: ICON_COLOR_HOVER_BORDER, duration: 0.2 });
            animateArrows(btnRef.current, true);
        }
    }, [animateArrows]);

    // 鼠标移出处理
    const handleMouseLeave = useCallback(() => {
        setIsHovered(false);
        if (btnRef.current) {
            gsap.to(btnRef.current, { borderColor: ICON_COLOR, duration: 0.2 });
            animateArrows(btnRef.current, false);
        }
        // 确保移开时恢复按钮大小，以防只触发了 onMouseDown
        if (btnRef.current?.style.transform === 'scale(0.9)') {
             gsap.to(btnRef.current, { scale: 1, duration: 0.1 });
        }
    }, [animateArrows]);

    // 当全屏状态改变且鼠标仍在悬停时，重新应用箭头动画
    useEffect(() => {
        if (isHovered && btnRef.current) {
            animateArrows(btnRef.current, true);
        }
        // isFullScreen 或 isHovered 变化时，如果isHovered为false，确保动画停止
        else if (!isHovered && btnRef.current){
            animateArrows(btnRef.current, false);
        }
    }, [isFullScreen, isHovered, animateArrows]);

    return (
        <div
            ref={btnRef}
            title={isFullScreen ? "还原" : "最大化"} // 根据全屏状态动态设置title
            className={`w-[23px] h-[23px] cursor-pointer flex items-center justify-center border transition-colors duration-300 ease-in-out hover:bg-[${ICON_COLOR_HOVER_BG}] relative`}
            style={{ borderColor: ICON_COLOR }} // 初始边框颜色
            onMouseDown={handleMouseDown}
            onMouseUp={handleMouseUpOrLeave} // mouseup时也可能触发leave，但leave处理更全面
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
            onClick={toggleFullScreen}
        >
            {isFullScreen ? (
                // 全屏状态下的图标：向内收缩的箭头
                <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g className="arrow-top-left"> {/* 左上角箭头组合 */}
                        <line x1="0.5" y1="0.5" x2="5" y2="5" stroke={ICON_COLOR} strokeWidth="1"/>
                        <polyline points="0.5,5 5,5 5,0.5" fill="none" stroke={ICON_COLOR} strokeWidth="1"/>
                    </g>
                    <g className="arrow-bottom-right"> {/* 右下角箭头组合 */}
                        <line x1="11.5" y1="11.5" x2="7" y2="7" stroke={ICON_COLOR} strokeWidth="1"/>
                        <polyline points="11.5,7 7,7 7,11.5" fill="none" stroke={ICON_COLOR} strokeWidth="1"/>
                    </g>
                </svg>
            ) : (
                // 非全屏状态下的图标：向外展开的箭头
                <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g className="arrow-top-left"> {/* 左上角箭头组合 */}
                        <line x1="5" y1="5" x2="0.5" y2="0.5" stroke={ICON_COLOR} strokeWidth="1"/>
                        <polyline points="5,0.5 0.5,0.5 0.5,5" fill="none" stroke={ICON_COLOR} strokeWidth="1"/>
                    </g>
                    <g className="arrow-bottom-right"> {/* 右下角箭头组合 */}
                        <line x1="7" y1="7" x2="11.5" y2="11.5" stroke={ICON_COLOR} strokeWidth="1"/>
                        <polyline points="7,11.5 11.5,11.5 11.5,7" fill="none" stroke={ICON_COLOR} strokeWidth="1"/>
                    </g>
                </svg>
            )}
        </div>
    );
};

export default memo(HeaderMaximizeBtn); 