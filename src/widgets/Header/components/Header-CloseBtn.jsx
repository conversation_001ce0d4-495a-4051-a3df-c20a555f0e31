import React, { useEffect, useRef, useState, memo } from 'react';
import { gsap } from 'gsap';
import AnimationUtils from '../../../utils/animations';

/**
 * @description 头部关闭按钮组件
 * @param {Object} props - 组件属性
 * @param {Function} props.onExitSystem - 退出系统的回调函数
 * @returns {JSX.Element}
 */
const HeaderCloseBtn = ({ onExitSystem }) => {
    const btnRef = useRef(null);
    const guideLine1Ref = useRef(null);
    const guideLine2Ref = useRef(null);
    const decorLineRef = useRef(null); // 新增装饰线引用
    const guideLineContainerRef = useRef(null);
    const titleContainerRef = useRef(null);
    const svgContainerRef = useRef(null);
    const stripeRef = useRef(null); // 新增斜条纹引用
    const textContainerRef = useRef(null); // 新增: 文字容器的ref
    const [isGuideLineVisible, setIsGuideLineVisible] = useState(false);
    const [svgLoaded, setSvgLoaded] = useState(false); // 新增加载状态
    // 存储按钮中心位置坐标
    const [btnPosition, setBtnPosition] = useState({ x: 0, y: 0 });
    const actionButtonsContainerRef = useRef(null); // 新增：底部按钮容器的ref

    // 为每个文字创建ref
    const textToAnimate = "确认退出系统？";
    const charRefs = useRef(textToAnimate.split('').map(() => React.createRef()));

    // 加载并初始化SVG - 正确获取SVG文件
    useEffect(() => {
        // 使用正确的路径加载SVG内容 - 修改为public目录的路径
        // fetch('/animated_check_icon.svg')
        fetch('/question_icon.svg')
        // fetch('/warnning_icon.svg')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`SVG加载失败，状态: ${response.status}`);
                }
                return response.text();
            })
            .then(svgText => {
                if (svgContainerRef.current) {
                    // 清空容器
                    svgContainerRef.current.innerHTML = '';
                    
                    // 创建解析器和DOM
                    const parser = new DOMParser();
                    const svgDoc = parser.parseFromString(svgText, 'image/svg+xml');
                    const svgElement = svgDoc.querySelector('svg');
                    
                    if (svgElement) {
                        // 调整SVG大小为48px
                        svgElement.setAttribute('width', '48');
                        svgElement.setAttribute('height', '48');
                        // 保留原始viewBox使内部比例不变
                        
                        // 添加到容器
                        svgContainerRef.current.appendChild(svgElement);
                        
                        // 执行SVG中的脚本 - 确保控制函数可用
                        const scriptContent = Array.from(svgDoc.querySelectorAll('script'))
                            .map(script => script.textContent)
                            .join('\n');
                            
                        if (scriptContent) {
                            try {
                                // 创建一个独立的函数来执行脚本内容
                                const executeScript = new Function(scriptContent);
                                executeScript();
                                
                                // 确认控制函数已经被创建
                                if (window.checkIconControl) {
                                    // console.log('SVG控制函数已成功创建'); // 已注释掉，减少控制台输出
                                } else {
                                    console.error('SVG控制函数未能创建');
                                }
                            } catch (error) {
                                console.error('执行SVG脚本时出错:', error);
                            }
                        }
                        
                        // console.log('SVG加载完成，动画控制已初始化'); // 已注释掉，减少控制台输出
                        setSvgLoaded(true);
                    }
                }
            })
            .catch(error => {
                console.error('加载SVG失败:', error);
            });
    }, []); // 只在组件挂载时加载一次

    useEffect(() => {
        const tl = AnimationUtils.createBlinkEffect(btnRef, {
            opacitySequence: [0.8, 0.3, 1], 
            durationSequence: [0.15, 0.15, 0.15],
            initialDelay: 0.1
        });
        
        // 添加全局点击事件监听，当点击页面其他地方时隐藏指引线
        const handleGlobalClick = (e) => {
            if (isGuideLineVisible && btnRef.current && !btnRef.current.contains(e.target)) {
                hideGuideLine();
            }
        };

        document.addEventListener('click', handleGlobalClick);
        
        // 获取按钮位置
        if (btnRef.current) {
            const rect = btnRef.current.getBoundingClientRect();
            setBtnPosition({
                x: rect.left + rect.width / 2,
                y: rect.top + rect.height / 2
            });
        }

        // 当指引线可见时，为每个文字应用闪烁动画
        if (isGuideLineVisible) {
            charRefs.current.forEach((charRef, index) => {
                if (charRef.current) {
                    AnimationUtils.createCustomBlinkEffect(
                        charRef.current,
                        [
                            // 前半段：快速、跳跃式闪烁 (保持不变，但可以微调)
                            { opacity: 0.2, duration: 0.03, ease: "none" }, // 稍快一点
                            { opacity: 1, duration: 0.03, ease: "none" },   // 稍快一点
                            { opacity: 0.2, duration: 0.03, ease: "none" },
                            { opacity: 1, duration: 0.03, ease: "none" },
                            { opacity: 0.2, duration: 0.04, ease: "none" },
                            { opacity: 1, duration: 0.04, ease: "none" },
                            { opacity: 0.2, duration: 0.04, ease: "none" },
                            { opacity: 1, duration: 0.04, ease: "none" },
                            
                            // 后半段优化：更多跳跃感，最终稳定 (缩短持续时间)
                            { opacity: 0.1, duration: 0.05, ease: "none" }, 
                            { opacity: 0.9, duration: 0.1, ease: "none" },  
                            { opacity: 0.2, duration: 0.05, ease: "none" }, 
                            { opacity: 1.0, duration: 0.1, ease: "none" }, 
                            { opacity: 0.3, duration: 0.07, ease: "none" },  
                            { opacity: 0.8, duration: 0.1, ease: "none" },  
                            { opacity: 0.1, duration: 0.04, ease: "none" }, 
                            { opacity: 1, duration: 0.3, ease: "none" }    // 缩短最后完全显示并停留的时间
                        ],
                        { 
                            initialDelay: 0.15 + index * 0.07, // 稍微调整延迟以适应更快的节奏
                            initialOpacity: 0 // 初始透明度为0
                        }
                    );
                }
            });
        }
        
        return () => {
            tl?.kill();
            document.removeEventListener('click', handleGlobalClick);
        };
    }, [isGuideLineVisible]);

    // 使用抽象的按钮缩放效果
    const { handleMouseDown, handleMouseUpOrLeave } = 
        AnimationUtils.createButtonScaleEffect(btnRef);
    
    // 显示指引线动画
    const showGuideLine = () => {
        if (!guideLine1Ref.current || !guideLine2Ref.current || !guideLineContainerRef.current || !btnRef.current || !textContainerRef.current || !svgContainerRef.current || !titleContainerRef.current || !stripeRef.current || !actionButtonsContainerRef.current) {
            console.error('指引线或相关元素引用缺失，无法执行显示动画');
            return;
        }

        // --- 状态重置 ---
        gsap.set(guideLineContainerRef.current, { autoAlpha: 1 });
        gsap.set(titleContainerRef.current, { autoAlpha: 1 }); // 确保父容器可见
        gsap.set(textContainerRef.current, { scaleY: 1, opacity: 1 });
        gsap.set(svgContainerRef.current, { opacity: 1 });
        // 指引线和装饰线会在后续动画中从 width: 0 开始
        gsap.set(guideLine1Ref.current, { width: 0 });
        gsap.set(decorLineRef.current, { width: 0 });
        gsap.set(guideLine2Ref.current, { width: 0 });
        // 斜条纹初始状态，也会在动画中从 width: 0 开始
        gsap.set(stripeRef.current, { width: 0, opacity: 0 });
        // 底部按钮容器初始状态
        gsap.set(actionButtonsContainerRef.current, { autoAlpha: 0 });

        setIsGuideLineVisible(true);
        // console.log('显示指引线动画'); // 已注释掉，减少控制台输出

        // 尝试播放SVG开启动画 (或重置SVG状态)
        if (window.checkIconControl && typeof window.checkIconControl.open === 'function') {
            // console.log('尝试播放SVG开启动画 - showGuideLine'); // 已注释掉，减少控制台输出
            window.checkIconControl.open();
        } else {
            // 如果SVG控制不直接可用，尝试之前的重新初始化逻辑 (简化版)
            console.warn('SVG open control not directly available, attempting re-init from cache or re-run script if needed - showGuideLine');
            if (svgLoaded) { // 确保SVG已加载过
                const svgScriptContent = svgContainerRef.current?.querySelector('script')?.textContent;
                if (svgScriptContent) {
                    try {
                        const executeScript = new Function(svgScriptContent);
                        executeScript();
                        if (window.checkIconControl && typeof window.checkIconControl.open === 'function') {
                            window.checkIconControl.open();
                            // console.log('SVG控制函数重新初始化并调用open成功 - showGuideLine'); // 已注释掉，减少控制台输出
                        } else {
                            console.error('SVG控制函数重新初始化后open仍失败 - showGuideLine');
                        }
                    } catch (error) {
                        console.error('重新执行SVG脚本以调用open时出错:', error);
                    }
                }
            }
        }
        
        // 获取按钮位置
        const btnRect = btnRef.current.getBoundingClientRect();
        
        // 修改：使用按钮左下角坐标作为起点，而不是中心点
        const btnStartX = btnRect.left;
        const btnStartY = btnRect.top + btnRect.height;
        
        // 设置第一条线的起始位置为按钮左下角
        gsap.set(guideLine1Ref.current, {
            width: 0,
            top: btnStartY,
            left: btnStartX,
            rotation: 135, // 向左下角方向 (225度，但GSAP是顺时针计算)
            transformOrigin: 'left center'
        });
        
        // 计算第一条线的终点坐标
        const line1EndX = btnStartX - 120 * Math.cos(Math.PI/4); // 计算终点X (Math.PI/4 = 45度，符合左下45度)
        const line1EndY = btnStartY + 120 * Math.sin(Math.PI/4); // 计算终点Y
        
        // 计算装饰线的位置（在第一根线的中间段）
        const decorStartX = btnStartX - 30 * Math.cos(Math.PI/4); // 起点在第一根线的1/4处
        const decorStartY = btnStartY + 30 * Math.sin(Math.PI/4);
        
        // 设置装饰线的起始位置 - 位于第一条线的中间段上
        if (decorLineRef.current) {
            gsap.set(decorLineRef.current, {
                width: 0,
                top: decorStartY,
                left: decorStartX,
                rotation: 135, // 与第一根线相同旋转角度
                transformOrigin: 'left center'
            });
        }
        
        // 提前设置第二条线的位置，但宽度为0
        gsap.set(guideLine2Ref.current, {
            width: 0,
            top: line1EndY,
            left: line1EndX,
            rotation: 0,
            transformOrigin: 'left center',
            scaleX: -1 // 水平翻转
        });
        
        // 设置标题容器位置和宽度
        if (titleContainerRef.current) {
            gsap.set(titleContainerRef.current, {
                top: line1EndY - 25, // 调整位置使其位于线上方，居中显示
                left: line1EndX - 300, // 起始位置与第二条线的起始位置对齐
                width: 300, // 与第二条线宽度一致
                height: 50, // 用户要求的容器高度
                autoAlpha: 0
            });
        }

        // 设置底部按钮容器位置和宽度 - 修改位置使其位于第二条线下方，与第二条线左侧对齐
        if (actionButtonsContainerRef.current) {
            // 第二条线的左侧坐标
            const lineLeftPosition = line1EndX - 300; // 因为第二条线是向左延伸的
            
            gsap.set(actionButtonsContainerRef.current, {
                top: line1EndY + 10, // 位于第二条线下方10px
                left: lineLeftPosition, // 与第二条线左侧对齐
                width: 300,
                autoAlpha: 0 // 初始不可见
            });
        }

        // 创建主时间线
        const mainTl = gsap.timeline();
        
        // 第一段线：从按钮中心向左下方的动画
        mainTl.fromTo(
            guideLine1Ref.current, 
            { width: 0 },
            { 
                width: 120, 
                duration: 0.35,
                ease: "power2.out"
            }
        );
        
        // 装饰线动画：与第一条线同步，但长度为一半
        if (decorLineRef.current) {
            mainTl.fromTo(
                decorLineRef.current,
                { width: 0 },
                {
                    width: 60, // 第一根线的一半长度
                    duration: 0.35,
                    ease: "power2.out"
                },
                "-=0.35" // 与第一条线同时开始
            );
        }
        
        // 第二段线：从第一条线终点向左的动画，使用position参数"-=0.05"让两个动画有轻微重叠
        mainTl.fromTo(
            guideLine2Ref.current, 
            { width: 0 },
            { 
                width: 300,
                duration: 0.35,
                ease: "power2.out" 
            },
            "-=0.05" // 在第一条线完成前0.05秒开始，创造无缝衔接效果
        );
        
        // 标题容器淡入动画
        if (titleContainerRef.current) {
            mainTl.to(
                titleContainerRef.current,
                {
                    autoAlpha: 1,
                    duration: 0.2,
                    ease: "power2.out",
                    onStart: () => {
                        // console.log('尝试播放开启动画', window.checkIconControl); // 已注释掉，减少控制台输出
                        if (window.checkIconControl && typeof window.checkIconControl.open === 'function') {
                            // 确保在控制函数可用时才调用
                            window.checkIconControl.open();
                        } else if (svgLoaded) {
                            // 直接创建并执行相关函数
                            try {
                                // 重新定义checkIconControl对象
                                const setupScript = `
                                window.checkIconControl = {
                                    open: function() {
                                        // 获取SVG内的元素
                                        const svg = document.querySelector("#checkIcon");
                                        if (svg) {
                                            // 触发圆圈显示
                                            document.getElementById("outerCircleOpacityOpen")?.beginElement();
                                            document.getElementById("outerCircleScaleOpen")?.beginElement();
                                            document.getElementById("innerCircleOpacityOpen")?.beginElement();
                                            document.getElementById("innerCircleScaleOpen")?.beginElement();
                                            document.getElementById("glowOpacityOpen")?.beginElement();
                                            document.getElementById("glowScaleOpen")?.beginElement();
                                            
                                            // 触发点的动画
                                            document.getElementById("dot1Open")?.beginElement();
                                            document.getElementById("dot2Open")?.beginElement();
                                            document.getElementById("dot3Open")?.beginElement();
                                            document.getElementById("dot4Open")?.beginElement();
                                            
                                            // 显示对勾
                                            setTimeout(() => {
                                                document.getElementById("checkOpacityOpen")?.beginElement();
                                                document.getElementById("checkDrawOpen")?.beginElement();
                                            }, 500);
                                        }
                                    },
                                    close: function() {
                                        // 获取SVG内的元素
                                        const svg = document.querySelector("#checkIcon");
                                        if (svg) {
                                            // 隐藏对勾
                                            document.getElementById("checkOpacityClose")?.beginElement();
                                            document.getElementById("checkDrawClose")?.beginElement();
                                            
                                            // 隐藏圆圈
                                            setTimeout(() => {
                                                document.getElementById("outerCircleOpacityClose")?.beginElement();
                                                document.getElementById("outerCircleScaleClose")?.beginElement();
                                                document.getElementById("innerCircleOpacityClose")?.beginElement();
                                                document.getElementById("innerCircleScaleClose")?.beginElement();
                                                document.getElementById("glowOpacityClose")?.beginElement();
                                                document.getElementById("glowScaleClose")?.beginElement();
                                            }, 300);
                                            
                                            // 移动点
                                            setTimeout(() => {
                                                document.getElementById("dot1Close")?.beginElement();
                                                document.getElementById("dot2Close")?.beginElement();
                                                document.getElementById("dot3Close")?.beginElement();
                                                document.getElementById("dot4Close")?.beginElement();
                                            }, 800);
                                        }
                                    },
                                    reset: function() {
                                        // 重置状态
                                    }
                                };
                                `;
                                const setupFunc = new Function(setupScript);
                                setupFunc();
                                
                                // 调用新定义的open函数
                                if (window.checkIconControl?.open) {
                                    window.checkIconControl.open();
                                }
                            } catch (error) {
                                console.error('重新初始化SVG控制时出错:', error);
                            }
                        }
                    }
                },
                "-=0.15" // 在第二条线画到一半时就开始显示
            );
        }

        // 斜条纹动画 - 在第二条线完成后显示
        if (stripeRef.current) {
            mainTl.to(
                stripeRef.current,
                {
                    width: "50%",
                    opacity: 1,
                    duration: 0.5,
                    ease: "power2.out"
                },
                "-=0.1" // 与标题容器动画略微重叠
            );
        }

        // 底部按钮容器淡入动画 - 修改：确保在第二条线动画完成后再显示按钮容器
        if (actionButtonsContainerRef.current) {
            // 设置容器为可见但按钮不展开
            gsap.set(actionButtonsContainerRef.current, { autoAlpha: 1 });
            
            // 延迟到第二条线和标题动画结束后才显示按钮容器
            mainTl.to(
                actionButtonsContainerRef.current,
                {
                    // 只应用autoAlpha属性，实际按钮的展开动画由StyledSlantedButton组件内部控制
                    autoAlpha: 1,
                    duration: 0, // 立即显示容器
                    onComplete: () => {
                        // 按钮的展开动画在StyledSlantedButton组件内部通过isVisible属性控制
                        // console.log('按钮容器显示，按钮将开始展开动画'); // 已注释掉，减少控制台输出
                    }
                },
                "+=0.1" // 在斜条纹动画开始后0.1秒后执行
            );
        }
    };
    
    // 隐藏指引线动画
    const hideGuideLine = () => {
        if (!guideLine1Ref.current || !guideLine2Ref.current || !guideLineContainerRef.current || !textContainerRef.current || !svgContainerRef.current || !titleContainerRef.current || !stripeRef.current || !actionButtonsContainerRef.current) {
            console.error('指引线或相关元素引用缺失，无法执行隐藏动画');
            return;
        }
        
        // console.log('隐藏指引线动画开始'); // 已注释掉，减少控制台输出
        
        const reverseTl = gsap.timeline({
            onComplete: () => {
                setIsGuideLineVisible(false);
                // console.log('指引线隐藏完成'); // 已注释掉，减少控制台输出
            }
        });
        
        // 1. 立即触发SVG关闭动画，但不等待
        if (window.checkIconControl && typeof window.checkIconControl.close === 'function') {
            // console.log('立即触发SVG关闭动画'); // 已注释掉，减少控制台输出
            window.checkIconControl.close();
        } else {
            console.warn('SVG close control/function not available when attempting to play close animation.');
        }
        
        // 2. SVG 容器保持可见，给SVG内部动画足够时间播放
        if (svgContainerRef.current) {
            reverseTl.to(
                svgContainerRef.current,
                {
                    opacity: 1, // 先保持完全可见
                    duration: 0.2, // 给SVG内部动画时间
                    ease: "none"
                }
            );
            
            // 然后再淡出SVG容器
            reverseTl.to(
                svgContainerRef.current,
                {
                    opacity: 0,
                    duration: 0.3,
                    ease: "power2.in"
                },
                "+=0.1" // 在保持可见动画结束后0.1秒开始淡出
            );
        }

        // 3. 文字收缩动画，与SVG容器淡出同时开始
        if (textContainerRef.current) {
            reverseTl.to(
                textContainerRef.current,
                {
                    scaleY: 0,
                    opacity: 0,
                    duration: 0.3, 
                    ease: "power2.in",
                    transformOrigin: "center center"
                },
                "<" // 与SVG容器淡出同时开始
            );
        }

        // 4. 斜条纹动画 (在SVG和文字动画开始后0.1秒开始)
        if (stripeRef.current) {
            reverseTl.to(
                stripeRef.current,
                {
                    width: 0,
                    opacity: 0,
                    duration: 0.3, 
                    ease: "power2.in"
                },
                "<0.1" // 相对于SVG容器淡出动画的0.1秒后
            );
        }
        
        // 5. 底部按钮容器淡出动画 (与斜条纹动画同时)
        if (actionButtonsContainerRef.current) {
            reverseTl.to(
                actionButtonsContainerRef.current,
                {
                    autoAlpha: 0,
                    duration: 0.25,
                    ease: "power2.in"
                },
                "<" // 与斜条纹动画同时开始
            );
        }
        
        // 6. 指引线收回动画 (在标题区域元素动画基本结束后开始)
        const titleElementsClearTime = 0.6; // 调整时间点，给前面的动画更多时间
        const durationLine2 = 0.25;         
        const durationLine1AndDecor = 0.25; 

        // 首先，动画第二根引导线 (guideLine2Ref)
        reverseTl.to(guideLine2Ref.current, { 
            width: 0, 
            duration: durationLine2, 
            ease: "power2.in" 
        }, titleElementsClearTime);
        
        // 计算第一根引导线和装饰线开始动画的时间点
        const line1AndDecorStartTime = titleElementsClearTime + durationLine2;

        // 然后，在第二根引导线动画播放完毕后，同时动画装饰线和第一根引导线
        if (decorLineRef.current) {
            reverseTl.to(
                decorLineRef.current, 
                { width: 0, duration: durationLine1AndDecor, ease: "power2.in" },
                line1AndDecorStartTime
            );
            reverseTl.to(guideLine1Ref.current, { 
                width: 0, 
                duration: durationLine1AndDecor, 
                ease: "power2.in" 
            }, "<" ); // 与 decorLineRef 同时开始
        } else {
            // 如果没有装饰线，则直接动画第一根引导线
            reverseTl.to(guideLine1Ref.current, { 
                width: 0, 
                duration: durationLine1AndDecor, 
                ease: "power2.in" 
            }, line1AndDecorStartTime);
        }
    };
    
    // 处理按钮点击事件
    const handleClick = (e) => {
        e.stopPropagation(); // 阻止事件冒泡
        // console.log('关闭按钮被点击', isGuideLineVisible); // 已注释掉，减少控制台输出
        
        if (!isGuideLineVisible) {
            showGuideLine();
        } else {
            hideGuideLine();
        }
    };

    // 新增：带斜角和划线效果的按钮组件
    const StyledSlantedButton = ({ text, onClick, isConfirm, isVisible }) => {
        // 定义基础状态和悬浮状态的颜色
        const normalBgColor = isConfirm ? 'rgba(24, 82, 88, 0.4)' : 'rgba(10, 25, 35, 0.85)';
        const hoverBgColor = isConfirm ? 'rgba(30, 95, 105, 1)' : 'rgba(24, 82, 88, 0.95)';
        const normalLineColor = isConfirm ? 'rgba(126, 252, 250, 0.3)' : 'rgba(70, 150, 160, 0.5)';
        const hoverLineColor = '#7EFCFA'; // 更亮的线条颜色
        const normalTextColor = isConfirm ? '#FFFFFF' : '#67C8D0';
        const hoverTextColor = '#FFFFFF';

        // 斜角大小（像素）
        const slantSize = 8; // 增加斜角大小
        const slantSizePx = `${slantSize}px`;
        
        // 斜线长度 = 斜角大小 * √2
        const slantLineLength = slantSize * Math.sqrt(2);
        const slantLineLengthPx = `${slantLineLength}px`;

        const [isHovered, setIsHovered] = useState(false);
        const buttonRef = useRef(null); // 保留ref用于动画效果
        const buttonContentRef = useRef(null); // 添加内容区域的ref
        const buttonContainerHeight = 25; // 按钮的目标高度
        
        // 新增：边线的ref引用
        const topLineRef = useRef(null);
        const leftLineRef = useRef(null);
        const bottomLineRef = useRef(null);
        const rightLineRef = useRef(null);
        const slantLineRef = useRef(null);
        
        // 新增：按钮背景层ref
        const buttonBgRef = useRef(null);

        useEffect(() => {
            // 确保DOM元素已经渲染
            if (buttonRef.current) {
                // 重置按钮初始状态 - 重要：使用scaleY而不是height
                gsap.set(buttonRef.current, {
                    scaleY: 0,       // 完全压缩
                    transformOrigin: "top center", // 从顶部向下展开
                    opacity: 1       // 保持可见
                });
                
                // 根据可见性状态应用动画
                if (isVisible) {
                    // 展开动画：从上到下展开
                    gsap.to(buttonRef.current, {
                        scaleY: 1,   // 完全展开
                        duration: 0.25,  // 加快展开速度，从0.4改为0.25
                        ease: "power2.out",
                        delay: isConfirm ? 0.5 : 0.6, // 减少延迟时间，从0.7/0.8改为0.5/0.6
                        onComplete: () => {
                            // 按钮展开后开始画线动画
                            if (topLineRef.current && rightLineRef.current && 
                                bottomLineRef.current && leftLineRef.current && slantLineRef.current) {
                                
                                // 创建线条绘制时间线
                                const linesTl = gsap.timeline();
                                
                                // 1. 顶部线条从左到右
                                linesTl.fromTo(
                                    topLineRef.current,
                                    { width: 0, left: 0, right: 'auto' },
                                    { 
                                        width: '100%', 
                                        duration: 0.08,  // 加快线条绘制速度，从0.15改为0.08
                                        ease: "none"
                                    }
                                );
                                
                                // 2. 右侧线条从上到下
                                linesTl.fromTo(
                                    rightLineRef.current,
                                    { height: 0, top: 0, bottom: 'auto' },
                                    { 
                                        height: `calc(100% - ${slantSizePx})`, 
                                        duration: 0.06,  // 加快线条绘制速度，从0.12改为0.06
                                        ease: "none"
                                    }
                                );
                                
                                // 3. 斜线从右上到左下
                                linesTl.fromTo(
                                    slantLineRef.current,
                                    { width: 0 },
                                    { 
                                        width: slantLineLengthPx, 
                                        duration: 0.04,  // 加快线条绘制速度，从0.08改为0.04
                                        ease: "none"
                                    }
                                );
                                
                                // 4. 底部线条从右到左
                                linesTl.fromTo(
                                    bottomLineRef.current,
                                    { width: 0, right: slantSizePx, left: 'auto' },
                                    { 
                                        width: `calc(100% - ${slantSizePx})`, 
                                        duration: 0.08,  // 加快线条绘制速度，从0.15改为0.08
                                        ease: "none"
                                    }
                                );
                                
                                // 5. 左侧线条从下到上
                                linesTl.fromTo(
                                    leftLineRef.current,
                                    { height: 0, bottom: 0, top: 'auto' },
                                    { 
                                        height: '100%', 
                                        duration: 0.06,  // 加快线条绘制速度，从0.12改为0.06
                                        ease: "none"
                                    }
                                );
                            }
                        }
                    });
                } else {
                    // 先反向收回边线，再收起按钮
                    if (topLineRef.current && rightLineRef.current && 
                        bottomLineRef.current && leftLineRef.current && slantLineRef.current) {
                        
                        const reverseLinesTl = gsap.timeline({
                            onComplete: () => {
                                // 所有线条消失后，收缩按钮
                                gsap.to(buttonRef.current, {
                                    scaleY: 0,   // 完全收缩
                                    duration: 0.2,  // 加快收缩速度，从0.3改为0.2
                                    ease: "power2.in",
                                    delay: isConfirm ? 0 : 0.1, // 减少延迟时间，从0.15改为0.1
                                });
                            }
                        });
                        
                        // 1. 左侧线条从上到下消失
                        reverseLinesTl.to(
                            leftLineRef.current,
                            { 
                                height: 0, 
                                top: 0, 
                                bottom: 'auto',
                                duration: 0.05,  // 加快线条消失速度，从0.1改为0.05
                                ease: "none"
                            }
                        );
                        
                        // 2. 底部线条从左到右消失
                        reverseLinesTl.to(
                            bottomLineRef.current,
                            { 
                                width: 0, 
                                left: 0, 
                                right: 'auto',
                                duration: 0.05,  // 加快线条消失速度，从0.1改为0.05
                                ease: "none"
                            }
                        );
                        
                        // 3. 斜线消失
                        reverseLinesTl.to(
                            slantLineRef.current,
                            { 
                                width: 0, 
                                duration: 0.05,  // 加快线条消失速度，从0.1改为0.05
                                ease: "none"
                            }
                        );
                        
                        // 4. 右侧线条从下到上消失
                        reverseLinesTl.to(
                            rightLineRef.current,
                            { 
                                height: 0, 
                                bottom: 0, 
                                top: 'auto',
                                duration: 0.05,  // 加快线条消失速度，从0.1改为0.05
                                ease: "none"
                            }
                        );
                        
                        // 5. 顶部线条从右到左消失
                        reverseLinesTl.to(
                            topLineRef.current,
                            { 
                                width: 0, 
                                right: 0, 
                                left: 'auto',
                                duration: 0.05,  // 加快线条消失速度，从0.1改为0.05
                                ease: "none"
                            }
                        );
                    } else {
                        // 如果线条ref不存在，直接收缩按钮
                        gsap.to(buttonRef.current, {
                            scaleY: 0,   // 完全收缩
                            duration: 0.2,  // 加快收缩速度，从0.3改为0.2
                            ease: "power2.in",
                            delay: isConfirm ? 0 : 0.1, // 减少延迟时间，从0.15改为0.1
                        });
                    }
                }
            }
        }, [isVisible, isConfirm, slantSizePx, slantLineLengthPx]);

        const handleMouseEnter = () => setIsHovered(true);
        const handleMouseLeave = () => setIsHovered(false);

        // 新增：按钮点击闪烁效果
        const handleButtonClick = (e) => {
            // 创建闪烁动画
            const flashTl = gsap.timeline({
                onComplete: () => {
                    // 动画完成后调用原始的onClick回调
                    if (onClick) onClick(e);
                    flashTl.kill();
                }
            });
            
            // 添加按钮闪烁效果，同时缩放并调整透明度
            flashTl.to(buttonRef.current, { opacity: 0.3, scale: 0.97, duration: 0.05 })
                   .to(buttonRef.current, { opacity: 0.7, duration: 0.05 })
                   .to(buttonRef.current, { opacity: 0.4, duration: 0.05 })
                   .to(buttonRef.current, { opacity: 0.8, duration: 0.05 })
                   .to(buttonRef.current, { opacity: 1.0, scale: 1.0, duration: 0.08 });
            
            // 边线闪烁效果
            if (topLineRef.current && rightLineRef.current && 
                bottomLineRef.current && leftLineRef.current && slantLineRef.current) {
                
                const lineElements = [
                    topLineRef.current,
                    rightLineRef.current,
                    bottomLineRef.current, 
                    leftLineRef.current,
                    slantLineRef.current
                ];
                
                // 提高亮度，然后恢复
                gsap.to(lineElements, { 
                    backgroundColor: hoverLineColor,
                    opacity: 1.0,
                    duration: 0.05,
                    onComplete: () => {
                        gsap.to(lineElements, { 
                            backgroundColor: currentLineColor,
                            opacity: 0.8,
                            duration: 0.1
                        });
                    }
                });
            }
            
            // 背景闪烁效果
            if (buttonBgRef.current) {
                // 背景颜色闪烁：先变亮，再恢复
                gsap.to(buttonBgRef.current, {
                    backgroundColor: isConfirm ? 'rgba(39, 120, 128, 0.95)' : 'rgba(39, 90, 100, 0.95)',
                    duration: 0.05,
                    onComplete: () => {
                        gsap.to(buttonBgRef.current, {
                            backgroundColor: isHovered ? hoverBgColor : normalBgColor,
                            duration: 0.15
                        });
                    }
                });
            }
        };

        // 当前边线颜色
        const currentLineColor = isHovered ? hoverLineColor : normalLineColor;

        return (
            <div 
                className="relative" 
                style={{ 
                    height: buttonContainerHeight,
                    overflow: "hidden",
                }}
            >
                <div
                    ref={buttonRef}
                    className="relative w-[100%]"
                    style={{
                        height: buttonContainerHeight, // 固定高度
                        transform: "scaleY(0)",  // 初始状态完全收缩
                        transformOrigin: "top center" // 从顶部开始展开
                    }}
                >
                    <div
                        ref={buttonContentRef}
                        onClick={handleButtonClick}
                        onMouseEnter={handleMouseEnter}
                        onMouseLeave={handleMouseLeave}
                        className="relative flex items-center justify-center"
                        style={{
                            padding: '0 8px',
                            height: buttonContainerHeight,
                            cursor: 'pointer'
                        }}
                    >
                        {/* 背景层 */}
                        <div 
                            ref={buttonBgRef}
                            className='absolute top-[0px] left-[0px] w-[100%] h-[100%]'
                            style={{
                                backgroundColor: isHovered ? hoverBgColor : normalBgColor,
                                clipPath: `polygon(0 0, 100% 0, 100% calc(100% - ${slantSizePx}), calc(100% - ${slantSizePx}) 100%, 0 100%)`,
                                transition: 'background-color 0.3s',
                            }}
                        />
                        
                        {/* 文字层 - 独立于背景，确保居中 */}
                        <div
                            className='relative flex items-center justify-center z-[2] w-[100%] h-[100%] text-[14px] font-bold transition-colors duration-300'
                            style={{
                                color: isHovered ? hoverTextColor : normalTextColor,
                            }}
                        >
                            {text}
                        </div>

                        {/* 边线 - 使用内联样式明确定位，确保在按钮周围 */}
                        {/* 顶部线条 */}
                        <div
                            ref={topLineRef}
                            className='absolute top-[0px] left-[0px] w-[0px] h-[1px] transition-colors duration-300 ease-in-out'
                            style={{
                                backgroundColor: currentLineColor,
                            }}
                        />
                        
                        {/* 左侧线条 */}
                        <div
                            ref={leftLineRef}
                            className='absolute top-[0px] left-[0px] w-[1px] h-[0px] transition-colors duration-300 ease-in-out'
                            style={{
                                backgroundColor: currentLineColor,
                            }}
                        />
                        
                        {/* 底部线条 (不含右下角斜边部分) */}
                        <div
                            ref={bottomLineRef}
                            className='absolute bottom-[0px] left-[0px] w-[0px] h-[1px] transition-colors duration-300 ease-in-out z-[1]'
                            style={{
                                backgroundColor: currentLineColor,
                                transition: 'background-color 0.3s',
                            }}
                        />
                        
                        {/* 右侧线条 (不含右下角斜边部分) */}
                        <div
                            ref={rightLineRef}
                            className='absolute top-[0px] right-[0px] w-[1px] h-[0px] transition-colors duration-300 ease-in-out z-[1]'
                            style={{
                                backgroundColor: currentLineColor,
                                transition: 'background-color 0.3s',
                            }}
                        />
                        
                        {/* 右下角斜线 */}
                        <div
                            ref={slantLineRef}
                            className='absolute w-[0px] h-[1px] bottom-[0px] right-[0px] transition-colors duration-300 ease-in-out z-[1]'
                            style={{
                                transform: 'rotate(-45deg)',
                                transformOrigin: 'bottom right',
                                backgroundColor: currentLineColor,
                                transition: 'background-color 0.3s',
                                bottom: slantSizePx // 位置调整到斜角处
                            }}
                        />
                    </div>
                </div>
            </div>
        );
    };

    // 处理确认退出系统
    const handleConfirmExit = () => {
        // 隐藏弹窗
        hideGuideLine();
        
        // 调用退出系统的回调函数
        if (typeof onExitSystem === 'function') {
            setTimeout(() => {
                onExitSystem();
            }, 500); // 延迟500毫秒，等待弹窗关闭动画完成
        }
    };

    return (
        <div className="relative" style={{ position: 'relative' }}>
            <div
                ref={btnRef}
                title="关闭"
                className="w-[25px] h-[25px] cursor-pointer flex items-center justify-center text-[15px] font-bold bg-[rgba(24,82,88,0.8)] text-[#7EFCFA] transition-colors duration-300 ease-in-out hover:bg-[rgba(24,82,88,1)] relative"
                onMouseDown={handleMouseDown}
                onMouseUp={handleMouseUpOrLeave}
                onMouseLeave={handleMouseUpOrLeave}
                onClick={handleClick}
            >
                {/* 十字图标 */}
                <div className="absolute w-[13px] h-[1px] bg-[#7EFCFA] transform rotate-45"></div>
                <div className="absolute w-[13px] h-[1px] bg-[#7EFCFA] transform -rotate-45"></div>
            </div>
            
            {/* 指引线容器 - 使用固定定位确保可见性 */}
            <div 
                ref={guideLineContainerRef}
                className="fixed top-[0px] left-[0px] w-screen h-screen pointer-events-none z-[9999]"
                style={{ 
                    visibility: isGuideLineVisible ? 'visible' : 'hidden',
                    opacity: isGuideLineVisible ? 1 : 0,
                }}
            >
                {/* 从按钮中心到左下方的线 */}
                <div 
                    ref={guideLine1Ref}
                    className='absolute h-[1px] w-0 bg-[#335566]'
                />
                
                {/* 装饰线 - 位于第一条线中间段的粗线 */}
                <div 
                    ref={decorLineRef}
                    className='absolute h-[2px] w-0 bg-[#335566]'
                />
                
                {/* 从第一条线的末端向左延伸的线 */}
                <div 
                    ref={guideLine2Ref}
                    className="absolute h-[1px] w-0 bg-[#335566]"
                />
                
                {/* 标题容器 - 与第二条线等宽 */}
                <div
                    ref={titleContainerRef}
                    className="absolute flex justify-center items-center h-[50px] opacity-0 invisible"
                >
                    {/* SVG容器 - 48px尺寸 */}
                    <div className='flex flex-row w-[300px] h-12 justify-center items-center relative bottom-[25px]'>
                        {/* SVG容器 */}
                        <div
                            ref={svgContainerRef}
                            className="w-[48px] h-[48px] flex justify-center items-center"
                        >
                            {/* SVG内容会被动态插入到这里 */}
                        </div>

                        {/* 确认退出系统文字 */}
                        <div 
                            ref={textContainerRef} // 应用新的ref
                            className="absolute right-0 top-1/2 transform -translate-y-1/2 mx-2 text-[#FFFFFF] font-bold ml-[-40px] skew-x-[5deg] text-[18px]" 
                            style={{ 
                                fontFamily: "'DingTalkJinBuTi', sans-serif",
                                // opacity 和 animation 由 GSAP 控制
                            }}
                        >
                            {textToAnimate.split('').map((char, index) => (
                                <span key={index} ref={charRefs.current[index]} style={{ opacity: 0 }}>
                                    {char}
                                </span>
                            ))}
                        </div>

                        {/* 斜条纹斑马条纹 - 移出absolute布局，改为相对定位 */}
                        <div
                            ref={stripeRef}
                            className="ml-auto h-[25px] w-1/2 z-[50] mr-[10px]"
                            style={{
                                background: `repeating-linear-gradient(
                                135deg,
                                #F59F25,
                                #F59F25 6px,
                                transparent 6px,
                                transparent 12px
                                )`,
                                WebkitMaskImage: 'linear-gradient(to left, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0))',
                                maskImage: 'linear-gradient(to left, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0))',
                                pointerEvents: 'none',
                                opacity: 0,  // 初始隐藏
                                width: 0     // 初始宽度为0
                            }}
                        />
                    </div>

                </div>

                {/* 新增：按钮容器 - 位于标题容器下方 */}
                <div
                    ref={actionButtonsContainerRef} // 如果需要动画控制，可以添加ref
                    className="absolute flex justify-center items-center w-[300px] opacity-0 invisible"
                    style={{ pointerEvents: 'auto' }}
                >
                    <div className="flex flex-row justify-around items-center w-full gap-[10px]">
                        {/* 按钮容器 - 确保两个按钮各占50% */}
                        <div className="flex w-full px-4 gap-[10px]">
                            {/* 取消按钮 - 左半部分 */}
                            <div className="w-1/2 pr-2">
                                <StyledSlantedButton
                                    text="取消"
                                    onClick={() => hideGuideLine()} // 实现取消功能
                                    isConfirm={false}
                                    isVisible={isGuideLineVisible} // 传递可见性状态
                                />
                            </div>

                            {/* 确定按钮 - 右半部分 */}
                            <div className="w-1/2 pl-2">
                                <StyledSlantedButton
                                    text="确定"
                                    onClick={handleConfirmExit} // 修改为使用handleConfirmExit函数
                                    isConfirm={true}
                                    isVisible={isGuideLineVisible} // 传递可见性状态
                                />
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    );
};

export default memo(HeaderCloseBtn);
