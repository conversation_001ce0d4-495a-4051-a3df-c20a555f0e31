import React, { useEffect, useRef, memo, useState, useLayoutEffect } from 'react';
import { gsap } from 'gsap';
import CountUp from 'react-countup'; // 导入 CountUp
import AnimationUtils from '../../../utils/animations'; // 导入动画工具类
import Config from '../../../config'; // 导入全局配置

/**
 * @description 一个具有特定样式的弹性空间组件，底部有两条从右到左的动画线段
 * @param {object} props - 组件属性
 * @param {boolean} props.animateBorder - 是否触发边框动画
 * @returns {JSX.Element} UI 界面
 */
const FlexibleSpace = ({ animateBorder }) => {
    const animatedMainBorderRef = useRef(null); // 主动画线 ref
    const animatedSecondaryLineRef = useRef(null); // 第二条动画线 ref
    const timeDisplayRef = useRef(null); // Ref to the time display div
    const stripedContainerRef = useRef(null); // 斜条纹背景容器 ref
    const versionContainerRef = useRef(null); // 版本号容器 ref
    const versionTextRef = useRef(null); // 版本号文本 ref
    const isMountedRef = useRef(true); // 追踪组件是否挂载
    // 使用全局配置中的版本号
    const version = Config.app.version;
    
    // 解析版本号，分离字符和数字部分
    const [versionParts, setVersionParts] = useState({ prefix: '', numbers: [0, 0, 0] });
    const [prevVersionParts, setPrevVersionParts] = useState({ prefix: '', numbers: [0, 0, 0] });
    const [isVersionParsed, setIsVersionParsed] = useState(false);
    const [showVersion, setShowVersion] = useState(false); // 控制版本号显示时机

    // 初始让时间部分为空字符串或null，CountUp不会渲染任何内容
    const initialEmptyTime = { month: null, day: null, hours: null, minutes: null, seconds: null };
    const zeroTime = { month: 0, day: 0, hours: 0, minutes: 0, seconds: 0 };

    const [timeParts, setTimeParts] = useState(initialEmptyTime);
    const [prevTimeParts, setPrevTimeParts] = useState(initialEmptyTime);
    const [isTimeTextRendered, setIsTimeTextRendered] = useState(false); // Still useful for conditional rendering of the wrapper

    // Ref 控制动画流程状态
    const animationControl = useRef({
        isReadyForTimeUpdates: false, // For setInterval to know when to start updating timeParts for CountUp
        isInitialRollupPending: false, // To trigger the 00 -> currentTime animation once
        hasFadedIn: false, // Track if fade-in animation has completed
    });

    // 使用 useLayoutEffect 来在浏览器绘制前设置初始状态
    useLayoutEffect(() => {
        isMountedRef.current = true;
        setIsTimeTextRendered(false); // Ensure wrapper is not rendered initially
        animationControl.current.hasFadedIn = false;
        // Set time parts to empty on initial layout to ensure CountUp renders nothing
        setTimeParts(initialEmptyTime);
        setPrevTimeParts(initialEmptyTime);
        return () => {
            // 组件卸载时标记
            isMountedRef.current = false;
        };
    }, []);

    useEffect(() => {
        const mainEl = animatedMainBorderRef.current;
        const secondEl = animatedSecondaryLineRef.current;
        const stripedEl = stripedContainerRef.current;

        if (!mainEl || !secondEl || !isMountedRef.current) return;

        setIsTimeTextRendered(false); // Reset for each effect run, controlled by animation timeline
        animationControl.current.isReadyForTimeUpdates = false;
        animationControl.current.isInitialRollupPending = animateBorder; // Only roll up from 00 if border animates
        animationControl.current.hasFadedIn = false;

        gsap.set(mainEl, { width: '0%', bottom: '0px', right: '0px' });
        gsap.set(secondEl, { width: '0%', bottom: '0px', right: '0px' });

        // Initially set time to empty so nothing shows even if div is briefly visible
        setTimeParts(initialEmptyTime);
        setPrevTimeParts(initialEmptyTime);

        if (animateBorder) {
            gsap.to(mainEl, { width: '100%', duration: 0.8, ease: 'power2.out', delay: 0.8 });
            gsap.to(secondEl, {
                width: '30%', duration: 1, ease: 'power2.out', delay: 0.9,
                onComplete: () => {
                    if (isMountedRef.current) {
                        setIsTimeTextRendered(true);
                        
                        // 在边框动画完成后添加斜条纹容器的闪烁效果
                        if (stripedEl) {
                            AnimationUtils.createBlinkEffect(stripedEl, {
                                opacitySequence: [0.2, 0.7, 0.3, 1],
                                durationSequence: [0.08, 0.08, 0.1, 0.15],
                                // initialDelay: 5.0,
                                initialOpacity: 0
                            });
                        }
                    }
                }
            });
        } else {
            // No border animation: Directly prepare to show current time (no 00 rollup)
            const now = new Date();
            const currentTime = { month: now.getMonth() + 1, day: now.getDate(), hours: now.getHours(), minutes: now.getMinutes(), seconds: now.getSeconds() };
            setPrevTimeParts(currentTime); // For CountUp to directly show current time
            setTimeParts(currentTime);
            animationControl.current.isInitialRollupPending = false;
            
            if (isMountedRef.current && stripedEl) {
                // 直接显示斜条纹容器，无边框动画时也添加闪烁效果
                AnimationUtils.createBlinkEffect(stripedEl, {
                    opacitySequence: [0.2, 0.7, 0.3, 1],
                    durationSequence: [0.08, 0.08, 0.1, 0.15],
                    // initialDelay: 5.0,
                    initialOpacity: 0
                });
            }
        }

        const timerId = setInterval(() => {
            if (!animationControl.current.isReadyForTimeUpdates || !isMountedRef.current || !animationControl.current.hasFadedIn) return;

            const now = new Date();
            const newTimeData = { month: now.getMonth() + 1, day: now.getDate(), hours: now.getHours(), minutes: now.getMinutes(), seconds: now.getSeconds() };

            if (animationControl.current.isInitialRollupPending) {
                setPrevTimeParts(zeroTime); // Start from 0
                setTimeParts(newTimeData);   // End at current time
                animationControl.current.isInitialRollupPending = false;
            } else {
                setTimeParts(currentTimeInState => {
                    // Guard against updates if currentTimeInState is still initialEmptyTime
                    if (Object.values(currentTimeInState).some(val => val === null)) return newTimeData;
                    setPrevTimeParts(currentTimeInState);
                    return newTimeData;
                });
            }
        }, 1000);

        // 清理函数，确保不会在卸载后还有状态更新
        return () => {
            clearInterval(timerId);
            if (mainEl) gsap.killTweensOf(mainEl);
            if (secondEl) gsap.killTweensOf(secondEl);
            if (timeDisplayRef.current) gsap.killTweensOf(timeDisplayRef.current);
            if (stripedEl) gsap.killTweensOf(stripedEl);
        };
    }, [animateBorder]);

    useEffect(() => {
        if (!isMountedRef.current) return;

        const timeEl = timeDisplayRef.current;
        if (isTimeTextRendered && timeEl) {
            // 1. Set to "00" for fade-in if border animation happened
            if (animationControl.current.isInitialRollupPending || animateBorder) {
                setPrevTimeParts(zeroTime);
                setTimeParts(zeroTime);
            }

            gsap.fromTo(timeEl,
                { autoAlpha: 0 },
                {
                    autoAlpha: 1, duration: 0.8, delay: 0.05,
                    onComplete: () => {
                        if (isMountedRef.current) {
                            animationControl.current.hasFadedIn = true;
                            animationControl.current.isReadyForTimeUpdates = true;
                        }
                    }
                }
            );
        } else if (!isTimeTextRendered && timeEl) {
            gsap.set(timeEl, { autoAlpha: 0 });
            animationControl.current.hasFadedIn = false;
        }
    }, [isTimeTextRendered, animateBorder]); // Add animateBorder dependency

    useEffect(() => {
        // 解析版本号格式，假设格式为 v_0.0.1 或类似格式
        if (version && !isVersionParsed) {
            // 修改正则表达式以适应不同版本号格式
            const versionMatch = version.match(/([a-zA-Z_]*)([0-9]+)[\._ ]?([0-9]+)?[\._ ]?([0-9]+)?/);
            if (versionMatch) {
                const prefix = versionMatch[1] || ''; // 前缀可能为空
                const majorVersion = parseInt(versionMatch[2] || '0', 10);
                const minorVersion = parseInt(versionMatch[3] || '0', 10);
                const patchVersion = parseInt(versionMatch[4] || '0', 10);
                
                // console.log('解析版本号:', version, '->', prefix, majorVersion, minorVersion, patchVersion); // 已注释掉，减少控制台输出
                
                // 设置起始为0
                setPrevVersionParts({
                    prefix,
                    numbers: [0, 0, 0]
                });
                
                // 设置目标版本号
                setVersionParts({
                    prefix,
                    numbers: [majorVersion, minorVersion, patchVersion]
                });
                
                setIsVersionParsed(true);
            } else {
                // console.log('无法解析版本号格式:', version); // 已注释掉，减少控制台输出
                // 如果无法解析，则直接设置原始版本号
                setVersionParts({ prefix: '', numbers: [0, 0, 1] });
                setPrevVersionParts({ prefix: '', numbers: [0, 0, 0] });
                setIsVersionParsed(true);
            }
        }
    }, [version]);

    // 控制版本号显示时机和闪烁效果
    useEffect(() => {
        if (!isTimeTextRendered || !isMountedRef.current) return;
        
        // 延迟显示版本号
        const versionTimer = setTimeout(() => {
            if (isMountedRef.current && versionContainerRef.current) {
                // 添加淡入动画效果
                gsap.fromTo(
                    versionContainerRef.current,
                    { autoAlpha: 0 },
                    { 
                        autoAlpha: 1, 
                        duration: 0.5, 
                        delay: 0.2,
                        onComplete: () => {
                            if (isMountedRef.current && versionTextRef.current) {
                                // console.log('开始版本号闪烁效果'); // 已注释掉，减少控制台输出
                                // 创建闪烁效果序列
                                AnimationUtils.createBlinkEffect(versionTextRef.current, {
                                    opacitySequence: [0, 1, 0, 1, 0, 1, 0.5, 1, 0.7, 1],
                                    durationSequence: [0.08, 0.05, 0.08, 0.05, 0.08, 0.1, 0.08, 0.1, 0.12, 0.2],
                                    initialOpacity: 0
                                });
                            }
                        }
                    }
                );
            }
        }, 300);
        
        return () => {
            clearTimeout(versionTimer);
            if (versionContainerRef.current) {
                gsap.killTweensOf(versionContainerRef.current);
            }
            if (versionTextRef.current) {
                gsap.killTweensOf(versionTextRef.current);
            }
        };
    }, [isTimeTextRendered]);

    const formatTimeVal = (val) => {
        if (val === null || typeof val === 'undefined') return ' '; // Render empty or space if null
        return val.toString().padStart(2, '0');
    };

    return (
        <div
            className="flex-1 flex relative h-[25px]"
        >
            {/* 你可以在这里添加其他需要居中或者占位的元素 */}
            {/* 主动画底部边线 (1px, #1C364A) */}
            <div
                ref={animatedMainBorderRef}
                className="absolute bottom-0 right-0 h-px bg-[rgba(90,170,180,0.3)] w-0"
            />

            {/* 第二条动画线 (2px, 新亮色, 位于主线上方) */}
            <div
                ref={animatedSecondaryLineRef}
                className="absolute bottom-0 right-0 h-[1.5px] bg-[#335566] w-0"
            />
            
            {/* 斜条纹背景容器 */}
            {
                isTimeTextRendered && (
                    <div
                        ref={stripedContainerRef}
                        className="absolute right-[120px] h-[15px] w-[100px] top-[5px] "
                        style={{
                            background: `repeating-linear-gradient(
                        -45deg,
                        transparent,
                        transparent 5px,
                        #1D2836 5px,
                        #1D2836 10px
                    )`
                        }}
                    >
                        {/* 版本号显示，使用闪烁效果 */}
                        <div 
                            ref={versionContainerRef}
                            className="absolute w-full h-full flex items-center justify-center opacity-0"
                        >
                            <span 
                                ref={versionTextRef}
                                className="text-[12px] font-['ChakraPetch-Light'] text-[#73DAEB] opacity-0"
                            >
                                {version}
                            </span>
                        </div>
                    </div>
                )
            }
            
            {/* 当前时间显示 - 使用 CountUp */}
            {isTimeTextRendered && (
                <div
                    ref={timeDisplayRef}
                    className="absolute right-[10px] text-[#73DAEB] text-[12px] leading-[25px] font-['ChakraPetch-Light'] opacity-0 invisible"
                >
                    [
                    {/* 月 */}
                    <CountUp className='ml-[5px] opacity-60' start={prevTimeParts.month === null ? 0 : prevTimeParts.month} end={timeParts.month === null ? 0 : timeParts.month} duration={0.8} formattingFn={formatTimeVal} />.
                    {/* 日 */}
                    <CountUp className='mr-[7px] opacity-60' start={prevTimeParts.day === null ? 0 : prevTimeParts.day} end={timeParts.day === null ? 0 : timeParts.day} duration={0.8} formattingFn={formatTimeVal} />
                    {/* 全角空格 */}
                    <CountUp start={prevTimeParts.hours === null ? 0 : prevTimeParts.hours} end={timeParts.hours === null ? 0 : timeParts.hours} duration={0.8} formattingFn={formatTimeVal} />:
                    <CountUp start={prevTimeParts.minutes === null ? 0 : prevTimeParts.minutes} end={timeParts.minutes === null ? 0 : timeParts.minutes} duration={0.8} formattingFn={formatTimeVal} />:
                    <CountUp className='mr-[5px]' start={prevTimeParts.seconds === null ? 0 : prevTimeParts.seconds} end={timeParts.seconds === null ? 0 : timeParts.seconds} duration={0.8} formattingFn={formatTimeVal} />
                    ]
                </div>
            )}
        </div>
    );
};

export default React.memo(FlexibleSpace); 