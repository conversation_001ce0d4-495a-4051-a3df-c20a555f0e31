import React from 'react';

// 将动画样式定义在模块作用域，只加载一次
if (typeof document !== 'undefined' && !document.getElementById('jhl-logo-styles')) {
    const styleElement = document.createElement('style');
    styleElement.id = 'jhl-logo-styles';
    styleElement.textContent = `
        @keyframes jhl-appear {
            0% { transform: scale(0); opacity: 0; }
            90% { transform: scale(1); opacity: 1; }
            100% { transform: scale(1); opacity: 1; }
        }
        
        .jhl-logo-container {
            position: relative;
            background-color: rgba(24, 82, 88, 0.5);
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: jhl-appear 1.5s cubic-bezier(0.19, 1, 0.22, 1) forwards;
        }
    `;
    document.head.appendChild(styleElement);
}

/**
 * JHL Logo 组件 - SVG实现版本
 * @returns {JSX.Element}
 */
const JHLLogo = React.memo(() => {
    return (
        <div className="jhl-logo-container">
            <svg width="25" height="25" viewBox="0 0 25 25">
                {/* 背景 - 已由外层div提供 */}
                
                {/* 使用defs和use实现精确的中心缩放 */}
                <defs>
                    <image 
                        id="logo-image" 
                        href="/favicon.ico" 
                        width="14" 
                        height="14" 
                    />
                </defs>
                
                <use 
                    href="#logo-image" 
                    x="5.5" 
                    y="5.5"
                >
                    <animateTransform
                        attributeName="transform"
                        attributeType="XML"
                        from="translate(7 7) scale(0) translate(-7 -7)"
                        to="translate(7 7) scale(1) translate(-7 -7)"
                        dur="0.8s"
                        begin="0.2s"
                        fill="freeze"
                        calcMode="spline"
                        keySplines="0.19 1 0.22 1"
                    />
                    <animate
                        attributeName="opacity"
                        from="0"
                        to="1"
                        dur="0.8s"
                        begin="0.2s"
                        fill="freeze"
                    />
                </use>
                
                {/* 四个角的点 */}
                <circle cx="1" cy="1" r="1" fill="#3ED5E9">
                    <animate
                        attributeName="opacity"
                        values="0.3;0.8;0.3"
                        dur="1.2s"
                        repeatCount="indefinite"
                    />
                </circle>
                
                <circle cx="24" cy="1" r="1" fill="#3ED5E9">
                    <animate
                        attributeName="opacity"
                        values="0.3;0.8;0.3"
                        dur="1.2s"
                        begin="0.1s"
                        repeatCount="indefinite"
                    />
                </circle>
                
                <circle cx="1" cy="24" r="1" fill="#3ED5E9">
                    <animate
                        attributeName="opacity"
                        values="0.3;0.8;0.3"
                        dur="1.2s"
                        begin="0.2s"
                        repeatCount="indefinite"
                    />
                </circle>
                
                <circle cx="24" cy="24" r="1" fill="#3ED5E9">
                    <animate
                        attributeName="opacity"
                        values="0.3;0.8;0.3"
                        dur="1.2s"
                        begin="0.3s"
                        repeatCount="indefinite"
                    />
                </circle>
            </svg>
        </div>
    );
});

// 设置组件显示名，方便调试
JHLLogo.displayName = 'JHLLogo';

export default JHLLogo; 
