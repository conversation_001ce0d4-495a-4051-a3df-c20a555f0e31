import React, { useState, useCallback } from 'react';
import <PERSON><PERSON><PERSON><PERSON> from './components/Header-Logo';
import TitleBar from './components/Title-Bar';
import HeaderCloseBtn from './components/Header-CloseBtn';
import HeaderMenu from './components/Header-Menu';
import HeaderMaximizeBtn from './components/Header-MaximizeBtn';
import HeaderResetBtn from './components/Header-ResetBtn';
import FlexibleSpace from './components/Header-Space';

// 性能优化：提取常量样式类名，避免重复创建字符串
const CONTAINER_STYLES = "flex flex-row items-center m-[10px] h-[38px] gap-[15px]";

/**
 * @description Header 组件，用于展示应用的头部信息和控制按钮
 * @param {Object} props - 组件属性
 * @param {Function} props.onLogout - 退出登录的回调函数
 * @param {Object} props.userData - 用户数据
 * @param {Function} props.onExitSystem - 退出系统的回调函数
 * @returns {JSX.Element} Header UI 界面
 * @constructor
 */
const Header = ({ onLogout, userData, onExitSystem }) => {
    const [isMenuReady, setIsMenuReady] = useState(false);

    // 性能优化：使用useCallback避免函数重复创建
    const handleMenuReady = useCallback(() => {
        setIsMenuReady(true);
    }, []);

    // 处理退出登录按钮点击 - 使用useCallback优化
    const handleLogout = useCallback(() => {
        if (typeof onLogout === 'function') {
            onLogout();
        }
    }, [onLogout]);

    return (
        // 主容器，使用 flex 布局，水平排列，垂直居中 - 使用常量样式
        <div className={CONTAINER_STYLES}>
            {/* JHL Logo */}
            <JHLLogo />
            {/* 标题栏 */}
            <TitleBar />
            {/* 头部菜单 */}
            <HeaderMenu onMenuReady={handleMenuReady} />

            {/* 封装后的弹性空间组件 */}
            <FlexibleSpace animateBorder={isMenuReady} />

            {/* 用户信息 */}
            {/* {userData && (
                <div className="flex items-center mr-[15px]">
                    <span className="text-[#67C8D0] text-[12px] font-['ChakraPetch-Light']">
                        {userData.username || '用户'}
                    </span>
                    <button 
                        onClick={handleLogout}
                        className="ml-[10px] text-[#67C8D0] text-[12px] font-['ChakraPetch-Light'] hover:text-[#9EEDF5] transition-colors"
                    >
                        退出
                    </button>
                </div>
            )} */}

            {/* 重置按钮 */}
            <HeaderResetBtn />
            {/* 最大化按钮 */}
            <HeaderMaximizeBtn />
            {/* 关闭按钮 */}
            <HeaderCloseBtn onExitSystem={onExitSystem} />
        </div>
    );
};

// 性能优化：使用浅比较来决定是否重新渲染
// 当关键props没有改变时，React将跳过渲染组件的操作并重用最近一次渲染的结果
const MemoizedHeader = React.memo(Header, (prevProps, nextProps) => {
    // 自定义比较函数，只对关键props进行比较
    return (
        prevProps.onLogout === nextProps.onLogout &&
        prevProps.onExitSystem === nextProps.onExitSystem &&
        prevProps.userData === nextProps.userData
    );
});

// 添加显示名称以便调试
MemoizedHeader.displayName = 'Header';

export default MemoizedHeader;