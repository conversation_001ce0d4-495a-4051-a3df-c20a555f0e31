import React, { useEffect, useRef, useCallback } from 'react';
import gsap from "gsap";
import './style.css';

// 性能优化：提取常量避免重复创建
const ANIMATION_CONFIG = {
    opacity: 1.0,
    scale: 1.0,
    duration: 2,
    ease: "power1.inOut"
};

const STYLE_CONFIG = {
    willChange: 'opacity, transform',
    willChangeAuto: 'auto',
    pointerEvents: 'none'
};

// 使用memo包装组件，避免不必要的重新渲染
const Background = React.memo(() => {
    // 背景
    const bg = useRef(null);

    // 性能优化：使用useCallback优化动画创建函数
    const createBackgroundAnimation = useCallback((element) => {
        if (!element) return null;
        
        // 添加性能优化属性
        element.style.willChange = STYLE_CONFIG.willChange;
        
        // 创建动画并立即执行 - 使用常量配置
        const tl = gsap.timeline();
        tl.to(element, ANIMATION_CONFIG);
        
        return tl;
    }, []);

    // 性能优化：使用useCallback优化清理函数
    const cleanupAnimation = useCallback((timeline, element) => {
        if (timeline) {
            timeline.kill();
        }
        if (element) {
            element.style.willChange = STYLE_CONFIG.willChangeAuto;
            gsap.killTweensOf(element);
        }
    }, []);

    useEffect(() => {
        const timeline = createBackgroundAnimation(bg.current);
        
        // 清理函数
        return () => {
            cleanupAnimation(timeline, bg.current);
        };
    }, [createBackgroundAnimation, cleanupAnimation]);

    // 注释：这些是早期开发时的实验代码，已移除以提高性能

    return (
        <div ref={bg} className='grid-background' style={{ pointerEvents: STYLE_CONFIG.pointerEvents }} />
    );
});

// 添加显示名称以便调试
Background.displayName = 'Background';

export default Background;
