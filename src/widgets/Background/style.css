.grid-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;

    /* 背景图案 - 圆点模式优化 */
    background-image:
        radial-gradient(circle at 15px 15px, #2D4443 0.5px, transparent 0.5px), /* 原色点 */
        radial-gradient(circle at 45px 15px, #5d6e6d 0.5px, transparent 0.5px), /* 浅色点 */
        radial-gradient(circle at 15px 45px, #5d6e6d 0.5px, transparent 0.5px), /* 浅色点 */
        radial-gradient(circle at 45px 45px, #2D4443 0.5px, transparent 0.5px);  /* 原色点 */
    background-size: 60px 60px; /* 控制重复单元的大小 (现在包含2x2个点) */

    /* background-position: right; 加号位置，中心对齐 - 已移除 */
    opacity: 0;
    scale: 0.95;
}

/* 注释：毛玻璃效果代码已移除以减少文件大小，如需要可重新添加 */
