.login_main_div {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.login_title_div {
    border-left: 1px solid #2a2a2a;
    border-right: 1px solid #2a2a2a;
    pointer-events: none;
    position: relative;
    opacity: 0.0;

    /* 半透明效果 */
    /* background: rgb(255, 255, 255, 0.01); !* 白色背景，调整透明度为50% *! */
    background-image: linear-gradient(to bottom right, rgba(255, 255, 255, 0.02), rgba(255, 255, 255, 0.01));

    /* 磨砂玻璃效果 */
    backdrop-filter: blur(2px); /* 模糊度根据需要调整 */

    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 40px;


    /* Effect */
    height: 100px;
    transform: scaleY(0);
}

.login_leftTop_left {
    background-color: var(--div-corner-color);
    position: absolute;
    border-radius: 99px;
    height: 20px;
    width: 1px;
    left: -1px;
    top: 0;
}

.login_leftTop_top {
    background-color: var(--div-corner-color);
    position: absolute;
    border-radius: 99px;
    height: 1px;
    width: 20px;
    left: 0;
    top: 0;
}

.login_leftBottom_left {
    background-color: var(--div-corner-color);
    position: absolute;
    border-radius: 99px;
    height: 20px;
    width: 1px;
    left: -1px;
    bottom: 0;
}

.login_leftBottom_bottom {
    background-color: var(--div-corner-color);
    position: absolute;
    border-radius: 99px;
    height: 1px;
    width: 20px;
    left: 0;
    bottom: 0;
}

.login_rightTop_top {
    background-color: var(--div-corner-color);
    position: absolute;
    border-radius: 99px;
    height: 1px;
    width: 20px;
    right: 0;
    top: 0;
}

.login_rightTop_right {
    background-color: var(--div-corner-color);
    position: absolute;
    border-radius: 99px;
    height: 20px;
    width: 1px;
    right: -1px;
    top: 0;
}

.login_rightBottom_right {
    background-color: var(--div-corner-color);
    position: absolute;
    border-radius: 99px;
    height: 20px;
    width: 1px;
    right: -1px;
    bottom: 0;
}

.login_rightBottom_bottom {
    background-color: var(--div-corner-color);
    position: absolute;
    border-radius: 99px;
    height: 1px;
    width: 20px;
    right: -1px;
    bottom: 0;
}

.login_title_txt {
    /*opacity: 0;*/
    width: 0;
    height: 0;

    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    font-size: 40px;
    user-select: none;
}

.login_title_word {
    opacity: 0;
}

/* 账号框 */
.login_account_div {
    margin-top: 20px;

    display: flex;
    justify-content: center;
    align-items: center;
    align-self: center;

    position: relative;
    border-top: 1px solid #2a2a2a;
    border-bottom: 1px solid #2a2a2a;
    background-image: linear-gradient(to bottom right, rgba(255, 255, 255, 0.02), rgba(255, 255, 255, 0.01));


    width: 500px;
    transform: scaleX(0);
    height: 0;
    opacity: 0;
}

.login_account_div_leftTop_Dot {
    width: 3px;
    height: 3px;
    border-radius: 999px;
    background: var(--div-corner-color);
    position: absolute;
    top: -1px;
    left: -1px;
}

.login_account_div_rightTop_Dot {
    width: 3px;
    height: 3px;
    border-radius: 999px;
    background: var(--div-corner-color);
    position: absolute;
    top: -1px;
    right: -1px;
}

.login_account_div_leftBottom_Dot {
    width: 3px;
    height: 3px;
    border-radius: 999px;
    background: var(--div-corner-color);
    position: absolute;
    left: -1px;
    bottom: -1px;
}

.login_account_div_rightBottom_Dot {
    width: 3px;
    height: 3px;
    border-radius: 999px;
    background: var(--div-corner-color);
    position: absolute;
    bottom: -1px;
    right: -1px;
}

.login_account_input_div {
    opacity: 0;
}

.login_account-input {
    width: 500px; /* Input宽度100% */
    height: 100%; /* Input高度100% */
    text-align: center; /* 文本居中 */
    font-size: 20px;
    font-family: 'DingTalkJinBuTi', serif;
    background-color: transparent;
    border-width: 0;
    color: #ffffff;
    outline: none;
}

.login_account-input::placeholder {
    color: #606060;
}

.login_account-input:focus {
    box-shadow: none !important;
}


/* 密码框 */
.login_pwd_div {
    margin-top: 20px;

    display: flex;
    justify-content: center;
    align-items: center;
    align-self: center;

    position: relative;
    border-top: 1px solid #2a2a2a;
    border-bottom: 1px solid #2a2a2a;
    background-image: linear-gradient(to bottom right, rgba(255, 255, 255, 0.02), rgba(255, 255, 255, 0.01));


    width: 500px;
    transform: scaleX(0);
    height: 0;
    opacity: 0;
}

.login_pwd_div_leftTop_Dot {
    width: 3px;
    height: 3px;
    border-radius: 999px;
    background: var(--div-corner-color);
    position: absolute;
    top: -1px;
    left: -1px;
}

.login_pwd_div_rightTop_Dot {
    width: 3px;
    height: 3px;
    border-radius: 999px;
    background: var(--div-corner-color);
    position: absolute;
    top: -1px;
    right: -1px;
}

.login_pwd_div_leftBottom_Dot {
    width: 3px;
    height: 3px;
    border-radius: 999px;
    background: var(--div-corner-color);
    position: absolute;
    left: -1px;
    bottom: -1px;
}

.login_pwd_div_rightBottom_Dot {
    width: 3px;
    height: 3px;
    border-radius: 999px;
    background: var(--div-corner-color);
    position: absolute;
    bottom: -1px;
    right: -1px;
}

.login_pwd_input_div {
    opacity: 0;
}

.ant-input-borderless {
    width: 500px; /* Input宽度100% */
    height: 100%; /* Input高度100% */
    text-align: center; /* 文本居中 */
    font-size: 20px;
    font-family: 'DingTalkJinBuTi', serif;
    background-color: transparent;
    border-width: 0;
    color: #ffffff;
    outline: none;
}

.ant-input-borderless::placeholder {
    color: #606060;
}

.ant-input-borderless:focus {
    box-shadow: none !important;
}

/* 登录按钮 */
.login_logBtn {
    width: 500px;
    height: 50px;
    margin-top: 50px;
    cursor: pointer;
    background: linear-gradient(-45deg, transparent 10px, #1e1e1e 0);
    /*background: linear-gradient(to right, #11202C, #11202C);*/
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    font-size: 25px;
    opacity: 0;
    border-top: 1px solid #2a2a2a;
    pointer-events: none;
    user-select: none;
}

.login_logBtn_icon {
    font-size: 20px;
    color: #ffffff;
    position: absolute;
    right: 30px;
    pointer-events: none;
}

.login_logBtn_leftTopDot {
    width: 10px;
    height: 10px;
    border-radius: 999px;
    position: absolute;
    left: 5px;
    top: 5px;
    background-color: var(--stateGrey-color);
}

.login_logBtn_corner {
    pointer-events: none;
    position: absolute;
    width: 500px;
    height: 50px;
    transform: scale(1.02, 1.2);
    opacity: 0;
}

.login_logBtn_corner_leftTop_left {
    pointer-events: none;
    background-color: var(--div-corner-color);
    position: absolute;
    border-radius: 99px;
    height: 10px;
    width: 1px;
    left: -2px;
    top: -2px;
}

.login_logBtn_corner_leftTop_top {
    pointer-events: none;
    background-color: var(--div-corner-color);
    position: absolute;
    border-radius: 99px;
    height: 1px;
    width: 10px;
    left: -2px;
    top: -2px;
}

.login_logBtn_corner_leftBottom_left {
    pointer-events: none;
    background-color: var(--div-corner-color);
    position: absolute;
    border-radius: 99px;
    height: 10px;
    width: 1px;
    left: -2px;
    bottom: -2px;
}

.login_logBtn_corner_leftBottom_bottom {
    pointer-events: none;
    background-color: var(--div-corner-color);
    position: absolute;
    border-radius: 99px;
    height: 1px;
    width: 10px;
    left: -2px;
    bottom: -2px;
}

.login_logBtn_corner_rightTop_right {
    pointer-events: none;
    background-color: var(--div-corner-color);
    position: absolute;
    border-radius: 99px;
    height: 10px;
    width: 1px;
    right: -2px;
    top: -2px;
}

.login_logBtn_corner_rightTop_top {
    pointer-events: none;
    background-color: var(--div-corner-color);
    position: absolute;
    border-radius: 99px;
    height: 1px;
    width: 10px;
    right: -2px;
    top: -2px;
}

.login_logBtn_corner_rightBottom_right {
    pointer-events: none;
    background-color: var(--div-corner-color);
    position: absolute;
    border-radius: 99px;
    height: 10px;
    width: 1px;
    right: -2px;
    bottom: -2px;
}

.login_logBtn_corner_rightBottom_bottom {
    pointer-events: none;
    background-color: var(--div-corner-color);
    position: absolute;
    border-radius: 99px;
    height: 1px;
    width: 10px;
    right: -2px;
    bottom: -2px;
}

.login_loading_div {
    width: 30px;
    height: 30px;
    overflow: hidden;
    position: absolute;
    left: 10px;
    top: 10px;
}

.login_loading_line {
    width: 100px;
    height: 30px;

    background: repeating-linear-gradient(135deg, var(--stateGrey-color) 0px, var(--stateGrey-color) 3.5px, transparent 3.5px, transparent 10px);

    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
}

.login_error_div {
    /* opacity: 0; */
    width: 500px;
    height: 150px;
    position: absolute;
    top: calc(50% + 140px);
    pointer-events: none;
}

.login_error_first_dot {
    width: 5px;
    height: 5px;
    border-radius: 999px;
    background-color: var(--yangcheng-color);
    position: absolute;
    top: 25px;
    left: 22px;
    opacity: 0;
}

.login_error_vLine {
    width: 1px;
    /* height: 80px; */
    height: 0;
    background-color: var(--stateGrey-color);
    position: absolute;
    top: 30px;
    left: 24px;
}

.login_error_end_dot {
    width: 3px;
    height: 3px;
    border-radius: 999px;
    background-color: var(--div-corner-color);
    position: absolute;
    top: 110px;
    left: 23px;
    opacity: 0;
}

.login_error_hLine {
    /* width: 200px; */
    width: 0;
    height: 1px;
    background-color: var(--stateGrey-color);
    position: absolute;
    top: 111px;
    left: 26px;
}

.login_error_pan_1 {
    font-size: 20px;
    color: #FFFFFF;
    position: absolute;
    /* top: 83px; */
    top: 90px;
    opacity: 0;
    left: 50px;
    height: 30px;
    overflow: hidden;
    /* 英文斜体更美观 */
    /*transform: skewX(7deg);*/
}

.login_error_pan_2 {
    font-size: 12px;
    color: #000000;
    background: linear-gradient(-45deg, transparent 6px, var(--yangcheng-color) 0);
    position: absolute;
    top: 117px;
    left: 50px;
    padding-left: 10px;
    padding-right: 10px;
    overflow: hidden;
    height: 0;
}

.accountErrorDiv {
    position: absolute;
    width: 400px;
    height: 150px;
    /*background-color: var(--edex-color);*/
    left: 497px;
    bottom: 0;
}
.accountErrorDivDot1 {
    width: 5px;
    height: 5px;
    border-radius: 999px;
    background-color: var(--yangcheng-color);
    position: absolute;
    left: 0;
    bottom: 30px;
    opacity: 0;
}
.accountErrorDivLine1 {
    width: 50px;
    height: 1px;
    background-color: var(--stateGrey-color);
    position: absolute;
    left: 5px;
    bottom: 32px;

    transform: scaleX(0);
}
.accountErrorDivLine2 {
    height: 1px;
    background-color: var(--stateGrey-color);
    position: absolute;
    left: 55px;
    bottom: 32px;
    transform: rotate(-30deg);

    /*width: 50px;*/
    width: 0;
}
.accountErrorDivLine3 {
    width: 150px;
    height: 1px;
    background-color: var(--stateGrey-color);
    position: absolute;
    left: 97px;
    bottom: 57px;

    transform: scaleX(0);
}
.account_error_pan_1 {
    font-size: 20px;
    color: #FFFFFF;
    position: absolute;
    height: 30px;
    overflow: hidden;
    left: 115px;
    bottom: 57px;

    /* 英文斜体更美观 */
    /*transform: skewX(7deg);*/

    opacity: 0;
    transform: translateY(5px) skewX(5deg);
}
.account_error_pan_2 {
    font-size: 12px;
    color: #000000;
    background: linear-gradient(-45deg, transparent 6px, var(--yangcheng-color) 0);
    position: absolute;
    bottom: 35px;
    left: 115px;
    padding-left: 10px;
    padding-right: 10px;
    overflow: hidden;
    /*height: 0;*/

    opacity: 0;
    transform: translateY(-5px);
}

.pwdErrorDiv {
    position: absolute;
    width: 400px;
    height: 150px;
    /*background-color: var(--edex-color);*/
    left: 497px;
    bottom: 0;
}
.pwdErrorDivDot1 {
    width: 5px;
    height: 5px;
    border-radius: 999px;
    background-color: var(--yangcheng-color);
    position: absolute;
    left: 0;
    bottom: 30px;
    opacity: 0;
}
.pwdErrorDivLine1 {
    width: 50px;
    height: 1px;
    background-color: var(--stateGrey-color);
    position: absolute;
    left: 5px;
    bottom: 32px;

    transform: scaleX(0);
}
.pwdErrorDivLine2 {
    height: 1px;
    background-color: var(--stateGrey-color);
    position: absolute;
    left: 55px;
    bottom: 32px;
    transform: rotate(30deg);

    /*width: 50px;*/
    width: 0;
}
.pwdErrorDivLine3 {
    width: 150px;
    height: 1px;
    background-color: var(--stateGrey-color);
    position: absolute;
    left: 97px;
    bottom: 7px;

    transform: scaleX(0);
}
.pwd_error_pan_1 {
    font-size: 20px;
    color: #FFFFFF;
    position: absolute;
    height: 30px;
    overflow: hidden;
    left: 115px;
    bottom: 7px;

    /* 英文斜体更美观 */
    /*transform: skewX(7deg);*/

    opacity: 0;
    transform: translateY(5px) skewX(5deg);
}
.pwd_error_pan_2 {
    font-size: 12px;
    color: #000000;
    background: linear-gradient(-45deg, transparent 6px, var(--yangcheng-color) 0);
    position: absolute;
    bottom: -15px;
    left: 115px;
    padding-left: 10px;
    padding-right: 10px;
    overflow: hidden;
    /*height: 0;*/

    opacity: 0;
    transform: translateY(-5px);
}


/* 登录按钮-enter-四角 Corner */
@keyframes loginBtnCornerShowEff {
    0% {
        transform: scale(1.02, 1.2);
        opacity: 0;
    }
    100% {
        transform: scale(1, 1);
        opacity: 1;
    }
}
@keyframes loginBtnCornerCloseEff {
    0% {
        transform: scale(1, 1);
        opacity: 1;
    }
    100% {
        transform: scale(1.02, 1.2);
        opacity: 0;
    }
}
.loginBtnCornerShow {
    animation: loginBtnCornerShowEff 0.3s forwards;
}
.loginBtnCornerClose {
    animation: loginBtnCornerCloseEff 0.3s forwards;
}


/* 登录按钮-out-箭头 Arrow */
@keyframes loginBtnArrowShowEff {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-5px);
    }
}
@keyframes loginBtnArrowCloseEff {
    0% {
        transform: translateX(-5px);
    }
    100% {
        transform: translateX(0);
    }
}
.loginBtnArrowShow {
    animation: loginBtnArrowShowEff 0.3s forwards;
}
.loginBtnArrowClose {
    animation: loginBtnArrowCloseEff 0.3s forwards;
}


/*loginBtnArrow.current.classList.add('loginBtnArrowNormal');*/
/*loginBtnArrow.current.addEventListener('animationend', () => {*/
/*    element.classList.remove(animationClass);*/
/*}, { once: true });*/


/* 登录按钮-icon 橙条  */
/*@keyframes loginBtnIconPlayEff {*/
/*    0% {*/
/*        transform: translateX(0);*/
/*        !* 第一阶段持续1秒 *!*/
/*        !*animation-duration: 1s;*!*/
/*    }*/
/*    100% {*/
/*        transform: translateX(42px);*/
/*    }*/
/*}*/
/*.loginBtnIconPlay {*/
/*    background: repeating-linear-gradient(135deg, var(--yangcheng-color) 0px, var(--yangcheng-color) 3.5px, transparent 3.5px, transparent 10px);*/
/*    !* animation: name duration timing-function delay iteration-count direction fill-mode play-state; *!*/
/*    animation: loginBtnIconPlayEff 2s linear infinite;*/
/*}*/
