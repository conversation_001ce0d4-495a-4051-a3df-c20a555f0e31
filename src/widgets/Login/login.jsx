import { useEffect, useRef, useState } from 'react';
import { Input } from "antd";
import { CaretRightOutlined } from '@ant-design/icons';
import Utils from "../../utils/utils.js";
import gsap from "gsap";
import { click_loginBtn } from './login.js';
// import {How<PERSON>, Howler} from 'howler';

import './login.css';


export default function Login({ success }) {

    // 将众多 ref 整合到对象中，减少重复定义
    const refs = {
        // 主容器
        loginDiv: useRef(null),
        // 标题框
        titleDiv: useRef(null),
        // 标题 refs (预分配足够空间，例如20个字符)
        title: Array(20).fill().map(() => useRef(null)),
        // 输入框组
        accountDiv: useRef(null),
        accountInput: useRef(null),
        pwdDiv: useRef(null),
        pwdInput: useRef(null),
        // 登录按钮组
        loginBtn: useRef(null),
        loginBtnCorner: useRef(null),
        loginBtnArrow: useRef(null),
        loadingDiv: useRef(null),
        // 错误提示组
        errorDiv: useRef(null),
        errDot1: useRef(null),
        errLin1: useRef(null),
        errDot2: useRef(null),
        errLin2: useRef(null),
        errPan1: useRef(null),
        errPan2: useRef(null),
        // 账号错误提示组
        accountErrDiv: useRef(null),
        accountErrDot1: useRef(null),
        accountErrLine1: useRef(null),
        accountErrLine2: useRef(null),
        accountErrLine3: useRef(null),
        accoountErrPan1: useRef(null),
        accoountErrPan2: useRef(null),
        // 密码错误提示组
        pwdErrDiv: useRef(null),
        pwdErrDot1: useRef(null),
        pwdErrLine1: useRef(null),
        pwdErrLine2: useRef(null),
        pwdErrLine3: useRef(null),
        pwdErrPan1: useRef(null),
        pwdErrPan2: useRef(null)
    };

    // 登录按钮 hover
    const [isLoginBtnHovered, setIsLoginBtnHovered] = useState(false);
    // 账号
    const [account, setAccount] = useState('');
    // 密码
    const [pwd, setPwd] = useState('');
    // 标题文字
    // const [titleString, setTitleString] = useState('智慧水务平台欢迎您');
    // const [titleString, setTitleString] = useState('贵阳市水务环境集团清镇水务有限公司');
    const [titleString, setTitleString] = useState('智慧水务数字孪生平台');



    // 标题框初始动画 - 优化版
    useEffect(() => {
        // 创建一个通用函数来设置willChange属性
        const setWillChange = (elements, value) => {
            elements.forEach(el => {
                if (el && el.current) el.current.style.willChange = value;
            });
        };

        // 动态确定标题长度
        const currentTitleLength = titleString.length;

        // 收集需要应用willChange的元素
        const titleCharacterElements = refs.title.slice(0, currentTitleLength).filter(ref => ref && ref.current);
        const titleElements = [refs.titleDiv, ...titleCharacterElements];

        // 设置willChange属性
        setWillChange(titleElements, 'opacity, transform, width');

        // 创建timeline实例
        let tl = gsap.timeline({
            onComplete: () => {
                // 动画结束时移除willChange
                setWillChange(titleElements, 'auto');
            }
        });

        // 添加标题容器动画
        tl.to(refs.titleDiv.current, { opacity: 1.0, scaleY: 1, duration: 0.5, delay: 1, ease: "power1.inOut" })
            .to(refs.titleDiv.current, { width: '500px', duration: 1, ease: "power4.inOut" });

        // 添加标题文字闪烁动画 - 使用循环简化代码
        refs.title.slice(0, currentTitleLength).forEach((wordRef, index) => {
            // 确保 wordRef 和 wordRef.current 存在
            if (wordRef && wordRef.current) {
                // 为第一个字符（"智"）添加额外延迟，其他字符保持原有动画效果
                const offset = index > 0 ? "<-=0.08" : "+=0.5";
                tl.to(wordRef.current, { opacity: 0.7, duration: 0.04 }, offset)
                    .to(wordRef.current, { opacity: 0.2, duration: 0.04 })
                    .to(wordRef.current, { opacity: 0.5, duration: 0.04 })
                    .to(wordRef.current, { opacity: 1.0, duration: 0.04 });
            }
        });

        // 返回清理函数
        return () => {
            // 杀死所有元素的tween动画
            if (refs.titleDiv.current) gsap.killTweensOf(refs.titleDiv.current);
            refs.title.slice(0, currentTitleLength).forEach(ref => {
                if (ref && ref.current) gsap.killTweensOf(ref.current);
            });
            tl.kill();
        };
    }, [titleString.length]); // 当 titleString 长度变化时，重新运行动画

    /**
     * 创建一个通用的初始化动画函数，用于账号框和密码框
     * @param {React.RefObject} divRef - 输入框容器的ref
     * @param {React.RefObject} inputRef - 输入框本身的ref
     * @param {number} delay - 动画延迟时间
     */

    // 创建一个通用的初始化动画函数，用于账号框和密码框
    const createInputAnimation = (divRef, inputRef, delay = 2) => {
        useEffect(() => {
            // 设置willChange
            divRef.current.style.willChange = 'opacity, transform, height';
            inputRef.current.style.willChange = 'opacity';

            let tl = gsap.timeline({
                onComplete: () => {
                    // 移除willChange
                    divRef.current.style.willChange = 'auto';
                    inputRef.current.style.willChange = 'auto';
                }
            });

            tl.to(divRef.current, { scaleX: 1, opacity: 1.0, duration: 0.3, delay, ease: "power1.inOut" })
                .to(divRef.current, { height: '70px', duration: 0.3, ease: "power1.inOut" })
                .to(inputRef.current, { opacity: 1, duration: 0.3 });

            return () => {
                gsap.killTweensOf(divRef.current);
                gsap.killTweensOf(inputRef.current);
                tl.kill();
            };
        }, []);
    };

    // 应用通用动画函数
    createInputAnimation(refs.accountDiv, refs.accountInput);
    createInputAnimation(refs.pwdDiv, refs.pwdInput);

    // 登录按钮初始化动画
    useEffect(() => {
        let tl = gsap.timeline({
            onComplete: () => {
                gsap.set(refs.loginBtn.current, { pointerEvents: 'auto' })
                // remove will-change
                refs.loginBtn.current.style.willChange = 'auto';
            }
        });

        // will-change
        refs.loginBtn.current.style.willChange = 'opacity';

        tl.to(refs.loginBtn.current, { opacity: 0.8, delay: 2.8, duration: 0.06 })
            .to(refs.loginBtn.current, { opacity: 0.2, duration: 0.06 })
            .to(refs.loginBtn.current, { opacity: 0.7, duration: 0.06 })
            .to(refs.loginBtn.current, { opacity: 0.5, duration: 0.06 })
            .to(refs.loginBtn.current, { opacity: 1.0, duration: 0.1 })

        return () => {
            gsap.killTweensOf(refs.loginBtn.current);
            tl.kill();
            tl = null;
        };
    }, []);



    // 点击登录按钮
    const loginBtnClick = () => {
        // Eff
        loginBtnClickEff();
        // Fun
        loginBtnClickFun();
    };
    // 登录按钮点击效果
    const loginBtnClickEff = () => {
        // 登录按钮 - 闪烁
        let tl = gsap.timeline({
            onComplete: () => {
                tl.kill();
                tl = null;
            }
        });
        tl.to(refs.loginBtn.current, { opacity: 0.3, scale: 0.99, duration: 0.07 })
            .to(refs.loginBtn.current, { opacity: 0.7, duration: 0.07 })
            .to(refs.loginBtn.current, { opacity: 0.5, duration: 0.07 })
            .to(refs.loginBtn.current, { opacity: 0.8, duration: 0.07 })
            .to(refs.loginBtn.current, { opacity: 1.0, scale: 1.0, duration: 0.1 })

        // 角落消失
        // gsap.to(loginBtnCorner.current, {opacity: 0.0, width: '510px', height: '60px', duration: 0.3});

        // 橙条 Loading
        showLoginBtnLoading();
    };
    // 登录按钮点击执行 - 优化版
    const loginBtnClickFun = () => {
        // 登录错误
        if ((account === '' || pwd === '') && !refs.errorDiv.current.playing && !refs.errorDiv.current.show) {
            showLoginError();
        } else if (!refs.errorDiv.current.playing && refs.errorDiv.current.show) {
            closeLoginError();
        }

        // 账号错误检查
        if (account === '' && !refs.accountErrDiv.current.playing && !refs.accountErrDiv.current.show) {
            accountError.showError();
        } else if (account !== '' && !refs.accountErrDiv.current.playing && refs.accountErrDiv.current.show) {
            accountError.closeError();
        }

        // 密码错误检查
        if (pwd === '' && !refs.pwdErrDiv.current.playing && !refs.pwdErrDiv.current.show) {
            pwdError.showError();
        } else if (pwd !== '' && !refs.pwdErrDiv.current.playing && refs.pwdErrDiv.current.show) {
            pwdError.closeError();
        }

        // 登录成功
        if (account !== '' && pwd !== '') {
            setTimeout(() => {
                closeLogin();
            }, 500);
        }
    }
    // 显示错误弹窗
    const showLoginError = () => {
        refs.errorDiv.current.playing = true;
        let tl = gsap.timeline({
            ease: "power4.inOut",
            onComplete: () => {
                refs.errorDiv.current.playing = false;
                refs.errorDiv.current.show = true;
                tl.kill();
                tl = null;
            }
        });
        tl.to(refs.errDot1.current, { opacity: 1.0, duration: 0.15 })
            .to(refs.errLin1.current, { height: '80px', duration: 0.15 })
            .to(refs.errDot2.current, { opacity: 1.0, duration: 0.15 })
            .to(refs.errLin2.current, { width: '200px', duration: 0.15 })
            .to(refs.errPan1.current, { top: '83px', opacity: 1.0, transformOrigin: 'bottom', duration: 0.2 })
            .to(refs.errPan2.current, { height: '18px', duration: 0.2 }, "<")
    };
    // 关闭错误弹窗
    const closeLoginError = () => {
        refs.errorDiv.current.playing = true;
        let tl = gsap.timeline({
            ease: "power4.inOut",
            onComplete: () => {
                refs.errorDiv.current.playing = false;
                refs.errorDiv.current.show = false;
                tl.kill();
                tl = null;
            }
        });
        tl.to(refs.errPan2.current, { height: 0, duration: 0.05 }, "<")
            .to(refs.errPan1.current, { top: '90px', opacity: 0, transformOrigin: 'bottom', duration: 0.05 })
            .to(refs.errLin2.current, { width: 0, duration: 0.05 })
            .to(refs.errDot2.current, { opacity: 0, duration: 0.05 })
            .to(refs.errLin1.current, { height: 0, duration: 0.05 })
            .to(refs.errDot1.current, { opacity: 0, duration: 0.05 })
    };

    // 创建通用的错误提示显示/隐藏函数
    const createErrorToggle = (config) => {
        const { divRef, dotRef, line1Ref, line2Ref, line3Ref, pan1Ref, pan2Ref } = config;

        const showError = () => {
            divRef.current.playing = true;
            let tl = gsap.timeline({
                ease: "power4.inOut",
                onComplete: () => {
                    divRef.current.playing = false;
                    divRef.current.show = true;
                    tl.kill();
                }
            });

            tl.to(dotRef.current, { opacity: 1, duration: 0.15 })
                .to(line1Ref.current, { scaleX: 1, transformOrigin: 'left', duration: 0.15 })
                .to(line2Ref.current, { width: 50, transformOrigin: 'left', duration: 0.15 })
                .to(line3Ref.current, { scaleX: 1, transformOrigin: 'left', duration: 0.15 })
                .to(pan1Ref.current, { opacity: 1, y: '-=5', transformOrigin: 'bottom', duration: 0.2 })
                .to(pan2Ref.current, { opacity: 1, y: '+=5', duration: 0.2 }, "<");
        };

        const closeError = () => {
            divRef.current.playing = true;
            let tl = gsap.timeline({
                ease: "power4.inOut",
                onComplete: () => {
                    divRef.current.playing = false;
                    divRef.current.show = false;
                    tl.kill();
                }
            });

            tl.to(pan2Ref.current, { opacity: 0.0, y: '-=5', duration: 0.05 })
                .to(pan1Ref.current, { opacity: 0.0, y: '+=5', transformOrigin: 'bottom', duration: 0.05 }, "<")
                .to(line3Ref.current, { scaleX: 0, transformOrigin: 'left', duration: 0.15 })
                .to(line2Ref.current, { width: 0, transformOrigin: 'left', duration: 0.05 })
                .to(line1Ref.current, { scaleX: 0, transformOrigin: 'left', duration: 0.05 })
                .to(dotRef.current, { opacity: 0, duration: 0.05 });
        };

        return { showError, closeError };
    };

    // 应用错误提示通用函数
    const accountError = createErrorToggle({
        divRef: refs.accountErrDiv,
        dotRef: refs.accountErrDot1,
        line1Ref: refs.accountErrLine1,
        line2Ref: refs.accountErrLine2,
        line3Ref: refs.accountErrLine3,
        pan1Ref: refs.accoountErrPan1,
        pan2Ref: refs.accoountErrPan2
    });

    const pwdError = createErrorToggle({
        divRef: refs.pwdErrDiv,
        dotRef: refs.pwdErrDot1,
        line1Ref: refs.pwdErrLine1,
        line2Ref: refs.pwdErrLine2,
        line3Ref: refs.pwdErrLine3,
        pan1Ref: refs.pwdErrPan1,
        pan2Ref: refs.pwdErrPan2
    });

    // 登录成功，关闭登录页
    const closeLogin = () => {
        //  设置页面不可交互
        gsap.set(refs.loginBtn.current, { pointerEvents: 'none' })

        let tl = gsap.timeline({
            ease: "power4.inOut",
            onComplete: () => {
                // 传递用户信息给父组件
                const userData = {
                    username: account,
                    loginTime: new Date().toLocaleString(),
                };
                success(userData);
                tl.kill();
            }
        });
        tl.to(refs.loginBtn.current, { opacity: 0.8, duration: 0.1 })
            .to(refs.loginBtn.current, { opacity: 0.5, duration: 0.03 })
            .to(refs.loginBtn.current, { opacity: 0.7, duration: 0.03 })
            .to(refs.loginBtn.current, { opacity: 0.5, duration: 0.03 })
            .to(refs.loginBtn.current, { opacity: 0, duration: 0.05 })

            .to(refs.pwdInput.current, { opacity: 0, duration: 0.1 })
            .to(refs.pwdDiv.current, { height: 0, duration: 0.1, ease: "power1.inOut" })
            .to(refs.pwdDiv.current, { scaleX: 0, opacity: 0, duration: 0.1, ease: "power1.inOut" })

            .to(refs.accountInput.current, { opacity: 0, duration: 0.1 }, "<")
            .to(refs.accountDiv.current, { height: 0, duration: 0.1, ease: "power1.inOut" })
            .to(refs.accountDiv.current, { scaleX: 0, opacity: 0, duration: 0.1, ease: "power1.inOut" })

            .to(refs.titleDiv.current, { opacity: 0, scaleY: 0, duration: 0.15, ease: "power1.inOut" })
            .to(refs.titleDiv.current, { width: 0, duration: 0.15, ease: "power4.inOut" }, "<")
    }

    // 橙条 Loading
    const showLoginBtnLoading = () => {
        if (!refs.loadingDiv.current.animation) {
            // 开始Loading，设置斜条为橙色
            gsap.set(refs.loadingDiv.current, { background: 'repeating-linear-gradient(135deg, var(--yangcheng-color) 0px, var(--yangcheng-color) 3.5px, transparent 3.5px, transparent 10px)' });
            // 播放橙色Loading移动动画
            refs.loadingDiv.current.animation = gsap.to(refs.loadingDiv.current, {
                right: '-42px',
                duration: 2,
                ease: "none",
                repeat: -1
            });
        } else {
            if (refs.loadingDiv.current.animation.isActive()) {
                gsap.set(refs.loadingDiv.current, {
                    background: 'repeating-linear-gradient(135deg, var(--stateGrey-color) 0px, var(--stateGrey-color) 3.5px, transparent 3.5px, transparent 10px)',
                    right: 0
                });
                // loadingDiv.current.animation.kill();
                // loadingDiv.current.animation = null;
                refs.loadingDiv.current.animation.pause();
            } else {
                gsap.set(refs.loadingDiv.current, { background: 'repeating-linear-gradient(135deg, var(--yangcheng-color) 0px, var(--yangcheng-color) 3.5px, transparent 3.5px, transparent 10px)' });
                refs.loadingDiv.current.animation.play();
            }
        }
    }

    return (
        <div className='w-screen h-screen flex items-center justify-center'>

            <div ref={refs.loginDiv} className='login_main_div'>

                {/* 标题框 */}
                <div ref={refs.titleDiv} className='login_title_div'>
                    <div className='login_leftTop_left' />
                    <div className='login_leftTop_top' />
                    <div className='login_leftBottom_left' />
                    <div className='login_leftBottom_bottom' />
                    <div className='login_rightTop_top' />
                    <div className='login_rightTop_right' />
                    <div className='login_rightBottom_right' />
                    <div className='login_rightBottom_bottom' />

                    <div className='login_title_txt'>
                        {titleString.split('').map((char, index) => (
                            <div key={index} ref={refs.title[index]} className='login_title_word'>
                                {char}
                            </div>
                        ))}
                    </div>
                </div>


                {/* 账号框 */}
                <div ref={refs.accountDiv} className='login_account_div'>
                    <div className='login_account_div_leftTop_Dot' />
                    <div className='login_account_div_rightTop_Dot' />
                    <div className='login_account_div_leftBottom_Dot' />
                    <div className='login_account_div_rightBottom_Dot' />

                    <div ref={refs.accountInput} className='login_account_input_div'>
                        <Input className="login_account-input" variant="borderless" placeholder="请输入账号" value={account} onChange={(e) => setAccount(e.target.value)} />
                    </div>

                    {/* 账号错误提示框 */}
                    <div ref={refs.accountErrDiv} className='accountErrorDiv'>
                        <div ref={refs.accountErrDot1} className='accountErrorDivDot1' />
                        <div ref={refs.accountErrLine1} className='accountErrorDivLine1' />
                        <div ref={refs.accountErrLine2} className='accountErrorDivLine2' />
                        <div ref={refs.accountErrLine3} className='accountErrorDivLine3' />

                        {/* 文本框 */}
                        <div ref={refs.accoountErrPan1} className='account_error_pan_1'>账号不能为空</div>
                        <div ref={refs.accoountErrPan2} className='account_error_pan_2'>ERROR / 错误</div>
                    </div>
                </div>


                {/* 密码框 */}
                <div ref={refs.pwdDiv} className='login_pwd_div'>
                    <div className='login_pwd_div_leftTop_Dot' />
                    <div className='login_pwd_div_rightTop_Dot' />
                    <div className='login_pwd_div_leftBottom_Dot' />
                    <div className='login_pwd_div_rightBottom_Dot' />

                    <div ref={refs.pwdInput} className='login_pwd_input_div'>
                        <Input.Password className="login_pwd_input" variant="borderless" visibilityToggle={false} placeholder="请输入密码" value={pwd} onChange={(e) => setPwd(e.target.value)} />
                    </div>


                    {/* 密码错误提示框 */}
                    <div ref={refs.pwdErrDiv} className='pwdErrorDiv'>
                        <div ref={refs.pwdErrDot1} className='pwdErrorDivDot1' />
                        <div ref={refs.pwdErrLine1} className='pwdErrorDivLine1' />
                        <div ref={refs.pwdErrLine2} className='pwdErrorDivLine2' />
                        <div ref={refs.pwdErrLine3} className='pwdErrorDivLine3' />

                        {/* 文本框 */}
                        <div ref={refs.pwdErrPan1} className='pwd_error_pan_1'>密码不能为空</div>
                        <div ref={refs.pwdErrPan2} className='pwd_error_pan_2'>ERROR / 错误</div>
                    </div>
                </div>


                {/* 登录按钮 */}
                <div ref={refs.loginBtn}
                    className='login_logBtn'
                    onMouseEnter={() => { setIsLoginBtnHovered(true); }}
                    onMouseOut={() => { setIsLoginBtnHovered(false); }}
                    onClick={loginBtnClick}>
                    登 录
                    <CaretRightOutlined ref={refs.loginBtnArrow} className={`login_logBtn_icon ${isLoginBtnHovered ? 'loginBtnArrowShow' : 'loginBtnArrowClose'}`} />

                    {/* icon 橙条 */}
                    <div className='login_loading_div'>
                        <div ref={refs.loadingDiv} className='login_loading_line'></div>
                    </div>

                    {/* 四角 */}
                    <div ref={refs.loginBtnCorner} className={`login_logBtn_corner ${isLoginBtnHovered ? 'loginBtnCornerShow' : 'loginBtnCornerClose'}`}>
                        <div className='login_logBtn_corner_leftTop_left' />
                        <div className='login_logBtn_corner_leftTop_top' />
                        <div className='login_logBtn_corner_leftBottom_left' />
                        <div className='login_logBtn_corner_leftBottom_bottom' />
                        <div className='login_logBtn_corner_rightTop_top' />
                        <div className='login_logBtn_corner_rightTop_right' />
                        <div className='login_logBtn_corner_rightBottom_right' />
                        <div className='login_logBtn_corner_rightBottom_bottom' />
                    </div>
                </div>


                {/* 错误提示窗 */}
                <div ref={refs.errorDiv} className='login_error_div'>
                    <div ref={refs.errDot1} className='login_error_first_dot' />
                    <div ref={refs.errLin1} className='login_error_vLine' />
                    <div ref={refs.errDot2} className='login_error_end_dot' />
                    <div ref={refs.errLin2} className='login_error_hLine' />

                    {/* 文本框 */}
                    <div ref={refs.errPan1} className='login_error_pan_1'>账号或者密码错误</div>
                    {/*<div ref={errPan1} className='login_error_pan_1'>0123456789 LINE DATA STORAGE</div>*/}
                    <div ref={refs.errPan2} className='login_error_pan_2'>FAIL / 失败</div>
                </div>

            </div>

        </div>
    )


}
