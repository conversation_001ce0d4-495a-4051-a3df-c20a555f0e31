import { gsap } from 'gsap';

/**
 * 动画效果工具类
 */
export default class AnimationUtils {
    /**
     * 创建基础闪烁动画
     * @param {HTMLElement|React.RefObject} element - DOM元素或React ref对象
     * @param {Object} options - 动画配置选项
     * @param {Array<number>} [options.opacitySequence=[0.8, 0.3, 1]] - 透明度变化序列
     * @param {Array<number>} [options.durationSequence=[0.15, 0.15, 0.15]] - 各阶段持续时间
     * @param {number} [options.initialDelay=0.1] - 初始延迟时间
     * @param {number} [options.initialOpacity=0] - 初始透明度
     * @param {number} [options.initialScale=1] - 初始缩放值
     * @return {gsap.core.Timeline} GSAP动画时间线对象，可用于控制动画
     */
    static createBlinkEffect(element, options = {}) {
        // 获取真实DOM元素（处理React ref的情况）
        const targetElement = element?.current || element;
        if (!targetElement) return null;

        const {
            opacitySequence = [0.8, 0.3, 1],
            durationSequence = [0.15, 0.15, 0.15],
            initialDelay = 0.1,
            initialOpacity = 0,
            initialScale = 1
        } = options;

        // 初始化元素状态
        gsap.set(targetElement, { opacity: initialOpacity, scale: initialScale });

        // 创建动画时间线
        const tl = gsap.timeline();

        // 添加动画序列
        let currentTime = 0;
        tl.to(targetElement, { opacity: opacitySequence[0], duration: durationSequence[0], delay: initialDelay });
        currentTime += durationSequence[0] + initialDelay;

        // 添加剩余的动画序列
        for (let i = 1; i < opacitySequence.length; i++) {
            tl.to(targetElement, { opacity: opacitySequence[i], duration: durationSequence[i] }, currentTime);
            currentTime += durationSequence[i];
        }

        return tl;
    }

    /**
     * 创建自定义序列闪烁效果（保持向后兼容性）
     * @param {HTMLElement|React.RefObject} element - DOM元素或React ref对象
     * @param {Array<Object>} sequence - 动画序列配置，每个对象包含opacity和duration
     * @param {Object} options - 其他配置选项
     * @return {gsap.core.Timeline} GSAP动画时间线对象
     */
    static createCustomBlinkEffect(element, sequence, options = {}) {
        const targetElement = element?.current || element;
        if (!targetElement) return null;

        const {
            initialDelay = 0.1,
            initialOpacity = 0,
            initialScale = 1,
        } = options;

        // 初始化元素状态
        gsap.set(targetElement, { opacity: initialOpacity, scale: initialScale });

        // 创建动画时间线
        const tl = gsap.timeline();

        // 第一个动画带延迟
        if (sequence.length > 0) {
            tl.to(targetElement, {
                ...sequence[0],
                delay: initialDelay
            });
        }

        // 添加剩余的动画序列
        for (let i = 1; i < sequence.length; i++) {
            tl.to(targetElement, sequence[i]);
        }

        return tl;
    }

    /**
     * 创建高级频闪效果 - 统一的频闪工具方法
     * 基于GSAP timeline和多个to动画实现，参考DevicesList中的高质量频闪效果
     * @param {HTMLElement|React.RefObject} element - DOM元素或React ref对象
     * @param {Object} options - 动画配置选项
     * @param {number} [options.duration=0.8] - 总闪烁时长（秒）
     * @param {number} [options.delay=0] - 延迟时间（秒）
     * @param {string} [options.mode='opacity'] - 闪烁模式：'opacity', 'brightness', 'autoAlpha', 'boxShadow'
     * @param {number} [options.flashCount=10] - 闪烁次数
     * @param {number} [options.flashDuration=0.03] - 单次闪烁持续时间
     * @param {number} [options.minOpacity=0.6] - 最小透明度（opacity/autoAlpha模式）
     * @param {number} [options.maxOpacity=1] - 最大透明度（opacity/autoAlpha模式）
     * @param {number} [options.minBrightness=1] - 最小亮度（brightness模式）
     * @param {number} [options.maxBrightness=1.5] - 最大亮度（brightness模式）
     * @param {string} [options.shadowColor='#23C76D'] - 发光颜色（boxShadow模式）
     * @param {string} [options.shadowSize='0 0 8px'] - 发光大小（boxShadow模式）
     * @param {number} [options.shadowOpacity=0.8] - 发光透明度（boxShadow模式）
     * @return {gsap.core.Timeline} GSAP动画时间线对象
     */
    static createFlashEffect(element, options = {}) {
        const targetElement = element?.current || element;
        if (!targetElement) return null;

        const {
            duration = 0.8,
            delay = 0,
            mode = 'opacity',
            flashCount = 10,
            flashDuration = 0.03,
            minOpacity = 0.6,
            maxOpacity = 1.0,
            minBrightness = 1.0,
            maxBrightness = 1.5,
            shadowColor = '#23C76D',
            shadowSize = '0 0 8px',
            shadowOpacity = 0.8
        } = options;

        // 参数验证
        if (flashCount <= 0 || flashDuration <= 0) {
            console.warn('AnimationUtils: flashCount和flashDuration必须大于0');
            return null;
        }
        
        // 验证支持的模式
        const supportedModes = ['opacity', 'autoAlpha', 'brightness', 'boxShadow'];
        if (!supportedModes.includes(mode)) {
            console.warn(`AnimationUtils: 不支持的频闪模式: ${mode}。支持的模式: ${supportedModes.join(', ')}`);
            return null;
        }

        // 创建主时间线
        const tl = gsap.timeline({ delay });

        switch (mode) {
            case 'opacity':
                // 透明度频闪 - 使用多个to动画
                for (let i = 0; i < flashCount; i++) {
                    tl.to(targetElement, { 
                        opacity: minOpacity, 
                        duration: flashDuration,
                        ease: "none"
                    })
                    .to(targetElement, { 
                        opacity: maxOpacity, 
                        duration: flashDuration,
                        ease: "none"
                    });
                }
                // 确保最终状态为完全显示
                tl.set(targetElement, { opacity: 1 });
                break;

            case 'autoAlpha':
                // 自动透明度频闪 - 使用多个to动画
                for (let i = 0; i < flashCount; i++) {
                    tl.to(targetElement, { 
                        autoAlpha: minOpacity, 
                        duration: flashDuration,
                        ease: "none"
                    })
                    .to(targetElement, { 
                        autoAlpha: maxOpacity, 
                        duration: flashDuration,
                        ease: "none"
                    });
                }
                // 确保最终状态为完全显示
                tl.set(targetElement, { autoAlpha: 1 });
                break;

            case 'brightness':
                // 亮度频闪 - 使用多个to动画，缓存filter字符串
                const brightnessHigh = `brightness(${maxBrightness})`;
                const brightnessLow = `brightness(${minBrightness})`;
                
                for (let i = 0; i < flashCount; i++) {
                    tl.to(targetElement, { 
                        filter: brightnessHigh, 
                        duration: flashDuration,
                        ease: "none"
                    })
                    .to(targetElement, { 
                        filter: brightnessLow, 
                        duration: flashDuration,
                        ease: "none"
                    });
                }
                // 确保最终状态为正常亮度
                tl.set(targetElement, { filter: "brightness(1)" });
                break;

            case 'boxShadow':
                // 阴影发光频闪 - 使用多个to动画，优化RGB转换
                const rgbColor = this.hexToRgb(shadowColor); // 只调用一次hexToRgb
                const shadowOn = `${shadowSize} rgba(${rgbColor}, ${shadowOpacity})`;
                const shadowOff = `${shadowSize} rgba(${rgbColor}, 0)`;

                // 初始状态：无发光
                tl.set(targetElement, { boxShadow: shadowOff });
                
                // 连续闪烁
                for (let i = 0; i < flashCount; i++) {
                    tl.to(targetElement, { 
                        boxShadow: shadowOn, 
                        duration: flashDuration,
                        ease: "none"
                    })
                    .to(targetElement, { 
                        boxShadow: shadowOff, 
                        duration: flashDuration,
                        ease: "none"
                    });
                }
                // 确保最终状态为无发光
                tl.set(targetElement, { boxShadow: "none" });
                break;

            default:
                console.warn(`不支持的频闪模式: ${mode}`);
                return null;
        }

        return tl;
    }

    /**
     * 创建组合频闪效果 - 同时支持多种闪烁模式
     * @param {HTMLElement|React.RefObject} element - DOM元素或React ref对象
     * @param {Object} options - 动画配置选项
     * @param {number} [options.duration=0.8] - 总闪烁时长（秒）
     * @param {number} [options.delay=0] - 延迟时间（秒）
     * @param {Array<string>} [options.modes=['opacity']] - 闪烁模式数组
     * @param {Object} [options.modeConfig={}] - 各模式的具体配置
     * @return {gsap.core.Timeline} GSAP动画时间线对象
     */
    static createCombinedFlashEffect(element, options = {}) {
        const targetElement = element?.current || element;
        if (!targetElement) return null;

        const {
            duration = 0.8,
            delay = 0,
            modes = ['opacity'],
            modeConfig = {}
        } = options;

        // 创建主时间线
        const masterTl = gsap.timeline({ delay });

        // 为每个模式创建独立的闪烁动画，并同时执行
        modes.forEach((mode, index) => {
            const flashTl = this.createFlashEffect(element, {
                duration,
                delay: 0, // 在主时间线中已经设置了delay
                mode,
                ...modeConfig[mode]
            });

            if (flashTl) {
                masterTl.add(flashTl, 0); // 所有效果同时开始
            }
        });

        return masterTl;
    }

    /**
     * 创建按钮缩放动画效果
     * @param {HTMLElement|React.RefObject} element - DOM元素或React ref对象
     * @param {Object} options - 动画配置选项
     * @param {number} [options.pressedScale=0.9] - 按下时的缩放比例
     * @param {number} [options.normalScale=1] - 正常状态缩放比例
     * @param {number} [options.duration=0.1] - 动画持续时间
     * @return {Object} 包含mouseDown和mouseUp/mouseLeave事件处理函数
     */
    static createButtonScaleEffect(element, options = {}) {
        const {
            pressedScale = 0.9,
            normalScale = 1,
            duration = 0.1
        } = options;

        return {
            handleMouseDown: () => {
                const targetElement = element?.current || element;
                if (targetElement) {
                    gsap.to(targetElement, { scale: pressedScale, duration });
                }
            },
            handleMouseUpOrLeave: () => {
                const targetElement = element?.current || element;
                if (targetElement) {
                    gsap.to(targetElement, { scale: normalScale, duration });
                }
            }
        };
    }

    // 颜色转换缓存，避免重复计算
    static _colorCache = new Map();

    /**
     * 辅助方法：将十六进制颜色转换为RGB值（带缓存优化）
     * @param {string} hex - 十六进制颜色值（如 #23C76D）
     * @return {string} RGB值字符串（如 35, 199, 109）
     */
    static hexToRgb(hex) {
        // 检查缓存
        if (this._colorCache.has(hex)) {
            return this._colorCache.get(hex);
        }

        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        const rgbString = result ? 
            `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}` : 
            '0, 0, 0';
        
        // 存入缓存
        this._colorCache.set(hex, rgbString);
        return rgbString;
    }
} 