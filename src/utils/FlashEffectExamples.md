# 频闪效果工具类使用指南

## 概述

新扩展的 `AnimationUtils` 类提供了三个强大的频闪效果方法：

1. `createRapidFlashEffect` - 单一模式急促频闪
2. `createCombinedFlashEffect` - 组合模式频闪 
3. 原有的 `createBlinkEffect` - 基础闪烁效果

## 1. 基础急促频闪效果

### 透明度闪烁（默认）
```javascript
import AnimationUtils from '../utils/animations.js';

// React组件中使用
const MyComponent = () => {
    const elementRef = useRef(null);
    
    useEffect(() => {
        // 基础透明度闪烁，延迟1.5秒后开始
        AnimationUtils.createRapidFlashEffect(elementRef, {
            duration: 0.8,  // 闪烁0.8秒
            delay: 1.5,     // 延迟1.5秒
            mode: 'opacity' // 透明度模式
        });
    }, []);
    
    return <div ref={elementRef}>要闪烁的元素</div>;
};
```

### 亮度闪烁
```javascript
// 按钮点击时闪烁
const handleButtonClick = () => {
    AnimationUtils.createRapidFlashEffect(buttonRef, {
        duration: 0.6,
        delay: 0,
        mode: 'brightness',
        minBrightness: 1,
        maxBrightness: 1.5
    });
};
```

### 发光闪烁
```javascript
// 绿色发光边框闪烁
AnimationUtils.createRapidFlashEffect(lineRef, {
    duration: 0.8,
    delay: 2.0,
    mode: 'boxShadow',
    shadowColor: '#23C76D',
    shadowSize: '0 0 8px'
});
```

## 2. 组合频闪效果

```javascript
// 同时进行透明度和发光闪烁
AnimationUtils.createCombinedFlashEffect(elementRef, {
    duration: 0.8,
    delay: 1.5,
    modes: ['opacity', 'boxShadow'],
    modeConfig: {
        opacity: {
            minOpacity: 0.6,
            maxOpacity: 1
        },
        boxShadow: {
            shadowColor: '#23C76D',
            shadowSize: '0 0 6px'
        }
    }
});
```

## 3. 完整参数配置

```javascript
const flashTimeline = AnimationUtils.createRapidFlashEffect(element, {
    // 基础配置
    duration: 0.8,          // 总闪烁时长（秒）
    delay: 1.5,            // 延迟时间（秒）
    mode: 'opacity',       // 闪烁模式：'opacity', 'brightness', 'boxShadow'
    
    // 频率配置
    flashInterval: 0.06,   // 单次闪烁间隔（秒）- 值越小闪烁越急促
    
    // 透明度模式配置
    minOpacity: 0.6,       // 最小透明度
    maxOpacity: 1,         // 最大透明度
    
    // 亮度模式配置
    minBrightness: 1,      // 最小亮度
    maxBrightness: 1.5,    // 最大亮度
    
    // 发光模式配置
    shadowColor: '#23C76D', // 发光颜色
    shadowSize: '0 0 8px'   // 发光大小
});

// 可以控制动画播放
flashTimeline.play();   // 播放
flashTimeline.pause();  // 暂停
flashTimeline.kill();   // 停止并清理
```

## 4. 实际应用场景

### 流量计开场动画（参考实现）
```javascript
const Flowmeter = () => {
    const topLineRef = useRef(null);
    const titleRef = useRef(null);
    const bottomLineRef = useRef(null);
    
    useEffect(() => {
        // 生成随机延迟（1.5-2.5秒）
        const getRandomDelay = () => Math.random() * 1000 + 1500;
        
        // 顶部线条：发光闪烁
        AnimationUtils.createRapidFlashEffect(topLineRef, {
            duration: 0.8,
            delay: getRandomDelay() / 1000, // 转换为秒
            mode: 'boxShadow',
            shadowColor: '#23C76D'
        });
        
        // 标题：亮度闪烁
        AnimationUtils.createRapidFlashEffect(titleRef, {
            duration: 0.6,
            delay: getRandomDelay() / 1000,
            mode: 'brightness'
        });
        
        // 底部线条：组合效果
        AnimationUtils.createCombinedFlashEffect(bottomLineRef, {
            duration: 0.8,
            delay: getRandomDelay() / 1000,
            modes: ['opacity', 'boxShadow'],
            modeConfig: {
                boxShadow: {
                    shadowColor: '#23C76D',
                    shadowSize: '0 0 6px'
                }
            }
        });
    }, []);
    
    return (
        <div>
            <div ref={topLineRef} className="top-line" />
            <div ref={titleRef} className="title">流量计</div>
            <div ref={bottomLineRef} className="bottom-line" />
        </div>
    );
};
```

### 设备状态指示器
```javascript
const DeviceStatus = ({ status }) => {
    const statusRef = useRef(null);
    
    // 根据设备状态闪烁不同颜色
    useEffect(() => {
        if (status === 'alarm') {
            AnimationUtils.createRapidFlashEffect(statusRef, {
                duration: 2.0,  // 持续闪烁2秒
                delay: 0,
                mode: 'boxShadow',
                shadowColor: '#E39D25', // 警告黄色
                flashInterval: 0.1      // 较慢的闪烁
            });
        }
    }, [status]);
    
    return <div ref={statusRef} className="status-indicator" />;
};
```

## 5. 注意事项

1. **性能考虑**：避免同时对大量元素进行频闪动画
2. **用户体验**：闪烁频率不宜过快，避免引起视觉疲劳
3. **内存管理**：组件销毁时记得调用 `timeline.kill()` 清理动画
4. **浏览器兼容性**：依赖GSAP库，确保项目中已正确引入

## 6. 高级用法

### 链式动画
```javascript
const tl = gsap.timeline();

// 先扩散，再闪烁
tl.to(element, { scaleX: 1, duration: 0.8 })
  .add(AnimationUtils.createRapidFlashEffect(element, {
      duration: 0.8,
      delay: 0,
      mode: 'opacity'
  }), "-=0.8"); // 与扩散动画同时开始
```

### 随机化闪烁效果
```javascript
// 为多个元素创建随机闪烁
const elements = [ref1, ref2, ref3];
elements.forEach((ref, index) => {
    const randomDelay = Math.random() * 2 + 1; // 1-3秒随机延迟
    const randomDuration = Math.random() * 0.4 + 0.6; // 0.6-1秒随机时长
    
    AnimationUtils.createRapidFlashEffect(ref, {
        duration: randomDuration,
        delay: randomDelay,
        mode: 'opacity'
    });
});
```

这个工具类让你可以轻松地在整个项目中实现统一、高质量的频闪效果！🎉 