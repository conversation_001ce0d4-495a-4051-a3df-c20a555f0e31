{"name": "qzsw", "private": true, "version": "1.0.0", "author": "<PERSON>", "type": "module", "scripts": {"dev": "vite", "dev:perf": "vite --mode development --profile", "build": "vite build", "build:analyze": "vite build --mode analyze", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "perf:audit": "node check_syntax.js && lighthouse http://localhost:4173 --output=html --output-path=./performance-report.html"}, "dependencies": {"@amap/amap-react": "^0.1.5", "@gsap/react": "^2.1.2", "@number-flow/react": "^0.5.9", "@pixi/react": "^8.0.2", "@react-buddy/ide-toolbox": "^2.4.0", "@react-buddy/palette-antd": "^5.3.1", "@tailwindcss/vite": "^4.1.7", "animejs": "^4.0.2", "antd": "^5.25.2", "axios": "^1.9.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "gsap": "^3.13.0", "howler": "^2.2.4", "konva": "^9.3.20", "lodash": "^4.17.21", "pixi.js": "^8.10.1", "react": "^19.1.0", "react-countup": "^6.5.3", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.1.0", "react-grid-layout": "^1.5.1", "react-konva": "^19.0.5", "react-router-dom": "^7.6.0", "tailwindcss": "^4.1.7", "three": "^0.176.0"}, "devDependencies": {"@types/react": "^19.1.4", "@types/react-dom": "^19.1.5", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.27.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "vite": "^6.3.5", "vite-plugin-svgr": "^4.3.0"}}