import {defineConfig} from 'vite'
import react from '@vitejs/plugin-react'
import tailwindcss from '@tailwindcss/vite'
import svgr from 'vite-plugin-svgr'

// https://vitejs.dev/config/
export default defineConfig({
    plugins: [
        react(), 
        tailwindcss(),
        svgr({
            // svgr 选项配置
            svgrOptions: {
                // 保留 SVG 元素的宽高比例
                // icon: true, // 该选项可能对复杂SVG不友好，故移除
                // SVG 转换为 React 组件的其他选项可以在这里添加
                svgoConfig: {
                    plugins: [
                        {
                            name: 'prefixIds',
                        },
                    ],
                },
            },
            // 将 SVG 文件作为 React 组件导入
            exportAsDefault: true,
        })
    ],

    server: {
        proxy: {
            "/api": {
                target: "http://dev-water-platform.gzjhlgs.cn",
                // target: "http://222.85.181.29:9091",
                changeOrigin: true,
                rewrite: (path) => path.replace(/^\/api/, ""),
            },
        },
    },

})
