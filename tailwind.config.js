/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,jsx}",
  ],
  theme: {
    extend: {
      animation: {
        'fadeInUp': 'fadeInUp 0.4s ease-out forwards',
        'titleBackground': 'titleBackground 0.4s ease-in-out forwards',
        'menuLine': 'menuLine 0.6s ease-out forwards',
        'typingBlink': 'typingBlink 0.15s ease-in-out infinite',
        // 🚀 GPU加速动画
        'gpu-spin': 'gpuSpin 1s linear infinite',
        'gpu-bounce': 'gpuBounce 1s ease-in-out infinite',
        'gpu-pulse': 'gpuPulse 2s ease-in-out infinite',
        'gpu-slide-in': 'gpuSlideIn 0.3s ease-out forwards',
      },
      keyframes: {
        fadeInUp: {
          '0%': { 
            opacity: '0',
            transform: 'translate3d(0, 5px, 0)'
          },
          '100%': { 
            opacity: '1',
            transform: 'translate3d(0, 0, 0)'
          }
        },
        // 🚀 GPU加速关键帧动画
        gpuSpin: {
          '0%': { transform: 'rotate3d(0, 0, 1, 0deg)' },
          '100%': { transform: 'rotate3d(0, 0, 1, 360deg)' }
        },
        gpuBounce: {
          '0%, 100%': { transform: 'translate3d(0, 0, 0) scale3d(1, 1, 1)' },
          '50%': { transform: 'translate3d(0, -25%, 0) scale3d(1.05, 1.05, 1)' }
        },
        gpuPulse: {
          '0%, 100%': { transform: 'scale3d(1, 1, 1)', opacity: '1' },
          '50%': { transform: 'scale3d(1.05, 1.05, 1)', opacity: '0.8' }
        },
        gpuSlideIn: {
          '0%': { 
            transform: 'translate3d(100%, 0, 0)',
            opacity: '0'
          },
          '100%': { 
            transform: 'translate3d(0, 0, 0)',
            opacity: '1'
          }
        },
        titleBackground: {
          '0%': { opacity: '0' },
          '15%': { opacity: '0.8' },
          '30%': { opacity: '0.2' },
          '45%': { opacity: '0.7' },
          '60%': { opacity: '0.1' },
          '75%': { opacity: '1' },
          '90%': { opacity: '0.2' },
          '100%': { opacity: '1' }
        },
        menuLine: {
          '0%': { 
            width: '0px',
            opacity: '0',
            left: '50%',
            right: '50%',
            margin: '0'
          },
          '20%': { 
            width: 'auto',
            opacity: '0.8',
            left: '0px',
            right: '0px',
            margin: '0'
          },
          '35%': { opacity: '0.2' },
          '50%': { opacity: '1' },
          '65%': { opacity: '0.3' },
          '80%': { opacity: '0.9' },
          '95%': { opacity: '0.4' },
          '100%': { 
            width: 'auto',
            opacity: '1',
            left: '0px',
            right: '0px',
            margin: '0'
          }
        },
        typingBlink: {
          '0%': { opacity: '1' },
          '50%': { opacity: '0.7' },
          '100%': { opacity: '1' }
        }
      }
    },
  },
  plugins: [],
}